{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Cf0kDHlZzLNPnFGxBcnYE", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4dpbFrnrrlpAYLCchUjI2b6UGa3BRUjnvYrIGIizNps=", "__NEXT_PREVIEW_MODE_ID": "9232476c906c4c58124951f7a9e2945b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1ad49556c839f2b247fe3af36f319f3675815c12256e5e7bf1148bc76127e4be", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f6f80401ded2d3baee66cb3e40e53aa67979801b6644360efede1e1f9098b944"}}}, "functions": {}, "sortedMiddleware": ["/"]}