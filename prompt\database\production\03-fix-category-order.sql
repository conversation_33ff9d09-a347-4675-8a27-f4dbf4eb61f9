-- =====================================================
-- 修复分类排序功能
-- =====================================================
-- 版本: 1.0.1
-- 创建时间: 2025-07-26
-- 描述: 修复分类拖拽排序的权限问题
-- =====================================================

-- 创建批量更新分类排序的函数
CREATE OR REPLACE FUNCTION update_categories_order(
    category_ids UUID[],
    user_uuid UUID DEFAULT auth.uid()
)
RETURNS VOID AS $$
DECLARE
    category_id UUID;
    sort_index INTEGER := 1;
BEGIN
    -- 检查用户权限
    IF user_uuid IS NULL THEN
        RAISE EXCEPTION '用户未登录';
    END IF;
    
    -- 逐个更新分类排序
    FOREACH category_id IN ARRAY category_ids
    LOOP
        UPDATE categories 
        SET 
            sort_order = sort_index,
            updated_at = NOW()
        WHERE 
            id = category_id 
            AND user_id = user_uuid
            AND deleted_at IS NULL;
        
        -- 检查是否更新成功
        IF NOT FOUND THEN
            RAISE EXCEPTION '分类不存在或无权限更新: %', category_id;
        END IF;
        
        sort_index := sort_index + 1;
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 授予执行权限
GRANT EXECUTE ON FUNCTION update_categories_order(UUID[], UUID) TO authenticated;

-- 创建更安全的 RLS 策略（如果需要）
-- 确保 UPDATE 策略允许更新 sort_order 字段
DROP POLICY IF EXISTS "Users can update their own categories" ON categories;
CREATE POLICY "Users can update their own categories" ON categories
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- 添加索引优化排序查询
CREATE INDEX IF NOT EXISTS idx_categories_user_sort 
ON categories(user_id, sort_order) 
WHERE deleted_at IS NULL;

-- 验证函数创建成功
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_proc 
        WHERE proname = 'update_categories_order'
    ) THEN
        RAISE NOTICE '✅ 分类排序函数创建成功';
    ELSE
        RAISE EXCEPTION '❌ 分类排序函数创建失败';
    END IF;
END $$;
