-- 提示词管理工具数据库架构 (修复版)
-- 创建时间: 2025-07-25
-- 描述: 支持提示词管理、分类、标签、搜索等功能的完整数据库结构
-- 修复: 移除中文全文搜索配置，使用英文配置以确保兼容性

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. 分类表
CREATE TABLE categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    color VARCHAR(7) DEFAULT '#6366f1', -- 十六进制颜色代码
    icon VARCHAR(50) DEFAULT 'folder', -- Font Awesome图标名称
    sort_order INTEGER DEFAULT 0,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT categories_name_user_unique UNIQUE(name, user_id)
);

-- 2. 标签表
CREATE TABLE tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    color VARCHAR(7) DEFAULT '#10b981', -- 十六进制颜色代码
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT tags_name_user_unique UNIQUE(name, user_id)
);

-- 3. 提示词表
CREATE TABLE prompts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description VARCHAR(500),
    content TEXT NOT NULL,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    usage_count INTEGER DEFAULT 0 CHECK (usage_count >= 0),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    metadata JSONB DEFAULT '{}', -- 扩展字段，存储额外信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- 4. 提示词标签关联表
CREATE TABLE prompt_tags (
    prompt_id UUID REFERENCES prompts(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    PRIMARY KEY (prompt_id, tag_id)
);

-- 5. 搜索历史表
CREATE TABLE search_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    search_term VARCHAR(200) NOT NULL,
    search_count INTEGER DEFAULT 1,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    last_searched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT search_history_term_user_unique UNIQUE(search_term, user_id)
);

-- 6. 用户偏好设置表
CREATE TABLE user_preferences (
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    theme VARCHAR(20) DEFAULT 'system', -- 'light', 'dark', 'system'
    default_category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    items_per_page INTEGER DEFAULT 12 CHECK (items_per_page > 0),
    show_usage_count BOOLEAN DEFAULT true,
    auto_copy_feedback BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
-- 分类表索引
CREATE INDEX idx_categories_user_id ON categories(user_id);
CREATE INDEX idx_categories_sort_order ON categories(sort_order);
CREATE INDEX idx_categories_deleted_at ON categories(deleted_at);

-- 标签表索引
CREATE INDEX idx_tags_user_id ON tags(user_id);
CREATE INDEX idx_tags_name ON tags(name);

-- 提示词表索引
CREATE INDEX idx_prompts_user_id ON prompts(user_id);
CREATE INDEX idx_prompts_category_id ON prompts(category_id);
CREATE INDEX idx_prompts_created_at ON prompts(created_at DESC);
CREATE INDEX idx_prompts_updated_at ON prompts(updated_at DESC);
CREATE INDEX idx_prompts_usage_count ON prompts(usage_count DESC);
CREATE INDEX idx_prompts_deleted_at ON prompts(deleted_at);

-- 全文搜索索引（使用英文配置，兼容性更好）
CREATE INDEX idx_prompts_title_search ON prompts USING GIN(to_tsvector('english', title));
CREATE INDEX idx_prompts_content_search ON prompts USING GIN(to_tsvector('english', content));
CREATE INDEX idx_prompts_description_search ON prompts USING GIN(to_tsvector('english', description));

-- 关联表索引
CREATE INDEX idx_prompt_tags_prompt_id ON prompt_tags(prompt_id);
CREATE INDEX idx_prompt_tags_tag_id ON prompt_tags(tag_id);

-- 搜索历史索引
CREATE INDEX idx_search_history_user_id ON search_history(user_id);
CREATE INDEX idx_search_history_last_searched ON search_history(last_searched_at DESC);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_prompts_updated_at BEFORE UPDATE ON prompts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全策略 (RLS)
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompts ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompt_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
-- 分类表策略
CREATE POLICY "用户只能访问自己的分类" ON categories
    FOR ALL USING (auth.uid() = user_id);

-- 标签表策略
CREATE POLICY "用户只能访问自己的标签" ON tags
    FOR ALL USING (auth.uid() = user_id);

-- 提示词表策略
CREATE POLICY "用户只能访问自己的提示词" ON prompts
    FOR ALL USING (auth.uid() = user_id);

-- 提示词标签关联表策略
CREATE POLICY "用户只能访问自己的提示词标签关联" ON prompt_tags
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM prompts
            WHERE prompts.id = prompt_tags.prompt_id
            AND prompts.user_id = auth.uid()
        )
    );

-- 搜索历史表策略
CREATE POLICY "用户只能访问自己的搜索历史" ON search_history
    FOR ALL USING (auth.uid() = user_id);

-- 用户偏好表策略
CREATE POLICY "用户只能访问自己的偏好设置" ON user_preferences
    FOR ALL USING (auth.uid() = user_id);
