(()=>{var a={};a.id=38,a.ids=[38],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9725:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Cursor Project\\\\prompy augment\\\\prompt\\\\app\\\\dashboard\\\\categories\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\categories\\page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},14744:(a,b,c)=>{"use strict";c.d(b,{K:()=>r});var d=c(60687),e=c(43210),f=c(37826),g=c(24934),h=c(68988),i=c(15616),j=c(39390),k=c(32418),l=c(71702),m=c(34257),n=c(22392),o=c(44655);let p=[{name:"folder",label:"文件夹"},{name:"code",label:"代码"},{name:"pen-to-square",label:"写作"},{name:"bullhorn",label:"营销"},{name:"rocket",label:"效率"},{name:"graduation-cap",label:"学习"},{name:"lightbulb",label:"创意"},{name:"cog",label:"工具"},{name:"heart",label:"收藏"},{name:"star",label:"重要"},{name:"fire",label:"热门"},{name:"gem",label:"精选"},{name:"bullseye",label:"目标"},{name:"flag",label:"标记"},{name:"bookmark",label:"书签"},{name:"database",label:"数据"},{name:"cloud",label:"云端"},{name:"mobile",label:"移动"},{name:"desktop",label:"桌面"},{name:"palette",label:"设计"}],q=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9","#3b82f6","#6366f1","#8b5cf6","#a855f7","#d946ef","#ec4899"];function r({category:a,isOpen:b,onClose:c,onSuccess:r}){let[s,t]=(0,e.useState)(""),[u,v]=(0,e.useState)(""),[w,x]=(0,e.useState)("#6366f1"),[y,z]=(0,e.useState)("folder"),[A,B]=(0,e.useState)(""),[C,D]=(0,e.useState)(!1),[E,F]=(0,e.useState)(""),{toast:G}=(0,l.dj)(),H=!!a,I=async b=>{if(!b.trim())return F("分类名称不能为空"),!1;if(b.length>50)return F("分类名称不能超过50个字符"),!1;try{if(await (0,m.c1)(b.trim(),H?a?.id:void 0))return F("分类名称已存在"),!1}catch(a){console.error("检查分类名称失败:",a)}return F(""),!0},J=async b=>{if(b.preventDefault(),!await I(s))return;let d=A&&(0,o.o1)(A)?A:w;try{D(!0);let b={name:s.trim(),description:u.trim()||void 0,color:d,icon:y};if(H&&a)if(console.log("\uD83D\uDE80 使用本地优先更新分类"),await n.J.updateCategory(a.id,b))G({title:"更新成功",description:"分类已成功更新"});else throw Error("更新失败");else{console.log("\uD83D\uDE80 使用本地优先创建分类");let a=await n.J.createCategory(b);if(a)G({title:"创建成功",description:"分类已成功创建"}),console.log("✅ 分类创建成功:",a);else throw Error("创建失败")}r(),c(),t(""),v(""),x("#6366f1"),z("folder"),B(""),F("")}catch(a){console.error("保存分类失败:",a),G({title:"保存失败",description:H?"更新分类时出现错误":"创建分类时出现错误",variant:"destructive"})}finally{D(!1)}};return(0,d.jsx)(f.lG,{open:b,onOpenChange:c,children:(0,d.jsxs)(f.Cf,{className:"max-w-md",children:[(0,d.jsxs)(f.c7,{children:[(0,d.jsx)(f.L3,{children:H?"编辑分类":"创建新分类"}),(0,d.jsx)(f.rr,{children:H?"修改分类的信息和外观":"创建一个新的提示词分类"})]}),(0,d.jsxs)("form",{onSubmit:J,className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{htmlFor:"name",children:"分类名称 *"}),(0,d.jsx)(h.p,{id:"name",value:s,onChange:a=>{t(a.target.value),F("")},onBlur:()=>I(s),placeholder:"输入分类名称",className:E?"border-red-500":"",required:!0}),E&&(0,d.jsx)("p",{className:"text-sm text-red-600",children:E})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{htmlFor:"description",children:"描述"}),(0,d.jsx)(i.T,{id:"description",value:u,onChange:a=>v(a.target.value),placeholder:"输入分类描述（可选）",className:"min-h-[80px]"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{children:"图标"}),(0,d.jsx)("div",{className:"grid grid-cols-5 gap-2",children:p.map(a=>(0,d.jsx)("button",{type:"button",className:`
                    flex items-center justify-center w-10 h-10 rounded-lg border-2 transition-colors
                    ${y===a.name?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}
                  `,onClick:()=>z(a.name),title:a.label,children:(0,d.jsx)(k.Icon,{name:a.name,className:"h-5 w-5"})},a.name))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{children:"颜色"}),(0,d.jsx)("div",{className:"grid grid-cols-8 gap-2",children:q.map(a=>(0,d.jsx)("button",{type:"button",className:`
                    w-8 h-8 rounded-lg border-2 transition-all
                    ${w===a?"border-gray-400 scale-110":"border-gray-200 hover:scale-105"}
                  `,style:{backgroundColor:a},onClick:()=>{x(a),B("")}},a))}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(h.p,{type:"text",value:A,onChange:a=>{var b;B(b=a.target.value),(0,o.o1)(b)&&x(b)},placeholder:"#6366f1",className:"flex-1"}),(0,d.jsx)("div",{className:"w-8 h-8 rounded border border-gray-200",style:{backgroundColor:w}})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{children:"预览"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 p-3 border rounded-lg bg-gray-50",children:[(0,d.jsx)(k.Icon,{name:y,className:"h-5 w-5",color:w}),(0,d.jsx)("span",{className:"font-medium",children:s||"分类名称"}),u&&(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["- ",u]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-4",children:[(0,d.jsx)(g.$,{type:"button",variant:"outline",onClick:c,children:"取消"}),(0,d.jsx)(g.$,{type:"submit",disabled:C||!!E,children:C?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),H?"更新中...":"创建中..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.Icon,{name:"save",className:"h-4 w-4 mr-2"}),H?"更新":"创建"]})})]})]})]})})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25020:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>bD});var d,e,f,g,h,i,j,k,l,m,n=c(60687),o=c(43210),p=c.n(o),q=c(51215);let r="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function s(a){let b=Object.prototype.toString.call(a);return"[object Window]"===b||"[object global]"===b}function t(a){return"nodeType"in a}function u(a){var b,c;return a?s(a)?a:t(a)&&null!=(b=null==(c=a.ownerDocument)?void 0:c.defaultView)?b:window:window}function v(a){let{Document:b}=u(a);return a instanceof b}function w(a){return!s(a)&&a instanceof u(a).HTMLElement}function x(a){return a instanceof u(a).SVGElement}function y(a){return a?s(a)?a.document:t(a)?v(a)?a:w(a)||x(a)?a.ownerDocument:document:document:document}let z=r?o.useLayoutEffect:o.useEffect;function A(a){let b=(0,o.useRef)(a);return z(()=>{b.current=a}),(0,o.useCallback)(function(){for(var a=arguments.length,c=Array(a),d=0;d<a;d++)c[d]=arguments[d];return null==b.current?void 0:b.current(...c)},[])}function B(a,b){void 0===b&&(b=[a]);let c=(0,o.useRef)(a);return z(()=>{c.current!==a&&(c.current=a)},b),c}function C(a,b){let c=(0,o.useRef)();return(0,o.useMemo)(()=>{let b=a(c.current);return c.current=b,b},[...b])}function D(a){let b=A(a),c=(0,o.useRef)(null),d=(0,o.useCallback)(a=>{a!==c.current&&(null==b||b(a,c.current)),c.current=a},[]);return[c,d]}function E(a){let b=(0,o.useRef)();return(0,o.useEffect)(()=>{b.current=a},[a]),b.current}let F={};function G(a,b){return(0,o.useMemo)(()=>{if(b)return b;let c=null==F[a]?0:F[a]+1;return F[a]=c,a+"-"+c},[a,b])}function H(a){return function(b){for(var c=arguments.length,d=Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];return d.reduce((b,c)=>{for(let[d,e]of Object.entries(c)){let c=b[d];null!=c&&(b[d]=c+a*e)}return b},{...b})}}let I=H(1),J=H(-1);function K(a){if(!a)return!1;let{KeyboardEvent:b}=u(a.target);return b&&a instanceof b}function L(a){if(function(a){if(!a)return!1;let{TouchEvent:b}=u(a.target);return b&&a instanceof b}(a)){if(a.touches&&a.touches.length){let{clientX:b,clientY:c}=a.touches[0];return{x:b,y:c}}else if(a.changedTouches&&a.changedTouches.length){let{clientX:b,clientY:c}=a.changedTouches[0];return{x:b,y:c}}}return"clientX"in a&&"clientY"in a?{x:a.clientX,y:a.clientY}:null}let M=Object.freeze({Translate:{toString(a){if(!a)return;let{x:b,y:c}=a;return"translate3d("+(b?Math.round(b):0)+"px, "+(c?Math.round(c):0)+"px, 0)"}},Scale:{toString(a){if(!a)return;let{scaleX:b,scaleY:c}=a;return"scaleX("+b+") scaleY("+c+")"}},Transform:{toString(a){if(a)return[M.Translate.toString(a),M.Scale.toString(a)].join(" ")}},Transition:{toString(a){let{property:b,duration:c,easing:d}=a;return b+" "+c+"ms "+d}}}),N="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",O={display:"none"};function P(a){let{id:b,value:c}=a;return p().createElement("div",{id:b,style:O},c)}function Q(a){let{id:b,announcement:c,ariaLiveType:d="assertive"}=a;return p().createElement("div",{id:b,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":d,"aria-atomic":!0},c)}let R=(0,o.createContext)(null),S={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},T={onDragStart(a){let{active:b}=a;return"Picked up draggable item "+b.id+"."},onDragOver(a){let{active:b,over:c}=a;return c?"Draggable item "+b.id+" was moved over droppable area "+c.id+".":"Draggable item "+b.id+" is no longer over a droppable area."},onDragEnd(a){let{active:b,over:c}=a;return c?"Draggable item "+b.id+" was dropped over droppable area "+c.id:"Draggable item "+b.id+" was dropped."},onDragCancel(a){let{active:b}=a;return"Dragging was cancelled. Draggable item "+b.id+" was dropped."}};function U(a){let{announcements:b=T,container:c,hiddenTextDescribedById:d,screenReaderInstructions:e=S}=a,{announce:f,announcement:g}=function(){let[a,b]=(0,o.useState)("");return{announce:(0,o.useCallback)(a=>{null!=a&&b(a)},[]),announcement:a}}(),h=G("DndLiveRegion"),[i,j]=(0,o.useState)(!1);(0,o.useEffect)(()=>{j(!0)},[]);var k=(0,o.useMemo)(()=>({onDragStart(a){let{active:c}=a;f(b.onDragStart({active:c}))},onDragMove(a){let{active:c,over:d}=a;b.onDragMove&&f(b.onDragMove({active:c,over:d}))},onDragOver(a){let{active:c,over:d}=a;f(b.onDragOver({active:c,over:d}))},onDragEnd(a){let{active:c,over:d}=a;f(b.onDragEnd({active:c,over:d}))},onDragCancel(a){let{active:c,over:d}=a;f(b.onDragCancel({active:c,over:d}))}}),[f,b]);let l=(0,o.useContext)(R);if((0,o.useEffect)(()=>{if(!l)throw Error("useDndMonitor must be used within a children of <DndContext>");return l(k)},[k,l]),!i)return null;let m=p().createElement(p().Fragment,null,p().createElement(P,{id:d,value:e.draggable}),p().createElement(Q,{id:h,announcement:g}));return c?(0,q.createPortal)(m,c):m}function V(){}function W(a,b){return(0,o.useMemo)(()=>({sensor:a,options:null!=b?b:{}}),[a,b])}!function(a){a.DragStart="dragStart",a.DragMove="dragMove",a.DragEnd="dragEnd",a.DragCancel="dragCancel",a.DragOver="dragOver",a.RegisterDroppable="registerDroppable",a.SetDroppableDisabled="setDroppableDisabled",a.UnregisterDroppable="unregisterDroppable"}(d||(d={}));let X=Object.freeze({x:0,y:0});function Y(a,b){return Math.sqrt(Math.pow(a.x-b.x,2)+Math.pow(a.y-b.y,2))}function Z(a,b){let{data:{value:c}}=a,{data:{value:d}}=b;return c-d}function $(a,b){let{data:{value:c}}=a,{data:{value:d}}=b;return d-c}function _(a){let{left:b,top:c,height:d,width:e}=a;return[{x:b,y:c},{x:b+e,y:c},{x:b,y:c+d},{x:b+e,y:c+d}]}function aa(a,b){if(!a||0===a.length)return null;let[c]=a;return b?c[b]:c}function ab(a,b,c){return void 0===b&&(b=a.left),void 0===c&&(c=a.top),{x:b+.5*a.width,y:c+.5*a.height}}let ac=a=>{let{collisionRect:b,droppableRects:c,droppableContainers:d}=a,e=ab(b,b.left,b.top),f=[];for(let a of d){let{id:b}=a,d=c.get(b);if(d){let c=Y(ab(d),e);f.push({id:b,data:{droppableContainer:a,value:c}})}}return f.sort(Z)},ad=a=>{let{collisionRect:b,droppableRects:c,droppableContainers:d}=a,e=[];for(let a of d){let{id:d}=a,f=c.get(d);if(f){let c=function(a,b){let c=Math.max(b.top,a.top),d=Math.max(b.left,a.left),e=Math.min(b.left+b.width,a.left+a.width),f=Math.min(b.top+b.height,a.top+a.height);if(d<e&&c<f){let g=b.width*b.height,h=a.width*a.height,i=(e-d)*(f-c);return Number((i/(g+h-i)).toFixed(4))}return 0}(f,b);c>0&&e.push({id:d,data:{droppableContainer:a,value:c}})}}return e.sort($)};function ae(a,b){return a&&b?{x:a.left-b.left,y:a.top-b.top}:X}let af=function(a){return function(b){for(var c=arguments.length,d=Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];return d.reduce((b,c)=>({...b,top:b.top+a*c.y,bottom:b.bottom+a*c.y,left:b.left+a*c.x,right:b.right+a*c.x}),{...b})}}(1),ag={ignoreTransform:!1};function ah(a,b){void 0===b&&(b=ag);let c=a.getBoundingClientRect();if(b.ignoreTransform){let{transform:b,transformOrigin:d}=u(a).getComputedStyle(a);b&&(c=function(a,b,c){let d=function(a){if(a.startsWith("matrix3d(")){let b=a.slice(9,-1).split(/, /);return{x:+b[12],y:+b[13],scaleX:+b[0],scaleY:+b[5]}}if(a.startsWith("matrix(")){let b=a.slice(7,-1).split(/, /);return{x:+b[4],y:+b[5],scaleX:+b[0],scaleY:+b[3]}}return null}(b);if(!d)return a;let{scaleX:e,scaleY:f,x:g,y:h}=d,i=a.left-g-(1-e)*parseFloat(c),j=a.top-h-(1-f)*parseFloat(c.slice(c.indexOf(" ")+1)),k=e?a.width/e:a.width,l=f?a.height/f:a.height;return{width:k,height:l,top:j,right:i+k,bottom:j+l,left:i}}(c,b,d))}let{top:d,left:e,width:f,height:g,bottom:h,right:i}=c;return{top:d,left:e,width:f,height:g,bottom:h,right:i}}function ai(a){return ah(a,{ignoreTransform:!0})}function aj(a,b){let c=[];return a?function d(e){var f;if(null!=b&&c.length>=b||!e)return c;if(v(e)&&null!=e.scrollingElement&&!c.includes(e.scrollingElement))return c.push(e.scrollingElement),c;if(!w(e)||x(e)||c.includes(e))return c;let g=u(a).getComputedStyle(e);return(e!==a&&function(a,b){void 0===b&&(b=u(a).getComputedStyle(a));let c=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(a=>{let d=b[a];return"string"==typeof d&&c.test(d)})}(e,g)&&c.push(e),void 0===(f=g)&&(f=u(e).getComputedStyle(e)),"fixed"===f.position)?c:d(e.parentNode)}(a):c}function ak(a){let[b]=aj(a,1);return null!=b?b:null}function al(a){return r&&a?s(a)?a:t(a)?v(a)||a===y(a).scrollingElement?window:w(a)?a:null:null:null}function am(a){return s(a)?a.scrollX:a.scrollLeft}function an(a){return s(a)?a.scrollY:a.scrollTop}function ao(a){return{x:am(a),y:an(a)}}function ap(a){return!!r&&!!a&&a===document.scrollingElement}function aq(a){let b={x:0,y:0},c=ap(a)?{height:window.innerHeight,width:window.innerWidth}:{height:a.clientHeight,width:a.clientWidth},d={x:a.scrollWidth-c.width,y:a.scrollHeight-c.height},e=a.scrollTop<=b.y,f=a.scrollLeft<=b.x;return{isTop:e,isLeft:f,isBottom:a.scrollTop>=d.y,isRight:a.scrollLeft>=d.x,maxScroll:d,minScroll:b}}!function(a){a[a.Forward=1]="Forward",a[a.Backward=-1]="Backward"}(e||(e={}));let ar={x:.2,y:.2};function as(a){return a.reduce((a,b)=>I(a,ao(b)),X)}let at=[["x",["left","right"],function(a){return a.reduce((a,b)=>a+am(b),0)}],["y",["top","bottom"],function(a){return a.reduce((a,b)=>a+an(b),0)}]];class au{constructor(a,b){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let c=aj(b),d=as(c);for(let[b,e,f]of(this.rect={...a},this.width=a.width,this.height=a.height,at))for(let a of e)Object.defineProperty(this,a,{get:()=>{let e=f(c),g=d[b]-e;return this.rect[a]+g},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class av{constructor(a){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(a=>{var b;return null==(b=this.target)?void 0:b.removeEventListener(...a)})},this.target=a}add(a,b,c){var d;null==(d=this.target)||d.addEventListener(a,b,c),this.listeners.push([a,b,c])}}function aw(a,b){let c=Math.abs(a.x),d=Math.abs(a.y);return"number"==typeof b?Math.sqrt(c**2+d**2)>b:"x"in b&&"y"in b?c>b.x&&d>b.y:"x"in b?c>b.x:"y"in b&&d>b.y}function ax(a){a.preventDefault()}function ay(a){a.stopPropagation()}!function(a){a.Click="click",a.DragStart="dragstart",a.Keydown="keydown",a.ContextMenu="contextmenu",a.Resize="resize",a.SelectionChange="selectionchange",a.VisibilityChange="visibilitychange"}(f||(f={})),function(a){a.Space="Space",a.Down="ArrowDown",a.Right="ArrowRight",a.Left="ArrowLeft",a.Up="ArrowUp",a.Esc="Escape",a.Enter="Enter",a.Tab="Tab"}(g||(g={}));let az={start:[g.Space,g.Enter],cancel:[g.Esc],end:[g.Space,g.Enter,g.Tab]},aA=(a,b)=>{let{currentCoordinates:c}=b;switch(a.code){case g.Right:return{...c,x:c.x+25};case g.Left:return{...c,x:c.x-25};case g.Down:return{...c,y:c.y+25};case g.Up:return{...c,y:c.y-25}}};class aB{constructor(a){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=a;let{event:{target:b}}=a;this.props=a,this.listeners=new av(y(b)),this.windowListeners=new av(u(b)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(f.Resize,this.handleCancel),this.windowListeners.add(f.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(f.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:a,onStart:b}=this.props,c=a.node.current;c&&function(a,b){if(void 0===b&&(b=ah),!a)return;let{top:c,left:d,bottom:e,right:f}=b(a);ak(a)&&(e<=0||f<=0||c>=window.innerHeight||d>=window.innerWidth)&&a.scrollIntoView({block:"center",inline:"center"})}(c),b(X)}handleKeyDown(a){if(K(a)){let{active:b,context:c,options:d}=this.props,{keyboardCodes:e=az,coordinateGetter:f=aA,scrollBehavior:h="smooth"}=d,{code:i}=a;if(e.end.includes(i))return void this.handleEnd(a);if(e.cancel.includes(i))return void this.handleCancel(a);let{collisionRect:j}=c.current,k=j?{x:j.left,y:j.top}:X;this.referenceCoordinates||(this.referenceCoordinates=k);let l=f(a,{active:b,context:c.current,currentCoordinates:k});if(l){let b=J(l,k),d={x:0,y:0},{scrollableAncestors:e}=c.current;for(let c of e){let e=a.code,{isTop:f,isRight:i,isLeft:j,isBottom:k,maxScroll:m,minScroll:n}=aq(c),o=function(a){if(a===document.scrollingElement){let{innerWidth:a,innerHeight:b}=window;return{top:0,left:0,right:a,bottom:b,width:a,height:b}}let{top:b,left:c,right:d,bottom:e}=a.getBoundingClientRect();return{top:b,left:c,right:d,bottom:e,width:a.clientWidth,height:a.clientHeight}}(c),p={x:Math.min(e===g.Right?o.right-o.width/2:o.right,Math.max(e===g.Right?o.left:o.left+o.width/2,l.x)),y:Math.min(e===g.Down?o.bottom-o.height/2:o.bottom,Math.max(e===g.Down?o.top:o.top+o.height/2,l.y))},q=e===g.Right&&!i||e===g.Left&&!j,r=e===g.Down&&!k||e===g.Up&&!f;if(q&&p.x!==l.x){let a=c.scrollLeft+b.x,f=e===g.Right&&a<=m.x||e===g.Left&&a>=n.x;if(f&&!b.y)return void c.scrollTo({left:a,behavior:h});f?d.x=c.scrollLeft-a:d.x=e===g.Right?c.scrollLeft-m.x:c.scrollLeft-n.x,d.x&&c.scrollBy({left:-d.x,behavior:h});break}if(r&&p.y!==l.y){let a=c.scrollTop+b.y,f=e===g.Down&&a<=m.y||e===g.Up&&a>=n.y;if(f&&!b.x)return void c.scrollTo({top:a,behavior:h});f?d.y=c.scrollTop-a:d.y=e===g.Down?c.scrollTop-m.y:c.scrollTop-n.y,d.y&&c.scrollBy({top:-d.y,behavior:h});break}}this.handleMove(a,I(J(l,this.referenceCoordinates),d))}}}handleMove(a,b){let{onMove:c}=this.props;a.preventDefault(),c(b)}handleEnd(a){let{onEnd:b}=this.props;a.preventDefault(),this.detach(),b()}handleCancel(a){let{onCancel:b}=this.props;a.preventDefault(),this.detach(),b()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function aC(a){return!!(a&&"distance"in a)}function aD(a){return!!(a&&"delay"in a)}aB.activators=[{eventName:"onKeyDown",handler:(a,b,c)=>{let{keyboardCodes:d=az,onActivation:e}=b,{active:f}=c,{code:g}=a.nativeEvent;if(d.start.includes(g)){let b=f.activatorNode.current;return(!b||a.target===b)&&(a.preventDefault(),null==e||e({event:a.nativeEvent}),!0)}return!1}}];class aE{constructor(a,b,c){var d;void 0===c&&(c=function(a){let{EventTarget:b}=u(a);return a instanceof b?a:y(a)}(a.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=a,this.events=b;let{event:e}=a,{target:f}=e;this.props=a,this.events=b,this.document=y(f),this.documentListeners=new av(this.document),this.listeners=new av(c),this.windowListeners=new av(u(f)),this.initialCoordinates=null!=(d=L(e))?d:X,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:a,props:{options:{activationConstraint:b,bypassActivationConstraint:c}}}=this;if(this.listeners.add(a.move.name,this.handleMove,{passive:!1}),this.listeners.add(a.end.name,this.handleEnd),a.cancel&&this.listeners.add(a.cancel.name,this.handleCancel),this.windowListeners.add(f.Resize,this.handleCancel),this.windowListeners.add(f.DragStart,ax),this.windowListeners.add(f.VisibilityChange,this.handleCancel),this.windowListeners.add(f.ContextMenu,ax),this.documentListeners.add(f.Keydown,this.handleKeydown),b){if(null!=c&&c({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(aD(b)){this.timeoutId=setTimeout(this.handleStart,b.delay),this.handlePending(b);return}if(aC(b))return void this.handlePending(b)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(a,b){let{active:c,onPending:d}=this.props;d(c,a,this.initialCoordinates,b)}handleStart(){let{initialCoordinates:a}=this,{onStart:b}=this.props;a&&(this.activated=!0,this.documentListeners.add(f.Click,ay,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(f.SelectionChange,this.removeTextSelection),b(a))}handleMove(a){var b;let{activated:c,initialCoordinates:d,props:e}=this,{onMove:f,options:{activationConstraint:g}}=e;if(!d)return;let h=null!=(b=L(a))?b:X,i=J(d,h);if(!c&&g){if(aC(g)){if(null!=g.tolerance&&aw(i,g.tolerance))return this.handleCancel();if(aw(i,g.distance))return this.handleStart()}return aD(g)&&aw(i,g.tolerance)?this.handleCancel():void this.handlePending(g,i)}a.cancelable&&a.preventDefault(),f(h)}handleEnd(){let{onAbort:a,onEnd:b}=this.props;this.detach(),this.activated||a(this.props.active),b()}handleCancel(){let{onAbort:a,onCancel:b}=this.props;this.detach(),this.activated||a(this.props.active),b()}handleKeydown(a){a.code===g.Esc&&this.handleCancel()}removeTextSelection(){var a;null==(a=this.document.getSelection())||a.removeAllRanges()}}let aF={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class aG extends aE{constructor(a){let{event:b}=a;super(a,aF,y(b.target))}}aG.activators=[{eventName:"onPointerDown",handler:(a,b)=>{let{nativeEvent:c}=a,{onActivation:d}=b;return!!c.isPrimary&&0===c.button&&(null==d||d({event:c}),!0)}}];let aH={move:{name:"mousemove"},end:{name:"mouseup"}};!function(a){a[a.RightClick=2]="RightClick"}(h||(h={}));class aI extends aE{constructor(a){super(a,aH,y(a.event.target))}}aI.activators=[{eventName:"onMouseDown",handler:(a,b)=>{let{nativeEvent:c}=a,{onActivation:d}=b;return c.button!==h.RightClick&&(null==d||d({event:c}),!0)}}];let aJ={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class aK extends aE{constructor(a){super(a,aJ)}static setup(){return window.addEventListener(aJ.move.name,a,{capture:!1,passive:!1}),function(){window.removeEventListener(aJ.move.name,a)};function a(){}}}aK.activators=[{eventName:"onTouchStart",handler:(a,b)=>{let{nativeEvent:c}=a,{onActivation:d}=b,{touches:e}=c;return!(e.length>1)&&(null==d||d({event:c}),!0)}}],function(a){a[a.Pointer=0]="Pointer",a[a.DraggableRect=1]="DraggableRect"}(i||(i={})),function(a){a[a.TreeOrder=0]="TreeOrder",a[a.ReversedTreeOrder=1]="ReversedTreeOrder"}(j||(j={}));let aL={x:{[e.Backward]:!1,[e.Forward]:!1},y:{[e.Backward]:!1,[e.Forward]:!1}};!function(a){a[a.Always=0]="Always",a[a.BeforeDragging=1]="BeforeDragging",a[a.WhileDragging=2]="WhileDragging"}(k||(k={})),(l||(l={})).Optimized="optimized";let aM=new Map;function aN(a,b){return C(c=>a?c||("function"==typeof b?b(a):a):null,[b,a])}function aO(a){let{callback:b,disabled:c}=a,d=A(b),e=(0,o.useMemo)(()=>{if(c||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:a}=window;return new a(d)},[c]);return(0,o.useEffect)(()=>()=>null==e?void 0:e.disconnect(),[e]),e}function aP(a){return new au(ah(a),a)}function aQ(a,b,c){void 0===b&&(b=aP);let[d,e]=(0,o.useState)(null);function f(){e(d=>{if(!a)return null;if(!1===a.isConnected){var e;return null!=(e=null!=d?d:c)?e:null}let f=b(a);return JSON.stringify(d)===JSON.stringify(f)?d:f})}let g=function(a){let{callback:b,disabled:c}=a,d=A(b),e=(0,o.useMemo)(()=>{if(c||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:a}=window;return new a(d)},[d,c]);return(0,o.useEffect)(()=>()=>null==e?void 0:e.disconnect(),[e]),e}({callback(b){if(a)for(let c of b){let{type:b,target:d}=c;if("childList"===b&&d instanceof HTMLElement&&d.contains(a)){f();break}}}}),h=aO({callback:f});return z(()=>{f(),a?(null==h||h.observe(a),null==g||g.observe(document.body,{childList:!0,subtree:!0})):(null==h||h.disconnect(),null==g||g.disconnect())},[a]),d}let aR=[];function aS(a,b){void 0===b&&(b=[]);let c=(0,o.useRef)(null);return(0,o.useEffect)(()=>{c.current=null},b),(0,o.useEffect)(()=>{let b=a!==X;b&&!c.current&&(c.current=a),!b&&c.current&&(c.current=null)},[a]),c.current?J(a,c.current):X}function aT(a){return(0,o.useMemo)(()=>a?function(a){let b=a.innerWidth,c=a.innerHeight;return{top:0,left:0,right:b,bottom:c,width:b,height:c}}(a):null,[a])}let aU=[],aV=[{sensor:aG,options:{}},{sensor:aB,options:{}}],aW={current:{}},aX={draggable:{measure:ai},droppable:{measure:ai,strategy:k.WhileDragging,frequency:l.Optimized},dragOverlay:{measure:ah}};class aY extends Map{get(a){var b;return null!=a&&null!=(b=super.get(a))?b:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(a=>{let{disabled:b}=a;return!b})}getNodeFor(a){var b,c;return null!=(b=null==(c=this.get(a))?void 0:c.node.current)?b:void 0}}let aZ={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new aY,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:V},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:aX,measureDroppableContainers:V,windowRect:null,measuringScheduled:!1},a$={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:V,draggableNodes:new Map,over:null,measureDroppableContainers:V},a_=(0,o.createContext)(a$),a0=(0,o.createContext)(aZ);function a1(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new aY}}}function a2(a,b){switch(b.type){case d.DragStart:return{...a,draggable:{...a.draggable,initialCoordinates:b.initialCoordinates,active:b.active}};case d.DragMove:if(null==a.draggable.active)return a;return{...a,draggable:{...a.draggable,translate:{x:b.coordinates.x-a.draggable.initialCoordinates.x,y:b.coordinates.y-a.draggable.initialCoordinates.y}}};case d.DragEnd:case d.DragCancel:return{...a,draggable:{...a.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case d.RegisterDroppable:{let{element:c}=b,{id:d}=c,e=new aY(a.droppable.containers);return e.set(d,c),{...a,droppable:{...a.droppable,containers:e}}}case d.SetDroppableDisabled:{let{id:c,key:d,disabled:e}=b,f=a.droppable.containers.get(c);if(!f||d!==f.key)return a;let g=new aY(a.droppable.containers);return g.set(c,{...f,disabled:e}),{...a,droppable:{...a.droppable,containers:g}}}case d.UnregisterDroppable:{let{id:c,key:d}=b,e=a.droppable.containers.get(c);if(!e||d!==e.key)return a;let f=new aY(a.droppable.containers);return f.delete(c),{...a,droppable:{...a.droppable,containers:f}}}default:return a}}function a3(a){let{disabled:b}=a,{active:c,activatorEvent:d,draggableNodes:e}=(0,o.useContext)(a_),f=E(d),g=E(null==c?void 0:c.id);return(0,o.useEffect)(()=>{if(!b&&!d&&f&&null!=g){if(!K(f)||document.activeElement===f.target)return;let a=e.get(g);if(!a)return;let{activatorNode:b,node:c}=a;(b.current||c.current)&&requestAnimationFrame(()=>{for(let a of[b.current,c.current]){if(!a)continue;let b=a.matches(N)?a:a.querySelector(N);if(b){b.focus();break}}})}},[d,b,e,g,f]),null}let a4=(0,o.createContext)({...X,scaleX:1,scaleY:1});!function(a){a[a.Uninitialized=0]="Uninitialized",a[a.Initializing=1]="Initializing",a[a.Initialized=2]="Initialized"}(m||(m={}));let a5=(0,o.memo)(function(a){var b,c,f,g,h,l;let{id:n,accessibility:s,autoScroll:t=!0,children:v,sensors:x=aV,collisionDetection:y=ad,measuring:A,modifiers:F,...H}=a,[J,K]=(0,o.useReducer)(a2,void 0,a1),[M,N]=function(){let[a]=(0,o.useState)(()=>new Set),b=(0,o.useCallback)(b=>(a.add(b),()=>a.delete(b)),[a]);return[(0,o.useCallback)(b=>{let{type:c,event:d}=b;a.forEach(a=>{var b;return null==(b=a[c])?void 0:b.call(a,d)})},[a]),b]}(),[O,P]=(0,o.useState)(m.Uninitialized),Q=O===m.Initialized,{draggable:{active:S,nodes:T,translate:V},droppable:{containers:W}}=J,Y=null!=S?T.get(S):null,Z=(0,o.useRef)({initial:null,translated:null}),$=(0,o.useMemo)(()=>{var a;return null!=S?{id:S,data:null!=(a=null==Y?void 0:Y.data)?a:aW,rect:Z}:null},[S,Y]),_=(0,o.useRef)(null),[ab,ac]=(0,o.useState)(null),[ag,ai]=(0,o.useState)(null),am=B(H,Object.values(H)),an=G("DndDescribedBy",n),at=(0,o.useMemo)(()=>W.getEnabled(),[W]),av=(0,o.useMemo)(()=>({draggable:{...aX.draggable,...null==A?void 0:A.draggable},droppable:{...aX.droppable,...null==A?void 0:A.droppable},dragOverlay:{...aX.dragOverlay,...null==A?void 0:A.dragOverlay}}),[null==A?void 0:A.draggable,null==A?void 0:A.droppable,null==A?void 0:A.dragOverlay]),{droppableRects:aw,measureDroppableContainers:ax,measuringScheduled:ay}=function(a,b){let{dragging:c,dependencies:d,config:e}=b,[f,g]=(0,o.useState)(null),{frequency:h,measure:i,strategy:j}=e,l=(0,o.useRef)(a),m=function(){switch(j){case k.Always:return!1;case k.BeforeDragging:return c;default:return!c}}(),n=B(m),p=(0,o.useCallback)(function(a){void 0===a&&(a=[]),n.current||g(b=>null===b?a:b.concat(a.filter(a=>!b.includes(a))))},[n]),q=(0,o.useRef)(null),r=C(b=>{if(m&&!c)return aM;if(!b||b===aM||l.current!==a||null!=f){let b=new Map;for(let c of a){if(!c)continue;if(f&&f.length>0&&!f.includes(c.id)&&c.rect.current){b.set(c.id,c.rect.current);continue}let a=c.node.current,d=a?new au(i(a),a):null;c.rect.current=d,d&&b.set(c.id,d)}return b}return b},[a,f,c,m,i]);return(0,o.useEffect)(()=>{l.current=a},[a]),(0,o.useEffect)(()=>{m||p()},[c,m]),(0,o.useEffect)(()=>{f&&f.length>0&&g(null)},[JSON.stringify(f)]),(0,o.useEffect)(()=>{m||"number"!=typeof h||null!==q.current||(q.current=setTimeout(()=>{p(),q.current=null},h))},[h,m,p,...d]),{droppableRects:r,measureDroppableContainers:p,measuringScheduled:null!=f}}(at,{dragging:Q,dependencies:[V.x,V.y],config:av.droppable}),az=function(a,b){let c=null!=b?a.get(b):void 0,d=c?c.node.current:null;return C(a=>{var c;return null==b?null:null!=(c=null!=d?d:a)?c:null},[d,b])}(T,S),aA=(0,o.useMemo)(()=>ag?L(ag):null,[ag]),aB=function(){let a=(null==ab?void 0:ab.autoScrollEnabled)===!1,b="object"==typeof t?!1===t.enabled:!1===t,c=Q&&!a&&!b;return"object"==typeof t?{...t,enabled:c}:{enabled:c}}(),aC=aN(az,av.draggable.measure);!function(a){let{activeNode:b,measure:c,initialRect:d,config:e=!0}=a,f=(0,o.useRef)(!1),{x:g,y:h}="boolean"==typeof e?{x:e,y:e}:e;z(()=>{if(!g&&!h||!b){f.current=!1;return}if(f.current||!d)return;let a=null==b?void 0:b.node.current;if(!a||!1===a.isConnected)return;let e=ae(c(a),d);if(g||(e.x=0),h||(e.y=0),f.current=!0,Math.abs(e.x)>0||Math.abs(e.y)>0){let b=ak(a);b&&b.scrollBy({top:e.y,left:e.x})}},[b,g,h,d,c])}({activeNode:null!=S?T.get(S):null,config:aB.layoutShiftCompensation,initialRect:aC,measure:av.draggable.measure});let aD=aQ(az,av.draggable.measure,aC),aE=aQ(az?az.parentElement:null),aF=(0,o.useRef)({activatorEvent:null,active:null,activeNode:az,collisionRect:null,collisions:null,droppableRects:aw,draggableNodes:T,draggingNode:null,draggingNodeRect:null,droppableContainers:W,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),aG=W.getNodeFor(null==(b=aF.current.over)?void 0:b.id),aH=function(a){let{measure:b}=a,[c,d]=(0,o.useState)(null),e=aO({callback:(0,o.useCallback)(a=>{for(let{target:c}of a)if(w(c)){d(a=>{let d=b(c);return a?{...a,width:d.width,height:d.height}:d});break}},[b])}),[f,g]=D((0,o.useCallback)(a=>{let c=function(a){if(!a)return null;if(a.children.length>1)return a;let b=a.children[0];return w(b)?b:a}(a);null==e||e.disconnect(),c&&(null==e||e.observe(c)),d(c?b(c):null)},[b,e]));return(0,o.useMemo)(()=>({nodeRef:f,rect:c,setRef:g}),[c,f,g])}({measure:av.dragOverlay.measure}),aI=null!=(c=aH.nodeRef.current)?c:az,aJ=Q?null!=(f=aH.rect)?f:aD:null,aK=!!(aH.nodeRef.current&&aH.rect),aP=function(a){let b=aN(a);return ae(a,b)}(aK?null:aD),aY=aT(aI?u(aI):null),aZ=function(a){let b=(0,o.useRef)(a),c=C(c=>a?c&&c!==aR&&a&&b.current&&a.parentNode===b.current.parentNode?c:aj(a):aR,[a]);return(0,o.useEffect)(()=>{b.current=a},[a]),c}(Q?null!=aG?aG:az:null),a$=function(a,b){void 0===b&&(b=ah);let[c]=a,d=aT(c?u(c):null),[e,f]=(0,o.useState)(aU);function g(){f(()=>a.length?a.map(a=>ap(a)?d:new au(b(a),a)):aU)}let h=aO({callback:g});return z(()=>{null==h||h.disconnect(),g(),a.forEach(a=>null==h?void 0:h.observe(a))},[a]),e}(aZ),a5=function(a,b){let{transform:c,...d}=b;return null!=a&&a.length?a.reduce((a,b)=>b({transform:a,...d}),c):c}(F,{transform:{x:V.x-aP.x,y:V.y-aP.y,scaleX:1,scaleY:1},activatorEvent:ag,active:$,activeNodeRect:aD,containerNodeRect:aE,draggingNodeRect:aJ,over:aF.current.over,overlayNodeRect:aH.rect,scrollableAncestors:aZ,scrollableAncestorRects:a$,windowRect:aY}),a6=aA?I(aA,V):null,a7=function(a){let[b,c]=(0,o.useState)(null),d=(0,o.useRef)(a),e=(0,o.useCallback)(a=>{let b=al(a.target);b&&c(a=>a?(a.set(b,ao(b)),new Map(a)):null)},[]);return(0,o.useEffect)(()=>{let b=d.current;if(a!==b){f(b);let g=a.map(a=>{let b=al(a);return b?(b.addEventListener("scroll",e,{passive:!0}),[b,ao(b)]):null}).filter(a=>null!=a);c(g.length?new Map(g):null),d.current=a}return()=>{f(a),f(b)};function f(a){a.forEach(a=>{let b=al(a);null==b||b.removeEventListener("scroll",e)})}},[e,a]),(0,o.useMemo)(()=>a.length?b?Array.from(b.values()).reduce((a,b)=>I(a,b),X):as(a):X,[a,b])}(aZ),a8=aS(a7),a9=aS(a7,[aD]),ba=I(a5,a8),bb=aJ?af(aJ,a5):null,bc=$&&bb?y({active:$,collisionRect:bb,droppableRects:aw,droppableContainers:at,pointerCoordinates:a6}):null,bd=aa(bc,"id"),[be,bf]=(0,o.useState)(null),bg=(h=aK?a5:I(a5,a9),l=null!=(g=null==be?void 0:be.rect)?g:null,{...h,scaleX:l&&aD?l.width/aD.width:1,scaleY:l&&aD?l.height/aD.height:1}),bh=(0,o.useRef)(null),bi=(0,o.useCallback)((a,b)=>{let{sensor:c,options:e}=b;if(null==_.current)return;let f=T.get(_.current);if(!f)return;let g=a.nativeEvent,h=new c({active:_.current,activeNode:f,event:g,options:e,context:aF,onAbort(a){if(!T.get(a))return;let{onDragAbort:b}=am.current,c={id:a};null==b||b(c),M({type:"onDragAbort",event:c})},onPending(a,b,c,d){if(!T.get(a))return;let{onDragPending:e}=am.current,f={id:a,constraint:b,initialCoordinates:c,offset:d};null==e||e(f),M({type:"onDragPending",event:f})},onStart(a){let b=_.current;if(null==b)return;let c=T.get(b);if(!c)return;let{onDragStart:e}=am.current,f={activatorEvent:g,active:{id:b,data:c.data,rect:Z}};(0,q.unstable_batchedUpdates)(()=>{null==e||e(f),P(m.Initializing),K({type:d.DragStart,initialCoordinates:a,active:b}),M({type:"onDragStart",event:f}),ac(bh.current),ai(g)})},onMove(a){K({type:d.DragMove,coordinates:a})},onEnd:i(d.DragEnd),onCancel:i(d.DragCancel)});function i(a){return async function(){let{active:b,collisions:c,over:e,scrollAdjustedTranslate:f}=aF.current,h=null;if(b&&f){let{cancelDrop:i}=am.current;h={activatorEvent:g,active:b,collisions:c,delta:f,over:e},a===d.DragEnd&&"function"==typeof i&&await Promise.resolve(i(h))&&(a=d.DragCancel)}_.current=null,(0,q.unstable_batchedUpdates)(()=>{K({type:a}),P(m.Uninitialized),bf(null),ac(null),ai(null),bh.current=null;let b=a===d.DragEnd?"onDragEnd":"onDragCancel";if(h){let a=am.current[b];null==a||a(h),M({type:b,event:h})}})}}bh.current=h},[T]),bj=(0,o.useCallback)((a,b)=>(c,d)=>{let e=c.nativeEvent,f=T.get(d);null!==_.current||!f||e.dndKit||e.defaultPrevented||!0===a(c,b.options,{active:f})&&(e.dndKit={capturedBy:b.sensor},_.current=d,bi(c,b))},[T,bi]),bk=(0,o.useMemo)(()=>x.reduce((a,b)=>{let{sensor:c}=b;return[...a,...c.activators.map(a=>({eventName:a.eventName,handler:bj(a.handler,b)}))]},[]),[x,bj]);(0,o.useEffect)(()=>{if(!r)return;let a=x.map(a=>{let{sensor:b}=a;return null==b.setup?void 0:b.setup()});return()=>{for(let b of a)null==b||b()}},x.map(a=>{let{sensor:b}=a;return b})),z(()=>{aD&&O===m.Initializing&&P(m.Initialized)},[aD,O]),(0,o.useEffect)(()=>{let{onDragMove:a}=am.current,{active:b,activatorEvent:c,collisions:d,over:e}=aF.current;if(!b||!c)return;let f={active:b,activatorEvent:c,collisions:d,delta:{x:ba.x,y:ba.y},over:e};(0,q.unstable_batchedUpdates)(()=>{null==a||a(f),M({type:"onDragMove",event:f})})},[ba.x,ba.y]),(0,o.useEffect)(()=>{let{active:a,activatorEvent:b,collisions:c,droppableContainers:d,scrollAdjustedTranslate:e}=aF.current;if(!a||null==_.current||!b||!e)return;let{onDragOver:f}=am.current,g=d.get(bd),h=g&&g.rect.current?{id:g.id,rect:g.rect.current,data:g.data,disabled:g.disabled}:null,i={active:a,activatorEvent:b,collisions:c,delta:{x:e.x,y:e.y},over:h};(0,q.unstable_batchedUpdates)(()=>{bf(h),null==f||f(i),M({type:"onDragOver",event:i})})},[bd]),z(()=>{aF.current={activatorEvent:ag,active:$,activeNode:az,collisionRect:bb,collisions:bc,droppableRects:aw,draggableNodes:T,draggingNode:aI,draggingNodeRect:aJ,droppableContainers:W,over:be,scrollableAncestors:aZ,scrollAdjustedTranslate:ba},Z.current={initial:aJ,translated:bb}},[$,az,bc,bb,T,aI,aJ,aw,W,be,aZ,ba]),function(a){let{acceleration:b,activator:c=i.Pointer,canScroll:d,draggingRect:f,enabled:g,interval:h=5,order:k=j.TreeOrder,pointerCoordinates:l,scrollableAncestors:m,scrollableAncestorRects:n,delta:p,threshold:q}=a,r=function(a){let{delta:b,disabled:c}=a,d=E(b);return C(a=>{if(c||!d||!a)return aL;let f={x:Math.sign(b.x-d.x),y:Math.sign(b.y-d.y)};return{x:{[e.Backward]:a.x[e.Backward]||-1===f.x,[e.Forward]:a.x[e.Forward]||1===f.x},y:{[e.Backward]:a.y[e.Backward]||-1===f.y,[e.Forward]:a.y[e.Forward]||1===f.y}}},[c,b,d])}({delta:p,disabled:!g}),[s,t]=function(){let a=(0,o.useRef)(null);return[(0,o.useCallback)((b,c)=>{a.current=setInterval(b,c)},[]),(0,o.useCallback)(()=>{null!==a.current&&(clearInterval(a.current),a.current=null)},[])]}(),u=(0,o.useRef)({x:0,y:0}),v=(0,o.useRef)({x:0,y:0}),w=(0,o.useMemo)(()=>{switch(c){case i.Pointer:return l?{top:l.y,bottom:l.y,left:l.x,right:l.x}:null;case i.DraggableRect:return f}},[c,f,l]),x=(0,o.useRef)(null),y=(0,o.useCallback)(()=>{let a=x.current;if(!a)return;let b=u.current.x*v.current.x,c=u.current.y*v.current.y;a.scrollBy(b,c)},[]),z=(0,o.useMemo)(()=>k===j.TreeOrder?[...m].reverse():m,[k,m]);(0,o.useEffect)(()=>{if(!g||!m.length||!w)return void t();for(let a of z){if((null==d?void 0:d(a))===!1)continue;let c=n[m.indexOf(a)];if(!c)continue;let{direction:f,speed:g}=function(a,b,c,d,f){let{top:g,left:h,right:i,bottom:j}=c;void 0===d&&(d=10),void 0===f&&(f=ar);let{isTop:k,isBottom:l,isLeft:m,isRight:n}=aq(a),o={x:0,y:0},p={x:0,y:0},q={height:b.height*f.y,width:b.width*f.x};return!k&&g<=b.top+q.height?(o.y=e.Backward,p.y=d*Math.abs((b.top+q.height-g)/q.height)):!l&&j>=b.bottom-q.height&&(o.y=e.Forward,p.y=d*Math.abs((b.bottom-q.height-j)/q.height)),!n&&i>=b.right-q.width?(o.x=e.Forward,p.x=d*Math.abs((b.right-q.width-i)/q.width)):!m&&h<=b.left+q.width&&(o.x=e.Backward,p.x=d*Math.abs((b.left+q.width-h)/q.width)),{direction:o,speed:p}}(a,c,w,b,q);for(let a of["x","y"])r[a][f[a]]||(g[a]=0,f[a]=0);if(g.x>0||g.y>0){t(),x.current=a,s(y,h),u.current=g,v.current=f;return}}u.current={x:0,y:0},v.current={x:0,y:0},t()},[b,y,d,t,g,h,JSON.stringify(w),JSON.stringify(r),s,m,z,n,JSON.stringify(q)])}({...aB,delta:V,draggingRect:bb,pointerCoordinates:a6,scrollableAncestors:aZ,scrollableAncestorRects:a$});let bl=(0,o.useMemo)(()=>({active:$,activeNode:az,activeNodeRect:aD,activatorEvent:ag,collisions:bc,containerNodeRect:aE,dragOverlay:aH,draggableNodes:T,droppableContainers:W,droppableRects:aw,over:be,measureDroppableContainers:ax,scrollableAncestors:aZ,scrollableAncestorRects:a$,measuringConfiguration:av,measuringScheduled:ay,windowRect:aY}),[$,az,aD,ag,bc,aE,aH,T,W,aw,be,ax,aZ,a$,av,ay,aY]),bm=(0,o.useMemo)(()=>({activatorEvent:ag,activators:bk,active:$,activeNodeRect:aD,ariaDescribedById:{draggable:an},dispatch:K,draggableNodes:T,over:be,measureDroppableContainers:ax}),[ag,bk,$,aD,K,an,T,be,ax]);return p().createElement(R.Provider,{value:N},p().createElement(a_.Provider,{value:bm},p().createElement(a0.Provider,{value:bl},p().createElement(a4.Provider,{value:bg},v)),p().createElement(a3,{disabled:(null==s?void 0:s.restoreFocus)===!1})),p().createElement(U,{...s,hiddenTextDescribedById:an}))}),a6=(0,o.createContext)(null),a7="button",a8={timeout:25};function a9(a,b,c){let d=a.slice();return d.splice(c<0?d.length+c:c,0,d.splice(b,1)[0]),d}function ba(a){return null!==a&&a>=0}let bb=a=>{let{rects:b,activeIndex:c,overIndex:d,index:e}=a,f=a9(b,d,c),g=b[e],h=f[e];return h&&g?{x:h.left-g.left,y:h.top-g.top,scaleX:h.width/g.width,scaleY:h.height/g.height}:null},bc={scaleX:1,scaleY:1},bd=a=>{var b;let{activeIndex:c,activeNodeRect:d,index:e,rects:f,overIndex:g}=a,h=null!=(b=f[c])?b:d;if(!h)return null;if(e===c){let a=f[g];return a?{x:0,y:c<g?a.top+a.height-(h.top+h.height):a.top-h.top,...bc}:null}let i=function(a,b,c){let d=a[b],e=a[b-1],f=a[b+1];return d?c<b?e?d.top-(e.top+e.height):f?f.top-(d.top+d.height):0:f?f.top-(d.top+d.height):e?d.top-(e.top+e.height):0:0}(f,e,c);return e>c&&e<=g?{x:0,y:-h.height-i,...bc}:e<c&&e>=g?{x:0,y:h.height+i,...bc}:{x:0,y:0,...bc}},be="Sortable",bf=p().createContext({activeIndex:-1,containerId:be,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:bb,disabled:{draggable:!1,droppable:!1}});function bg(a){let{children:b,id:c,items:d,strategy:e=bb,disabled:f=!1}=a,{active:g,dragOverlay:h,droppableRects:i,over:j,measureDroppableContainers:k}=(0,o.useContext)(a0),l=G(be,c),m=null!==h.rect,n=(0,o.useMemo)(()=>d.map(a=>"object"==typeof a&&"id"in a?a.id:a),[d]),q=null!=g,r=g?n.indexOf(g.id):-1,s=j?n.indexOf(j.id):-1,t=(0,o.useRef)(n),u=!function(a,b){if(a===b)return!0;if(a.length!==b.length)return!1;for(let c=0;c<a.length;c++)if(a[c]!==b[c])return!1;return!0}(n,t.current),v=-1!==s&&-1===r||u,w="boolean"==typeof f?{draggable:f,droppable:f}:f;z(()=>{u&&q&&k(n)},[u,n,q,k]),(0,o.useEffect)(()=>{t.current=n},[n]);let x=(0,o.useMemo)(()=>({activeIndex:r,containerId:l,disabled:w,disableTransforms:v,items:n,overIndex:s,useDragOverlay:m,sortedRects:n.reduce((a,b,c)=>{let d=i.get(b);return d&&(a[c]=d),a},Array(n.length)),strategy:e}),[r,l,w.draggable,w.droppable,v,n,s,i,m,e]);return p().createElement(bf.Provider,{value:x},b)}let bh=a=>{let{id:b,items:c,activeIndex:d,overIndex:e}=a;return a9(c,d,e).indexOf(b)},bi=a=>{let{containerId:b,isSorting:c,wasDragging:d,index:e,items:f,newIndex:g,previousItems:h,previousContainerId:i,transition:j}=a;return!!j&&!!d&&(h===f||e!==g)&&(!!c||g!==e&&b===i)},bj={duration:200,easing:"ease"},bk="transform",bl=M.Transition.toString({property:bk,duration:0,easing:"linear"}),bm={roleDescription:"sortable"};function bn(a){if(!a)return!1;let b=a.data.current;return!!b&&"sortable"in b&&"object"==typeof b.sortable&&"containerId"in b.sortable&&"items"in b.sortable&&"index"in b.sortable}let bo=[g.Down,g.Right,g.Up,g.Left],bp=(a,b)=>{let{context:{active:c,collisionRect:d,droppableRects:e,droppableContainers:f,over:h,scrollableAncestors:i}}=b;if(bo.includes(a.code)){if(a.preventDefault(),!c||!d)return;let b=[];f.getEnabled().forEach(c=>{if(!c||null!=c&&c.disabled)return;let f=e.get(c.id);if(f)switch(a.code){case g.Down:d.top<f.top&&b.push(c);break;case g.Up:d.top>f.top&&b.push(c);break;case g.Left:d.left>f.left&&b.push(c);break;case g.Right:d.left<f.left&&b.push(c)}});let j=(a=>{let{collisionRect:b,droppableRects:c,droppableContainers:d}=a,e=_(b),f=[];for(let a of d){let{id:b}=a,d=c.get(b);if(d){let c=_(d),g=Number((e.reduce((a,b,d)=>a+Y(c[d],b),0)/4).toFixed(4));f.push({id:b,data:{droppableContainer:a,value:g}})}}return f.sort(Z)})({active:c,collisionRect:d,droppableRects:e,droppableContainers:b,pointerCoordinates:null}),k=aa(j,"id");if(k===(null==h?void 0:h.id)&&j.length>1&&(k=j[1].id),null!=k){let a=f.get(c.id),b=f.get(k),g=b?e.get(b.id):null,h=null==b?void 0:b.node.current;if(h&&g&&a&&b){let c=aj(h).some((a,b)=>i[b]!==a),e=bq(a,b),f=function(a,b){return!!bn(a)&&!!bn(b)&&!!bq(a,b)&&a.data.current.sortable.index<b.data.current.sortable.index}(a,b),j=c||!e?{x:0,y:0}:{x:f?d.width-g.width:0,y:f?d.height-g.height:0},k={x:g.left,y:g.top};return j.x&&j.y?k:J(k,j)}}}};function bq(a,b){return!!bn(a)&&!!bn(b)&&a.data.current.sortable.containerId===b.data.current.sortable.containerId}let br=a=>{let{transform:b}=a;return{...b,x:0}};var bs=c(24934),bt=c(32418),bu=c(14744),bv=c(89904),bw=c(59821),bx=c(44655);function by({category:a,onEdit:b,onDelete:c}){let{attributes:e,listeners:f,setNodeRef:g,transform:h,transition:i,isDragging:j}=function(a){var b,c,e,f;let{animateLayoutChanges:g=bi,attributes:h,disabled:i,data:j,getNewIndex:k=bh,id:l,strategy:m,resizeObserverConfig:n,transition:p=bj}=a,{items:q,containerId:r,activeIndex:s,disabled:t,disableTransforms:u,sortedRects:v,overIndex:w,useDragOverlay:x,strategy:y}=(0,o.useContext)(bf),A=(b=i,c=t,"boolean"==typeof b?{draggable:b,droppable:!1}:{draggable:null!=(e=null==b?void 0:b.draggable)?e:c.draggable,droppable:null!=(f=null==b?void 0:b.droppable)?f:c.droppable}),C=q.indexOf(l),E=(0,o.useMemo)(()=>({sortable:{containerId:r,index:C,items:q},...j}),[r,j,C,q]),F=(0,o.useMemo)(()=>q.slice(q.indexOf(l)),[q,l]),{rect:H,node:I,isOver:J,setNodeRef:L}=function(a){let{data:b,disabled:c=!1,id:e,resizeObserverConfig:f}=a,g=G("Droppable"),{active:h,dispatch:i,over:j,measureDroppableContainers:k}=(0,o.useContext)(a_),l=(0,o.useRef)({disabled:c}),m=(0,o.useRef)(!1),n=(0,o.useRef)(null),p=(0,o.useRef)(null),{disabled:q,updateMeasurementsFor:r,timeout:s}={...a8,...f},t=B(null!=r?r:e),u=aO({callback:(0,o.useCallback)(()=>{if(!m.current){m.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{k(Array.isArray(t.current)?t.current:[t.current]),p.current=null},s)},[s]),disabled:q||!h}),[v,w]=D((0,o.useCallback)((a,b)=>{u&&(b&&(u.unobserve(b),m.current=!1),a&&u.observe(a))},[u])),x=B(b);return(0,o.useEffect)(()=>{u&&v.current&&(u.disconnect(),m.current=!1,u.observe(v.current))},[v,u]),(0,o.useEffect)(()=>(i({type:d.RegisterDroppable,element:{id:e,key:g,disabled:c,node:v,rect:n,data:x}}),()=>i({type:d.UnregisterDroppable,key:g,id:e})),[e]),(0,o.useEffect)(()=>{c!==l.current.disabled&&(i({type:d.SetDroppableDisabled,id:e,key:g,disabled:c}),l.current.disabled=c)},[e,g,c,i]),{active:h,rect:n,isOver:(null==j?void 0:j.id)===e,node:v,over:j,setNodeRef:w}}({id:l,data:E,disabled:A.droppable,resizeObserverConfig:{updateMeasurementsFor:F,...n}}),{active:N,activatorEvent:O,activeNodeRect:P,attributes:Q,setNodeRef:R,listeners:S,isDragging:T,over:U,setActivatorNodeRef:V,transform:W}=function(a){let{id:b,data:c,disabled:d=!1,attributes:e}=a,f=G("Draggable"),{activators:g,activatorEvent:h,active:i,activeNodeRect:j,ariaDescribedById:k,draggableNodes:l,over:m}=(0,o.useContext)(a_),{role:n=a7,roleDescription:p="draggable",tabIndex:q=0}=null!=e?e:{},r=(null==i?void 0:i.id)===b,s=(0,o.useContext)(r?a4:a6),[t,u]=D(),[v,w]=D(),x=(0,o.useMemo)(()=>g.reduce((a,c)=>{let{eventName:d,handler:e}=c;return a[d]=a=>{e(a,b)},a},{}),[g,b]),y=B(c);return z(()=>(l.set(b,{id:b,key:f,node:t,activatorNode:v,data:y}),()=>{let a=l.get(b);a&&a.key===f&&l.delete(b)}),[l,b]),{active:i,activatorEvent:h,activeNodeRect:j,attributes:(0,o.useMemo)(()=>({role:n,tabIndex:q,"aria-disabled":d,"aria-pressed":!!r&&n===a7||void 0,"aria-roledescription":p,"aria-describedby":k.draggable}),[d,n,q,r,p,k.draggable]),isDragging:r,listeners:d?void 0:x,node:t,over:m,setNodeRef:u,setActivatorNodeRef:w,transform:s}}({id:l,data:E,attributes:{...bm,...h},disabled:A.draggable}),X=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return(0,o.useMemo)(()=>a=>{b.forEach(b=>b(a))},b)}(L,R),Y=!!N,Z=Y&&!u&&ba(s)&&ba(w),$=!x&&T,_=$&&Z?W:null,aa=Z?null!=_?_:(null!=m?m:y)({rects:v,activeNodeRect:P,activeIndex:s,overIndex:w,index:C}):null,ab=ba(s)&&ba(w)?k({id:l,items:q,activeIndex:s,overIndex:w}):C,ac=null==N?void 0:N.id,ad=(0,o.useRef)({activeId:ac,items:q,newIndex:ab,containerId:r}),ae=q!==ad.current.items,af=g({active:N,containerId:r,isDragging:T,isSorting:Y,id:l,index:C,items:q,newIndex:ad.current.newIndex,previousItems:ad.current.items,previousContainerId:ad.current.containerId,transition:p,wasDragging:null!=ad.current.activeId}),ag=function(a){let{disabled:b,index:c,node:d,rect:e}=a,[f,g]=(0,o.useState)(null),h=(0,o.useRef)(c);return z(()=>{if(!b&&c!==h.current&&d.current){let a=e.current;if(a){let b=ah(d.current,{ignoreTransform:!0}),c={x:a.left-b.left,y:a.top-b.top,scaleX:a.width/b.width,scaleY:a.height/b.height};(c.x||c.y)&&g(c)}}c!==h.current&&(h.current=c)},[b,c,d,e]),(0,o.useEffect)(()=>{f&&g(null)},[f]),f}({disabled:!af,index:C,node:I,rect:H});return(0,o.useEffect)(()=>{Y&&ad.current.newIndex!==ab&&(ad.current.newIndex=ab),r!==ad.current.containerId&&(ad.current.containerId=r),q!==ad.current.items&&(ad.current.items=q)},[Y,ab,r,q]),(0,o.useEffect)(()=>{if(ac===ad.current.activeId)return;if(null!=ac&&null==ad.current.activeId){ad.current.activeId=ac;return}let a=setTimeout(()=>{ad.current.activeId=ac},50);return()=>clearTimeout(a)},[ac]),{active:N,activeIndex:s,attributes:Q,data:E,rect:H,index:C,newIndex:ab,items:q,isOver:J,isSorting:Y,isDragging:T,listeners:S,node:I,overIndex:w,over:U,setNodeRef:X,setActivatorNodeRef:V,setDroppableNodeRef:L,setDraggableNodeRef:R,transform:null!=ag?ag:aa,transition:ag||ae&&ad.current.newIndex===C?bl:(!$||K(O))&&p&&(Y||af)?M.Transition.toString({...p,property:bk}):void 0}}({id:a.id}),k={transform:M.Transform.toString(h),transition:i};return(0,n.jsxs)("div",{ref:g,style:k,className:`
        group relative bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 
        rounded-lg p-4 transition-all duration-200
        ${j?"shadow-lg scale-105 z-10":"hover:shadow-md hover:border-gray-300 dark:hover:border-gray-500"}
      `,children:[(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)("div",{...e,...f,className:"cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600",children:(0,n.jsx)(bt.Icon,{name:"bars",className:"h-4 w-4 text-gray-400"})}),(0,n.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg",style:{backgroundColor:`${a.color}20`},children:(0,n.jsx)(bt.Icon,{name:a.icon||"folder",className:"h-5 w-5",color:a.color})}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,n.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white truncate",children:a.name}),(0,n.jsxs)(bw.E,{variant:"secondary",style:{backgroundColor:`${a.color}20`,color:a.color},children:[a.prompt_count," 个提示词"]})]}),a.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 line-clamp-2",children:a.description}),(0,n.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400",children:[(0,n.jsxs)("span",{children:["排序: ",a.sort_order]}),(0,n.jsxs)("span",{children:["创建于 ",(0,bx.Yq)(a.created_at)]}),a.updated_at!==a.created_at&&(0,n.jsxs)("span",{children:["更新于 ",(0,bx.Yq)(a.updated_at)]})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,n.jsx)(bs.$,{variant:"ghost",size:"sm",onClick:()=>b(a),className:"hover:bg-blue-100 hover:text-blue-600",children:(0,n.jsx)(bt.Icon,{name:"edit",className:"h-4 w-4"})}),(0,n.jsx)(bs.$,{variant:"ghost",size:"sm",onClick:()=>c(a),className:"hover:bg-red-100 hover:text-red-600",disabled:a.prompt_count>0,children:(0,n.jsx)(bt.Icon,{name:"trash",className:"h-4 w-4"})})]})]}),j&&(0,n.jsx)("div",{className:"absolute inset-0 bg-blue-100 dark:bg-blue-900/20 rounded-lg border-2 border-blue-300 dark:border-blue-600"})]})}var bz=c(32945),bA=c(71702),bB=c(34257),bC=c(22392);function bD(){let[a,b]=(0,o.useState)([]),[c,d]=(0,o.useState)(!0),[e,f]=(0,o.useState)(!1),[g,h]=(0,o.useState)(null),[i,j]=(0,o.useState)(!1),[k,l]=(0,o.useState)(null),[m,p]=(0,o.useState)(!1),{toast:q}=(0,bA.dj)(),r=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return(0,o.useMemo)(()=>[...b].filter(a=>null!=a),[...b])}(W(aG),W(aB,{coordinateGetter:bp})),s=async()=>{try{d(!0),console.log("\uD83D\uDE80 使用本地优先存储加载分类");let a=await bC.J.getCategories();b(a),console.log(`✅ 加载了 ${a.length} 个分类`)}catch(a){console.error("加载分类失败:",a),q({title:"加载失败",description:"无法加载分类列表",variant:"destructive"})}finally{d(!1)}},t=()=>{h(null),f(!0)},u=a=>{h(a),f(!0)},v=async a=>{try{let b=await (0,bB.MN)(a.id);if(b>0)return void q({title:"无法删除",description:`该分类下还有 ${b} 个提示词，请先移动或删除这些提示词`,variant:"destructive"})}catch(a){console.error("检查分类提示词数量失败:",a)}l(a),j(!0)},w=async()=>{if(k)try{if(p(!0),console.log("\uD83D\uDE80 使用本地优先删除分类"),await bC.J.deleteCategory(k.id)){b(a=>a.filter(a=>a.id!==k.id)),q({title:"删除成功",description:"分类已成功删除"}),j(!1),l(null),console.log("✅ 分类已从本地状态中移除");let a=bC.J.getDetailedSyncStatus();console.log("\uD83D\uDD0D 删除后同步状态:",a)}else throw Error("删除失败")}catch(a){console.error("删除分类失败:",a),q({title:"删除失败",description:"删除分类时出现错误",variant:"destructive"})}finally{p(!1)}},x=async c=>{let{active:d,over:e}=c;if(d.id!==e?.id){let c=a.findIndex(a=>a.id===d.id),f=a.findIndex(a=>a.id===e.id),g=a9(a,c,f);b(g);try{let a=g.map(a=>a.id);await (0,bB.wp)(a),q({title:"排序已更新",description:"分类排序已成功保存"})}catch(c){console.error("更新分类排序失败:",c),b(a),q({title:"排序失败",description:"更新分类排序时出现错误",variant:"destructive"})}}};return c?(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(bt.Icon,{name:"spinner",className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"加载中..."})]})}):(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,n.jsx)(bz.a,{}),(0,n.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white",children:"分类管理"}),(0,n.jsx)("p",{className:"text-muted-foreground mt-2",children:"管理您的提示词分类，支持拖拽排序"})]}),(0,n.jsxs)(bs.$,{onClick:t,className:"w-full sm:w-auto",children:[(0,n.jsx)(bt.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),"新建分类"]})]}),a.length>0?(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("h2",{className:"text-lg font-semibold",children:["分类列表 (",a.length,")"]}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"拖拽可调整排序"})]}),(0,n.jsx)(a5,{sensors:r,collisionDetection:ac,onDragEnd:x,modifiers:[br],children:(0,n.jsx)(bg,{items:a.map(a=>a.id),strategy:bd,children:(0,n.jsx)("div",{className:"space-y-2",children:a.map(a=>(0,n.jsx)(by,{category:a,onEdit:u,onDelete:v},a.id))})})})]})}):(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)(bt.Icon,{name:"folder",className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"暂无分类"}),(0,n.jsx)("p",{className:"text-muted-foreground mb-4",children:"创建您的第一个分类来组织提示词"}),(0,n.jsxs)(bs.$,{onClick:t,children:[(0,n.jsx)(bt.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),"创建分类"]})]}),(0,n.jsx)("div",{className:"mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-start gap-3",children:[(0,n.jsx)(bt.Icon,{name:"info-circle",className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium text-blue-900 dark:text-blue-100 mb-1",children:"使用说明"}),(0,n.jsxs)("ul",{className:"text-sm text-blue-700 dark:text-blue-200 space-y-1",children:[(0,n.jsx)("li",{children:"• 拖拽分类项可以调整显示顺序"}),(0,n.jsx)("li",{children:"• 删除分类前需要先移动或删除该分类下的所有提示词"}),(0,n.jsx)("li",{children:"• 分类颜色和图标会在侧边栏中显示"}),(0,n.jsx)("li",{children:"• 分类名称在同一用户下必须唯一"})]})]})]})})]}),(0,n.jsx)(bu.K,{category:g,isOpen:e,onClose:()=>{f(!1),h(null)},onSuccess:()=>{s()}}),(0,n.jsx)(bv.o,{isOpen:i,onClose:()=>{j(!1),l(null)},onConfirm:w,title:"删除分类",description:"此操作无法撤销，确定要删除这个分类吗？",itemName:k?.name,isLoading:m})]})}},26134:(a,b,c)=>{"use strict";c.d(b,{UC:()=>ac,VY:()=>ae,ZL:()=>aa,bL:()=>$,bm:()=>af,hE:()=>ad,hJ:()=>ab,l9:()=>_});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(96963),i=c(65551),j=c(31355),k=c(32547),l=c(25028),m=c(46059),n=c(14163),o=c(1359),p=c(42247),q=c(63376),r=c(8730),s=c(60687),t="Dialog",[u,v]=(0,g.A)(t),[w,x]=u(t),y=a=>{let{__scopeDialog:b,children:c,open:e,defaultOpen:f,onOpenChange:g,modal:j=!0}=a,k=d.useRef(null),l=d.useRef(null),[m,n]=(0,i.i)({prop:e,defaultProp:f??!1,onChange:g,caller:t});return(0,s.jsx)(w,{scope:b,triggerRef:k,contentRef:l,contentId:(0,h.B)(),titleId:(0,h.B)(),descriptionId:(0,h.B)(),open:m,onOpenChange:n,onOpenToggle:d.useCallback(()=>n(a=>!a),[n]),modal:j,children:c})};y.displayName=t;var z="DialogTrigger",A=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,g=x(z,c),h=(0,f.s)(b,g.triggerRef);return(0,s.jsx)(n.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":g.open,"aria-controls":g.contentId,"data-state":U(g.open),...d,ref:h,onClick:(0,e.m)(a.onClick,g.onOpenToggle)})});A.displayName=z;var B="DialogPortal",[C,D]=u(B,{forceMount:void 0}),E=a=>{let{__scopeDialog:b,forceMount:c,children:e,container:f}=a,g=x(B,b);return(0,s.jsx)(C,{scope:b,forceMount:c,children:d.Children.map(e,a=>(0,s.jsx)(m.C,{present:c||g.open,children:(0,s.jsx)(l.Z,{asChild:!0,container:f,children:a})}))})};E.displayName=B;var F="DialogOverlay",G=d.forwardRef((a,b)=>{let c=D(F,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=x(F,a.__scopeDialog);return f.modal?(0,s.jsx)(m.C,{present:d||f.open,children:(0,s.jsx)(I,{...e,ref:b})}):null});G.displayName=F;var H=(0,r.TL)("DialogOverlay.RemoveScroll"),I=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(F,c);return(0,s.jsx)(p.A,{as:H,allowPinchZoom:!0,shards:[e.contentRef],children:(0,s.jsx)(n.sG.div,{"data-state":U(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),J="DialogContent",K=d.forwardRef((a,b)=>{let c=D(J,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=x(J,a.__scopeDialog);return(0,s.jsx)(m.C,{present:d||f.open,children:f.modal?(0,s.jsx)(L,{...e,ref:b}):(0,s.jsx)(M,{...e,ref:b})})});K.displayName=J;var L=d.forwardRef((a,b)=>{let c=x(J,a.__scopeDialog),g=d.useRef(null),h=(0,f.s)(b,c.contentRef,g);return d.useEffect(()=>{let a=g.current;if(a)return(0,q.Eq)(a)},[]),(0,s.jsx)(N,{...a,ref:h,trapFocus:c.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,e.m)(a.onCloseAutoFocus,a=>{a.preventDefault(),c.triggerRef.current?.focus()}),onPointerDownOutside:(0,e.m)(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:(0,e.m)(a.onFocusOutside,a=>a.preventDefault())})}),M=d.forwardRef((a,b)=>{let c=x(J,a.__scopeDialog),e=d.useRef(!1),f=d.useRef(!1);return(0,s.jsx)(N,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(e.current||c.triggerRef.current?.focus(),b.preventDefault()),e.current=!1,f.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(e.current=!0,"pointerdown"===b.detail.originalEvent.type&&(f.current=!0));let d=b.target;c.triggerRef.current?.contains(d)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&f.current&&b.preventDefault()}})}),N=d.forwardRef((a,b)=>{let{__scopeDialog:c,trapFocus:e,onOpenAutoFocus:g,onCloseAutoFocus:h,...i}=a,l=x(J,c),m=d.useRef(null),n=(0,f.s)(b,m);return(0,o.Oh)(),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k.n,{asChild:!0,loop:!0,trapped:e,onMountAutoFocus:g,onUnmountAutoFocus:h,children:(0,s.jsx)(j.qW,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":U(l.open),...i,ref:n,onDismiss:()=>l.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(Y,{titleId:l.titleId}),(0,s.jsx)(Z,{contentRef:m,descriptionId:l.descriptionId})]})]})}),O="DialogTitle",P=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(O,c);return(0,s.jsx)(n.sG.h2,{id:e.titleId,...d,ref:b})});P.displayName=O;var Q="DialogDescription",R=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(Q,c);return(0,s.jsx)(n.sG.p,{id:e.descriptionId,...d,ref:b})});R.displayName=Q;var S="DialogClose",T=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,f=x(S,c);return(0,s.jsx)(n.sG.button,{type:"button",...d,ref:b,onClick:(0,e.m)(a.onClick,()=>f.onOpenChange(!1))})});function U(a){return a?"open":"closed"}T.displayName=S;var V="DialogTitleWarning",[W,X]=(0,g.q)(V,{contentName:J,titleName:O,docsSlug:"dialog"}),Y=({titleId:a})=>{let b=X(V),c=`\`${b.contentName}\` requires a \`${b.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${b.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${b.docsSlug}`;return d.useEffect(()=>{a&&(document.getElementById(a)||console.error(c))},[c,a]),null},Z=({contentRef:a,descriptionId:b})=>{let c=X("DialogDescriptionWarning"),e=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${c.contentName}}.`;return d.useEffect(()=>{let c=a.current?.getAttribute("aria-describedby");b&&c&&(document.getElementById(b)||console.warn(e))},[e,a,b]),null},$=y,_=A,aa=E,ab=G,ac=K,ad=P,ae=R,af=T},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},26925:(a,b,c)=>{Promise.resolve().then(c.bind(c,25020))},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63309:(a,b,c)=>{Promise.resolve().then(c.bind(c,9725))},74075:a=>{"use strict";a.exports=require("zlib")},78148:(a,b,c)=>{"use strict";c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96974:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,9725)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\categories\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,38836)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]}.children,H=["D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\categories\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/categories/page",pathname:"/dashboard/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/categories/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,519,522,814,884,121,780,568,796],()=>b(b.s=96974));module.exports=c})();