// Font Awesome 配置
import { library } from '@fortawesome/fontawesome-svg-core'
import {
  faFolder,
  faFolderOpen,
  faCode,
  faPenToSquare,
  faBullhorn,
  faRocket,
  faGraduationCap,
  faSearch,
  faPlus,
  faCopy,
  faEdit,
  faTrash,
  faEye,
  faTags,
  faHeart,
  faShare,
  faDownload,
  faUpload,
  faStar,
  faBookmark,
  faFilter,
  faSortAmountDown,
  faSortAmountUp,
  faThLarge as faGrid,
  faList,
  faCog,
  faUser,
  faSignOutAlt,
  faHome,
  faChevronDown,
  faChevronUp,
  faChevronLeft,
  faChevronRight,
  faTimes,
  faCheck,
  faExclamationTriangle,
  faInfoCircle,
  faQuestionCircle,
  faBars,
  faEllipsisV,
  faSpinner,
  faRefresh,
  faSave,
  faUndo,
  faRedo,
  faExpand,
  faCompress,
  faExternalLinkAlt,
  faClipboard,
  faClipboardCheck,
  faFileAlt as faMarkdown,
  faFileCode,
  faFileText,
  faImage,
  faVideo,
  faMusic,
  faFile,
  faCalendar,
  faClock,
  faHashtag,
  faAt,
  faLink,
  faGlobe,
  faLock,
  faUnlock,
  faShield,
  faDatabase,
  faServer,
  faCloud,
  faDesktop,
  faMobile,
  faTablet,
  faLaptop,
  faPalette,
  faPaintBrush,
  faMagic,
  faLightbulb,
  faZap as faFlash,
  faBolt,
  faFire,
  faGem,
  faCrown,
  faTrophy,
  faMedal,
  faAward,
  faBullseye,
  faFlag,
  faMapMarkerAlt as faMapMarker,
  faCompass,
  faRoute,
  faMap,
  faChartBar,
  faThLarge,
  faFileAlt,
  faZap,
  faMapMarkerAlt
} from '@fortawesome/free-solid-svg-icons'

// 添加图标到库中
library.add(
  faFolder,
  faFolderOpen,
  faCode,
  faPenToSquare,
  faBullhorn,
  faRocket,
  faGraduationCap,
  faSearch,
  faPlus,
  faCopy,
  faEdit,
  faTrash,
  faEye,
  faTags,
  faHeart,
  faShare,
  faDownload,
  faUpload,
  faStar,
  faBookmark,
  faFilter,
  faSortAmountDown,
  faSortAmountUp,
  faGrid,
  faList,
  faCog,
  faUser,
  faSignOutAlt,
  faHome,
  faChevronDown,
  faChevronUp,
  faChevronLeft,
  faChevronRight,
  faTimes,
  faCheck,
  faExclamationTriangle,
  faInfoCircle,
  faQuestionCircle,
  faBars,
  faEllipsisV,
  faSpinner,
  faRefresh,
  faSave,
  faUndo,
  faRedo,
  faExpand,
  faCompress,
  faExternalLinkAlt,
  faClipboard,
  faClipboardCheck,
  faMarkdown,
  faFileCode,
  faFileText,
  faImage,
  faVideo,
  faMusic,
  faFile,
  faCalendar,
  faClock,
  faHashtag,
  faAt,
  faLink,
  faGlobe,
  faLock,
  faUnlock,
  faShield,
  faDatabase,
  faServer,
  faCloud,
  faDesktop,
  faMobile,
  faTablet,
  faLaptop,
  faPalette,
  faPaintBrush,
  faMagic,
  faLightbulb,
  faFlash,
  faBolt,
  faFire,
  faGem,
  faCrown,
  faTrophy,
  faMedal,
  faAward,
  faBullseye,
  faFlag,
  faMapMarker,
  faCompass,
  faRoute,
  faMap,
  faChartBar
)

// 导出常用图标名称映射
export const iconMap = {
  // 分类图标
  folder: 'folder',
  'folder-open': 'folder-open',
  code: 'code',
  'pen-to-square': 'pen-to-square',
  bullhorn: 'bullhorn',
  rocket: 'rocket',
  'graduation-cap': 'graduation-cap',
  
  // 操作图标
  search: 'search',
  plus: 'plus',
  copy: 'copy',
  edit: 'edit',
  trash: 'trash',
  eye: 'eye',
  tags: 'tags',
  heart: 'heart',
  share: 'share',
  download: 'download',
  upload: 'upload',
  star: 'star',
  bookmark: 'bookmark',
  
  // 界面图标
  filter: 'filter',
  'sort-amount-down': 'sort-amount-down',
  'sort-amount-up': 'sort-amount-up',
  grid: 'grid',
  list: 'list',
  cog: 'cog',
  user: 'user',
  'sign-out-alt': 'sign-out-alt',
  home: 'home',
  
  // 导航图标
  'chevron-down': 'chevron-down',
  'chevron-up': 'chevron-up',
  'chevron-left': 'chevron-left',
  'chevron-right': 'chevron-right',
  times: 'times',
  check: 'check',
  
  // 状态图标
  'exclamation-triangle': 'exclamation-triangle',
  'info-circle': 'info-circle',
  'question-circle': 'question-circle',
  bars: 'bars',
  'ellipsis-v': 'ellipsis-v',
  spinner: 'spinner',
  refresh: 'refresh',
  
  // 编辑图标
  save: 'save',
  undo: 'undo',
  redo: 'redo',
  expand: 'expand',
  compress: 'compress',
  'external-link-alt': 'external-link-alt',
  clipboard: 'clipboard',
  'clipboard-check': 'clipboard-check',
  
  // 文件类型图标
  markdown: 'markdown',
  'file-code': 'file-code',
  'file-text': 'file-text',
  image: 'image',
  video: 'video',
  music: 'music',
  file: 'file',
  
  // 时间图标
  calendar: 'calendar',
  clock: 'clock',
  
  // 社交图标
  hashtag: 'hashtag',
  at: 'at',
  link: 'link',
  globe: 'globe',
  
  // 安全图标
  lock: 'lock',
  unlock: 'unlock',
  shield: 'shield',
  
  // 技术图标
  database: 'database',
  server: 'server',
  cloud: 'cloud',
  desktop: 'desktop',
  mobile: 'mobile',
  tablet: 'tablet',
  laptop: 'laptop',
  
  // 设计图标
  palette: 'palette',
  'paint-brush': 'paint-brush',
  magic: 'magic',
  
  // 创意图标
  lightbulb: 'lightbulb',
  flash: 'flash',
  bolt: 'bolt',
  fire: 'fire',
  gem: 'gem',
  
  // 成就图标
  crown: 'crown',
  trophy: 'trophy',
  medal: 'medal',
  award: 'award',
  bullseye: 'bullseye',
  
  // 位置图标
  flag: 'flag',
  'map-marker': 'map-marker',
  compass: 'compass',
  route: 'route',
  map: 'map',

  // 图表图标
  chart: 'chart-bar'
} as const

export type IconName = keyof typeof iconMap
