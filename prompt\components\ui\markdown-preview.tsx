"use client"

import ReactMarkdown from 'react-markdown'
import { CodeBlock } from './code-block'

interface MarkdownPreviewProps {
  content: string
  className?: string
  truncate?: boolean
  maxLines?: number
}

export function MarkdownPreview({ 
  content, 
  className = "", 
  truncate = false, 
  maxLines = 3 
}: MarkdownPreviewProps) {
  return (
    <div className={`prose prose-sm max-w-none dark:prose-invert ${className}`}>
      <ReactMarkdown
        components={{
          // 简化的代码块渲染（卡片预览中不显示复制按钮）
          code({ node, inline, className, children, ...props }) {
            if (inline) {
              return (
                <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold">
                  {children}
                </code>
              )
            }
            
            // 在卡片预览中，代码块使用简化样式
            if (truncate) {
              return (
                <pre className="bg-muted rounded p-2 text-xs overflow-hidden">
                  <code>{String(children).replace(/\n$/, '')}</code>
                </pre>
              )
            }
            
            // 完整视图中使用带复制功能的代码块
            return (
              <CodeBlock
                inline={inline}
                className={className}
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </CodeBlock>
            )
          },
          
          // 简化其他元素的渲染
          h1: ({ children }) => truncate ? 
            <span className="font-bold text-base">{children}</span> : 
            <h1 className="text-xl font-bold mb-2">{children}</h1>,
          h2: ({ children }) => truncate ? 
            <span className="font-semibold text-sm">{children}</span> : 
            <h2 className="text-lg font-semibold mb-2">{children}</h2>,
          h3: ({ children }) => truncate ? 
            <span className="font-medium text-sm">{children}</span> : 
            <h3 className="text-base font-medium mb-1">{children}</h3>,
          
          p: ({ children }) => truncate ? 
            <span className="inline">{children} </span> : 
            <p className="mb-2">{children}</p>,
          
          ul: ({ children }) => truncate ? 
            <span className="inline">{children}</span> : 
            <ul className="list-disc list-inside mb-2">{children}</ul>,
          
          ol: ({ children }) => truncate ? 
            <span className="inline">{children}</span> : 
            <ol className="list-decimal list-inside mb-2">{children}</ol>,
          
          li: ({ children }) => truncate ? 
            <span className="inline">• {children} </span> : 
            <li className="mb-1">{children}</li>,
          
          strong: ({ children }) => <strong className="font-semibold">{children}</strong>,
          em: ({ children }) => <em className="italic">{children}</em>,
          
          blockquote: ({ children }) => truncate ? 
            <span className="inline italic">{children}</span> : 
            <blockquote className="border-l-4 border-muted pl-4 italic mb-2">{children}</blockquote>,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}
