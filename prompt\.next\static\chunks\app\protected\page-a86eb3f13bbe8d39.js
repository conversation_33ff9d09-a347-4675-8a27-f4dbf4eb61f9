(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[379],{5196:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},11275:(e,r,t)=>{"use strict";t.d(r,{X:()=>i});var n=t(12115),s=t(52712);function i(e){let[r,t]=n.useState(void 0);return(0,s.N)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let r=new ResizeObserver(r=>{let n,s;if(!Array.isArray(r)||!r.length)return;let i=r[0];if("borderBoxSize"in i){let e=i.borderBoxSize,r=Array.isArray(e)?e[0]:e;n=r.inlineSize,s=r.blockSize}else n=e.offsetWidth,s=e.offsetHeight;t({width:n,height:s})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}t(void 0)},[e]),r}},31611:(e,r,t)=>{"use strict";t.d(r,{CodeBlock:()=>d});var n=t(95155),s=t(12115),i=t(97168);let o=()=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,n.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]}),a=()=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,n.jsx)("polyline",{points:"20 6 9 17 4 12"})});function d(e){let{code:r}=e,[t,d]=(0,s.useState)(o),l=async()=>{var e,t;await (null==(t=navigator)||null==(e=t.clipboard)?void 0:e.writeText(r)),d(a),setTimeout(()=>d(o),2e3)};return(0,n.jsxs)("pre",{className:"bg-muted rounded-md p-6 my-6 relative",children:[(0,n.jsx)(i.$,{size:"icon",onClick:l,variant:"outline",className:"absolute right-2 top-2",children:t}),(0,n.jsx)("code",{className:"text-xs p-3",children:r})]})}},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var n=t(52596),s=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,n.$)(r))}},59986:(e,r,t)=>{Promise.resolve().then(t.bind(t,31611)),Promise.resolve().then(t.bind(t,96453))},96453:(e,r,t)=>{"use strict";t.d(r,{Checkbox:()=>z});var n=t(95155),s=t(12115),i=t(6101),o=t(46081),a=t(85185),d=t(5845),l=t(11275),c=t(28905),u=t(63655),h="Checkbox",[f,p]=(0,o.A)(h),[v,b]=f(h);function x(e){let{__scopeCheckbox:r,checked:t,children:i,defaultChecked:o,disabled:a,form:l,name:c,onCheckedChange:u,required:f,value:p="on",internal_do_not_use_render:b}=e,[x,m]=(0,d.i)({prop:t,defaultProp:null!=o&&o,onChange:u,caller:h}),[g,y]=s.useState(null),[k,w]=s.useState(null),j=s.useRef(!1),N=!g||!!l||!!g.closest("form"),E={checked:x,disabled:a,setChecked:m,control:g,setControl:y,name:c,form:l,value:p,hasConsumerStoppedPropagationRef:j,required:f,defaultChecked:!C(o)&&o,isFormControl:N,bubbleInput:k,setBubbleInput:w};return(0,n.jsx)(v,{scope:r,...E,children:"function"==typeof b?b(E):i})}var m="CheckboxTrigger",g=s.forwardRef((e,r)=>{let{__scopeCheckbox:t,onKeyDown:o,onClick:d,...l}=e,{control:c,value:h,disabled:f,checked:p,required:v,setControl:x,setChecked:g,hasConsumerStoppedPropagationRef:y,isFormControl:k,bubbleInput:w}=b(m,t),j=(0,i.s)(r,x),N=s.useRef(p);return s.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let r=()=>g(N.current);return e.addEventListener("reset",r),()=>e.removeEventListener("reset",r)}},[c,g]),(0,n.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":C(p)?"mixed":p,"aria-required":v,"data-state":E(p),"data-disabled":f?"":void 0,disabled:f,value:h,...l,ref:j,onKeyDown:(0,a.m)(o,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.m)(d,e=>{g(e=>!!C(e)||!e),w&&k&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});g.displayName=m;var y=s.forwardRef((e,r)=>{let{__scopeCheckbox:t,name:s,checked:i,defaultChecked:o,required:a,disabled:d,value:l,onCheckedChange:c,form:u,...h}=e;return(0,n.jsx)(x,{__scopeCheckbox:t,checked:i,defaultChecked:o,disabled:d,required:a,onCheckedChange:c,name:s,form:u,value:l,internal_do_not_use_render:e=>{let{isFormControl:s}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(g,{...h,ref:r,__scopeCheckbox:t}),s&&(0,n.jsx)(N,{__scopeCheckbox:t})]})}})});y.displayName=h;var k="CheckboxIndicator",w=s.forwardRef((e,r)=>{let{__scopeCheckbox:t,forceMount:s,...i}=e,o=b(k,t);return(0,n.jsx)(c.C,{present:s||C(o.checked)||!0===o.checked,children:(0,n.jsx)(u.sG.span,{"data-state":E(o.checked),"data-disabled":o.disabled?"":void 0,...i,ref:r,style:{pointerEvents:"none",...e.style}})})});w.displayName=k;var j="CheckboxBubbleInput",N=s.forwardRef((e,r)=>{let{__scopeCheckbox:t,...o}=e,{control:a,hasConsumerStoppedPropagationRef:d,checked:c,defaultChecked:h,required:f,disabled:p,name:v,value:x,form:m,bubbleInput:g,setBubbleInput:y}=b(j,t),k=(0,i.s)(r,y),w=function(e){let r=s.useRef({value:e,previous:e});return s.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}(c),N=(0,l.X)(a);s.useEffect(()=>{if(!g)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,r=!d.current;if(w!==c&&e){let t=new Event("click",{bubbles:r});g.indeterminate=C(c),e.call(g,!C(c)&&c),g.dispatchEvent(t)}},[g,w,c,d]);let E=s.useRef(!C(c)&&c);return(0,n.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=h?h:E.current,required:f,disabled:p,name:v,value:x,form:m,...o,tabIndex:-1,ref:k,style:{...o.style,...N,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function E(e){return C(e)?"indeterminate":e?"checked":"unchecked"}N.displayName=j;var _=t(5196),R=t(53999);let z=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(y,{ref:r,className:(0,R.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...s,children:(0,n.jsx)(w,{className:(0,R.cn)("flex items-center justify-center text-current"),children:(0,n.jsx)(_.A,{className:"h-4 w-4"})})})});z.displayName=y.displayName},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var n=t(95155),s=t(12115),i=t(99708),o=t(74466),a=t(53999);let d=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,r)=>{let{className:t,variant:s,size:o,asChild:l=!1,...c}=e,u=l?i.DX:"button";return(0,n.jsx)(u,{className:(0,a.cn)(d({variant:s,size:o,className:t})),ref:r,...c})});l.displayName="Button"}},e=>{e.O(0,[352,576,441,964,358],()=>e(e.s=59986)),_N_E=e.O()}]);