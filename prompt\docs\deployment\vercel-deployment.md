# 🚀 Vercel 部署指南

本指南将详细介绍如何将提示词管理工具部署到 Vercel 平台。

## 📋 部署前准备

### 1. 环境要求
- ✅ GitHub/GitLab/Bitbucket 账户
- ✅ Vercel 账户
- ✅ Supabase 项目已配置
- ✅ 项目代码已推送到代码仓库

### 2. 必要文件检查
确保项目包含以下文件：
- ✅ `package.json` - 依赖和脚本配置
- ✅ `next.config.ts` - Next.js 配置
- ✅ `vercel.json` - Vercel 部署配置
- ✅ `.env.example` - 环境变量示例

## 🔧 Vercel 配置

### 1. vercel.json 配置说明

```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "installCommand": "npm install",
  "devCommand": "npm run dev",
  "regions": ["hkg1", "sin1", "nrt1"],
  "functions": {
    "app/**/*.tsx": {
      "runtime": "nodejs18.x",
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options", 
          "value": "DENY"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/",
      "destination": "/dashboard",
      "permanent": false
    }
  ]
}
```

### 2. 配置项说明

#### **基础配置**
- `framework`: 指定为 Next.js 框架
- `buildCommand`: 构建命令
- `outputDirectory`: 输出目录
- `regions`: 部署区域（亚太地区优化）

#### **函数配置**
- `runtime`: Node.js 18.x 运行时
- `maxDuration`: 最大执行时间 30 秒

#### **安全头部**
- `X-Content-Type-Options`: 防止 MIME 类型嗅探
- `X-Frame-Options`: 防止点击劫持
- `X-XSS-Protection`: XSS 保护
- `Referrer-Policy`: 引用策略

## 🚀 部署步骤

### 方法一：通过 Vercel Dashboard

#### 1. 连接代码仓库
1. 访问 [Vercel Dashboard](https://vercel.com/dashboard)
2. 点击 "New Project"
3. 选择 Git 提供商（GitHub/GitLab/Bitbucket）
4. 授权 Vercel 访问您的仓库
5. 选择项目仓库

#### 2. 配置项目设置
```bash
# 项目设置
Framework Preset: Next.js
Root Directory: ./prompt
Build Command: npm run build
Output Directory: .next
Install Command: npm install
Development Command: npm run dev
```

#### 3. 配置环境变量
在 Vercel Dashboard 中添加环境变量：

```bash
# 必需的环境变量
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-anon-key

# 可选的环境变量
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-app.vercel.app
```

#### 4. 部署项目
1. 点击 "Deploy" 按钮
2. 等待构建完成（通常 2-5 分钟）
3. 部署成功后获得访问链接

### 方法二：通过 Vercel CLI

#### 1. 安装 Vercel CLI
```bash
npm install -g vercel
```

#### 2. 登录 Vercel
```bash
vercel login
```

#### 3. 初始化项目
```bash
cd prompt
vercel
```

#### 4. 配置环境变量
```bash
# 添加生产环境变量
vercel env add NEXT_PUBLIC_SUPABASE_URL production
vercel env add NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY production

# 添加预览环境变量
vercel env add NEXT_PUBLIC_SUPABASE_URL preview
vercel env add NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY preview
```

#### 5. 部署到生产环境
```bash
vercel --prod
```

## 🔧 环境变量配置

### 1. 必需的环境变量

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase 项目 URL | `https://xxx.supabase.co` |
| `NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY` | Supabase 公开密钥 | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |

### 2. 可选的环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `production` |
| `NEXT_PUBLIC_APP_URL` | 应用 URL | Vercel 自动生成 |

### 3. 环境变量设置方法

#### **通过 Dashboard**
1. 进入项目设置页面
2. 点击 "Environment Variables"
3. 添加变量名和值
4. 选择环境（Production/Preview/Development）

#### **通过 CLI**
```bash
# 交互式添加
vercel env add

# 直接添加
vercel env add VARIABLE_NAME production
```

## 🔍 部署验证

### 1. 构建检查
确保构建过程无错误：
```bash
# 本地构建测试
npm run build
npm run start
```

### 2. 功能测试
部署后测试关键功能：
- ✅ 用户注册/登录
- ✅ 提示词 CRUD 操作
- ✅ 搜索功能
- ✅ 分类管理
- ✅ 复制历史

### 3. 性能检查
使用工具检查性能：
- [Lighthouse](https://developers.google.com/web/tools/lighthouse)
- [WebPageTest](https://www.webpagetest.org/)
- [GTmetrix](https://gtmetrix.com/)

## 🔧 常见问题解决

### 1. 构建失败

#### **依赖问题**
```bash
# 清理依赖重新安装
rm -rf node_modules package-lock.json
npm install
```

#### **TypeScript 错误**
```bash
# 检查类型错误
npm run type-check
```

#### **环境变量缺失**
确保所有必需的环境变量都已配置。

### 2. 运行时错误

#### **Supabase 连接失败**
- 检查 Supabase URL 和密钥是否正确
- 确认 Supabase 项目状态正常
- 检查网络连接

#### **认证问题**
- 确认 Supabase Auth 配置正确
- 检查重定向 URL 设置
- 验证 RLS 策略

### 3. 性能问题

#### **加载缓慢**
- 启用 Vercel Analytics
- 优化图片和资源
- 检查数据库查询性能

#### **内存不足**
- 优化组件渲染
- 减少内存泄漏
- 使用 React.memo 优化

## 📊 监控和维护

### 1. Vercel Analytics
启用 Vercel Analytics 监控：
```bash
npm install @vercel/analytics
```

```typescript
// app/layout.tsx
import { Analytics } from '@vercel/analytics/react'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  )
}
```

### 2. 错误监控
配置错误监控服务：
- Sentry
- LogRocket
- Bugsnag

### 3. 性能监控
- Core Web Vitals
- Real User Monitoring
- Synthetic Monitoring

## 🔄 持续部署

### 1. 自动部署
Vercel 支持 Git 集成自动部署：
- 推送到 `main` 分支 → 生产部署
- 推送到其他分支 → 预览部署
- Pull Request → 预览部署

### 2. 部署钩子
配置部署钩子：
```bash
# 部署前钩子
vercel env add DEPLOY_HOOK_URL production
```

### 3. 回滚策略
如果部署出现问题：
1. 在 Vercel Dashboard 中回滚到上一个版本
2. 或者通过 CLI 回滚：
```bash
vercel rollback
```

## 🎯 最佳实践

### 1. 分支策略
- `main`: 生产环境
- `develop`: 开发环境
- `feature/*`: 功能分支

### 2. 环境管理
- 生产环境：稳定版本
- 预览环境：测试版本
- 开发环境：开发版本

### 3. 安全考虑
- 定期更新依赖
- 使用安全头部
- 启用 HTTPS
- 配置 CSP 策略

通过遵循这个部署指南，您可以成功将提示词管理工具部署到 Vercel，并确保应用的稳定运行。
