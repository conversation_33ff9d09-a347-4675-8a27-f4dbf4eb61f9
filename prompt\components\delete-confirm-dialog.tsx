"use client"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'

interface DeleteConfirmDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  description: string
  itemName?: string
  isLoading?: boolean
}

export function DeleteConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  itemName,
  isLoading = false
}: DeleteConfirmDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-red-100 rounded-full">
              <Icon name="exclamation-triangle" className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold">
                {title}
              </DialogTitle>
              <DialogDescription className="mt-1">
                {description}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {itemName && (
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 my-4">
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              即将删除：
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {itemName}
            </p>
          </div>
        )}

        <div className="flex items-center justify-end gap-2 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Icon name="spinner" className="h-4 w-4 mr-2 animate-spin" />
                删除中...
              </>
            ) : (
              <>
                <Icon name="trash" className="h-4 w-4 mr-2" />
                确认删除
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
