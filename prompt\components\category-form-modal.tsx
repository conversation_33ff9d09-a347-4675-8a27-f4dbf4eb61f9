"use client"

import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Icon, IconName } from '@/components/ui/icon'
import { useToast } from '@/hooks/use-toast'
import {
  createCategory,
  updateCategory,
  checkCategoryNameExists
} from '@/lib/database'
import { localFirstStore } from '@/lib/local-first-store'
import { generateRandomColor, isValidColor } from '@/lib/utils/format'
import type { 
  Category, 
  CategoryInsert,
  CategoryUpdate 
} from '@/types/database'

interface CategoryFormModalProps {
  category?: Category | null
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

// 可选的图标列表
const availableIcons: { name: IconName; label: string }[] = [
  { name: 'folder', label: '文件夹' },
  { name: 'code', label: '代码' },
  { name: 'pen-to-square', label: '写作' },
  { name: 'bullhorn', label: '营销' },
  { name: 'rocket', label: '效率' },
  { name: 'graduation-cap', label: '学习' },
  { name: 'lightbulb', label: '创意' },
  { name: 'cog', label: '工具' },
  { name: 'heart', label: '收藏' },
  { name: 'star', label: '重要' },
  { name: 'fire', label: '热门' },
  { name: 'gem', label: '精选' },
  { name: 'bullseye', label: '目标' },
  { name: 'flag', label: '标记' },
  { name: 'bookmark', label: '书签' },
  { name: 'database', label: '数据' },
  { name: 'cloud', label: '云端' },
  { name: 'mobile', label: '移动' },
  { name: 'desktop', label: '桌面' },
  { name: 'palette', label: '设计' }
]

// 预设颜色
const presetColors = [
  '#ef4444', // red
  '#f97316', // orange
  '#f59e0b', // amber
  '#eab308', // yellow
  '#84cc16', // lime
  '#22c55e', // green
  '#10b981', // emerald
  '#14b8a6', // teal
  '#06b6d4', // cyan
  '#0ea5e9', // sky
  '#3b82f6', // blue
  '#6366f1', // indigo
  '#8b5cf6', // violet
  '#a855f7', // purple
  '#d946ef', // fuchsia
  '#ec4899', // pink
]

export function CategoryFormModal({
  category,
  isOpen,
  onClose,
  onSuccess
}: CategoryFormModalProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [color, setColor] = useState('#6366f1')
  const [icon, setIcon] = useState<IconName>('folder')
  const [customColor, setCustomColor] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [nameError, setNameError] = useState('')

  const { toast } = useToast()
  const isEditing = !!category

  // 填充编辑数据
  useEffect(() => {
    if (category) {
      setName(category.name)
      setDescription(category.description || '')
      setColor(category.color)
      setIcon(category.icon as IconName)
      setCustomColor('')
    } else {
      resetForm()
    }
  }, [category])

  const resetForm = () => {
    setName('')
    setDescription('')
    setColor('#6366f1')
    setIcon('folder')
    setCustomColor('')
    setNameError('')
  }

  const validateName = async (nameValue: string) => {
    if (!nameValue.trim()) {
      setNameError('分类名称不能为空')
      return false
    }

    if (nameValue.length > 50) {
      setNameError('分类名称不能超过50个字符')
      return false
    }

    try {
      const exists = await checkCategoryNameExists(
        nameValue.trim(), 
        isEditing ? category?.id : undefined
      )
      
      if (exists) {
        setNameError('分类名称已存在')
        return false
      }
    } catch (error) {
      console.error('检查分类名称失败:', error)
    }

    setNameError('')
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const isNameValid = await validateName(name)
    if (!isNameValid) return

    const finalColor = customColor && isValidColor(customColor) ? customColor : color

    try {
      setIsLoading(true)

      const categoryData = {
        name: name.trim(),
        description: description.trim() || undefined,
        color: finalColor,
        icon: icon,
      }

      if (isEditing && category) {
        console.log('🚀 使用本地优先更新分类')
        const success = await localFirstStore.updateCategory(category.id, categoryData as CategoryUpdate)
        if (success) {
          toast({
            title: "更新成功",
            description: "分类已成功更新",
          })
        } else {
          throw new Error('更新失败')
        }
      } else {
        console.log('🚀 使用本地优先创建分类')
        const newCategory = await localFirstStore.createCategory(categoryData as CategoryInsert)
        if (newCategory) {
          toast({
            title: "创建成功",
            description: "分类已成功创建",
          })
          console.log('✅ 分类创建成功:', newCategory)
        } else {
          throw new Error('创建失败')
        }
      }

      onSuccess()
      onClose()
      resetForm()
    } catch (error) {
      console.error('保存分类失败:', error)
      toast({
        title: "保存失败",
        description: isEditing ? "更新分类时出现错误" : "创建分类时出现错误",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleColorSelect = (selectedColor: string) => {
    setColor(selectedColor)
    setCustomColor('')
  }

  const handleCustomColorChange = (value: string) => {
    setCustomColor(value)
    if (isValidColor(value)) {
      setColor(value)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? '编辑分类' : '创建新分类'}
          </DialogTitle>
          <DialogDescription>
            {isEditing ? '修改分类的信息和外观' : '创建一个新的提示词分类'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 分类名称 */}
          <div className="space-y-2">
            <Label htmlFor="name">分类名称 *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => {
                setName(e.target.value)
                setNameError('')
              }}
              onBlur={() => validateName(name)}
              placeholder="输入分类名称"
              className={nameError ? 'border-red-500' : ''}
              required
            />
            {nameError && (
              <p className="text-sm text-red-600">{nameError}</p>
            )}
          </div>

          {/* 分类描述 */}
          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="输入分类描述（可选）"
              className="min-h-[80px]"
            />
          </div>

          {/* 图标选择 */}
          <div className="space-y-2">
            <Label>图标</Label>
            <div className="grid grid-cols-5 gap-2">
              {availableIcons.map((iconOption) => (
                <button
                  key={iconOption.name}
                  type="button"
                  className={`
                    flex items-center justify-center w-10 h-10 rounded-lg border-2 transition-colors
                    ${icon === iconOption.name 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }
                  `}
                  onClick={() => setIcon(iconOption.name)}
                  title={iconOption.label}
                >
                  <Icon name={iconOption.name} className="h-5 w-5" />
                </button>
              ))}
            </div>
          </div>

          {/* 颜色选择 */}
          <div className="space-y-2">
            <Label>颜色</Label>
            
            {/* 预设颜色 */}
            <div className="grid grid-cols-8 gap-2">
              {presetColors.map((presetColor) => (
                <button
                  key={presetColor}
                  type="button"
                  className={`
                    w-8 h-8 rounded-lg border-2 transition-all
                    ${color === presetColor 
                      ? 'border-gray-400 scale-110' 
                      : 'border-gray-200 hover:scale-105'
                    }
                  `}
                  style={{ backgroundColor: presetColor }}
                  onClick={() => handleColorSelect(presetColor)}
                />
              ))}
            </div>

            {/* 自定义颜色 */}
            <div className="flex items-center gap-2">
              <Input
                type="text"
                value={customColor}
                onChange={(e) => handleCustomColorChange(e.target.value)}
                placeholder="#6366f1"
                className="flex-1"
              />
              <div 
                className="w-8 h-8 rounded border border-gray-200"
                style={{ backgroundColor: color }}
              />
            </div>
          </div>

          {/* 预览 */}
          <div className="space-y-2">
            <Label>预览</Label>
            <div className="flex items-center gap-2 p-3 border rounded-lg bg-gray-50">
              <Icon name={icon} className="h-5 w-5" color={color} />
              <span className="font-medium">{name || '分类名称'}</span>
              {description && (
                <span className="text-sm text-muted-foreground">
                  - {description}
                </span>
              )}
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="flex items-center justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" disabled={isLoading || !!nameError}>
              {isLoading ? (
                <>
                  <Icon name="spinner" className="h-4 w-4 mr-2 animate-spin" />
                  {isEditing ? '更新中...' : '创建中...'}
                </>
              ) : (
                <>
                  <Icon name="save" className="h-4 w-4 mr-2" />
                  {isEditing ? '更新' : '创建'}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
