"use client"

import { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@/components/ui/icon'
import { useToast } from '@/hooks/use-toast'
import { localFirstStore } from '@/lib/local-first-store'
import {
  createPrompt,
  updatePrompt,
  getCategories, 
  getTags,
  createTag
} from '@/lib/database'
import type { 
  PromptWithDetails, 
  Category, 
  Tag,
  PromptInsert,
  PromptUpdate 
} from '@/types/database'

interface PromptFormModalProps {
  prompt?: PromptWithDetails | null
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

export function PromptFormModal({
  prompt,
  isOpen,
  onClose,
  onSuccess
}: PromptFormModalProps) {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [content, setContent] = useState('')
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('')
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([])
  const [newTagName, setNewTagName] = useState('')
  const [categories, setCategories] = useState<Category[]>([])
  const [tags, setTags] = useState<Tag[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(false)

  const { toast } = useToast()
  const isEditing = !!prompt

  // 加载基础数据
  useEffect(() => {
    if (isOpen) {
      loadData()
    }
  }, [isOpen])

  // 填充编辑数据
  useEffect(() => {
    if (prompt) {
      setTitle(prompt.title)
      setDescription(prompt.description || '')
      setContent(prompt.content)
      setSelectedCategoryId(prompt.category_id || '')
      setSelectedTagIds(prompt.tags?.map(tag => tag.id) || [])
    } else {
      resetForm()
    }
  }, [prompt])

  const loadData = async () => {
    try {
      setIsLoadingData(true)
      const [categoriesData, tagsData] = await Promise.all([
        getCategories(),
        getTags()
      ])
      setCategories(categoriesData)
      setTags(tagsData)
    } catch (error) {
      console.error('加载数据失败:', error)
      toast({
        title: "加载失败",
        description: "无法加载分类和标签数据",
        variant: "destructive",
      })
    } finally {
      setIsLoadingData(false)
    }
  }

  const resetForm = () => {
    setTitle('')
    setDescription('')
    setContent('')
    setSelectedCategoryId('')
    setSelectedTagIds([])
    setNewTagName('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!title.trim() || !content.trim()) {
      toast({
        title: "表单验证失败",
        description: "标题和内容不能为空",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)

      const promptData = {
        title: title.trim(),
        description: description.trim() || undefined,
        content: content.trim(),
        category_id: selectedCategoryId || undefined,
      }

      if (isEditing && prompt) {
        await updatePrompt(prompt.id, promptData as PromptUpdate, selectedTagIds)
        toast({
          title: "更新成功",
          description: "提示词已成功更新",
        })
      } else {
        // 使用本地优先存储创建提示词
        console.log('🚀 使用本地优先存储创建提示词')
        const newPrompt = await localFirstStore.createPrompt(promptData as PromptInsert)

        // 立即通知父组件更新
        if (onSuccess) {
          onSuccess(newPrompt)
        }
        toast({
          title: "创建成功",
          description: "提示词已成功创建",
        })
      }

      onSuccess()
      onClose()
      resetForm()
    } catch (error) {
      console.error('保存提示词失败:', error)
      toast({
        title: "保存失败",
        description: isEditing ? "更新提示词时出现错误" : "创建提示词时出现错误",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddTag = async () => {
    if (!newTagName.trim()) return

    try {
      const newTag = await createTag({
        name: newTagName.trim(),
        color: '#6366f1' // 默认颜色
      })
      
      setTags(prev => [...prev, newTag])
      setSelectedTagIds(prev => [...prev, newTag.id])
      setNewTagName('')
      
      toast({
        title: "标签创建成功",
        description: `标签 "${newTag.name}" 已创建并添加`,
      })
    } catch (error) {
      console.error('创建标签失败:', error)
      toast({
        title: "创建标签失败",
        description: "无法创建新标签",
        variant: "destructive",
      })
    }
  }

  const toggleTag = (tagId: string) => {
    setSelectedTagIds(prev => 
      prev.includes(tagId) 
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    )
  }

  const selectedTags = tags.filter(tag => selectedTagIds.includes(tag.id))

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? '编辑提示词' : '创建新提示词'}
          </DialogTitle>
          <DialogDescription>
            {isEditing ? '修改提示词的信息和内容' : '填写提示词的基本信息和内容'}
          </DialogDescription>
        </DialogHeader>

        {isLoadingData ? (
          <div className="flex items-center justify-center py-8">
            <Icon name="spinner" className="h-6 w-6 animate-spin mr-2" />
            <span>加载中...</span>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="flex-1 overflow-hidden flex flex-col">
            <div className="flex-1 overflow-y-auto space-y-4">
              {/* 标题 */}
              <div className="space-y-2">
                <Label htmlFor="title">标题 *</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="输入提示词标题"
                  required
                />
              </div>

              {/* 描述 */}
              <div className="space-y-2">
                <Label htmlFor="description">描述</Label>
                <Input
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="输入提示词的简短描述（可选）"
                />
              </div>

              {/* 分类 */}
              <div className="space-y-2">
                <Label htmlFor="category">分类</Label>
                <select
                  id="category"
                  value={selectedCategoryId}
                  onChange={(e) => setSelectedCategoryId(e.target.value)}
                  className="w-full px-3 py-2 border border-input rounded-md bg-background"
                >
                  <option value="">选择分类（可选）</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* 标签 */}
              <div className="space-y-2">
                <Label>标签</Label>
                
                {/* 已选标签 */}
                {selectedTags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-2">
                    {selectedTags.map((tag) => (
                      <Badge
                        key={tag.id}
                        variant="secondary"
                        className="cursor-pointer"
                        style={{ 
                          backgroundColor: `${tag.color}20`,
                          color: tag.color 
                        }}
                        onClick={() => toggleTag(tag.id)}
                      >
                        {tag.name}
                        <Icon name="times" className="h-3 w-3 ml-1" />
                      </Badge>
                    ))}
                  </div>
                )}

                {/* 可选标签 */}
                <div className="flex flex-wrap gap-2 mb-2">
                  {tags
                    .filter(tag => !selectedTagIds.includes(tag.id))
                    .map((tag) => (
                      <Badge
                        key={tag.id}
                        variant="outline"
                        className="cursor-pointer hover:bg-gray-100"
                        onClick={() => toggleTag(tag.id)}
                      >
                        <Icon name="plus" className="h-3 w-3 mr-1" />
                        {tag.name}
                      </Badge>
                    ))}
                </div>

                {/* 新建标签 */}
                <div className="flex gap-2">
                  <Input
                    value={newTagName}
                    onChange={(e) => setNewTagName(e.target.value)}
                    placeholder="创建新标签"
                    className="flex-1"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        handleAddTag()
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleAddTag}
                    disabled={!newTagName.trim()}
                  >
                    <Icon name="plus" className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* 内容 */}
              <div className="space-y-2">
                <Label htmlFor="content">内容 *</Label>
                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="输入提示词内容"
                  className="min-h-[200px] font-mono"
                  required
                />
                <div className="text-sm text-muted-foreground">
                  {content.length} 字符
                </div>
              </div>
            </div>

            {/* 底部按钮 */}
            <div className="flex items-center justify-end gap-2 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Icon name="spinner" className="h-4 w-4 mr-2 animate-spin" />
                    {isEditing ? '更新中...' : '创建中...'}
                  </>
                ) : (
                  <>
                    <Icon name="save" className="h-4 w-4 mr-2" />
                    {isEditing ? '更新' : '创建'}
                  </>
                )}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
