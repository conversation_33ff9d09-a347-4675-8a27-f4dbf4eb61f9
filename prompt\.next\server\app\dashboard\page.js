(()=>{var a={};a.id=105,a.ids=[105],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1708:a=>{"use strict";a.exports=require("node:process")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},14744:(a,b,c)=>{"use strict";c.d(b,{K:()=>r});var d=c(60687),e=c(43210),f=c(37826),g=c(24934),h=c(68988),i=c(15616),j=c(39390),k=c(32418),l=c(71702),m=c(34257),n=c(22392),o=c(44655);let p=[{name:"folder",label:"文件夹"},{name:"code",label:"代码"},{name:"pen-to-square",label:"写作"},{name:"bullhorn",label:"营销"},{name:"rocket",label:"效率"},{name:"graduation-cap",label:"学习"},{name:"lightbulb",label:"创意"},{name:"cog",label:"工具"},{name:"heart",label:"收藏"},{name:"star",label:"重要"},{name:"fire",label:"热门"},{name:"gem",label:"精选"},{name:"bullseye",label:"目标"},{name:"flag",label:"标记"},{name:"bookmark",label:"书签"},{name:"database",label:"数据"},{name:"cloud",label:"云端"},{name:"mobile",label:"移动"},{name:"desktop",label:"桌面"},{name:"palette",label:"设计"}],q=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9","#3b82f6","#6366f1","#8b5cf6","#a855f7","#d946ef","#ec4899"];function r({category:a,isOpen:b,onClose:c,onSuccess:r}){let[s,t]=(0,e.useState)(""),[u,v]=(0,e.useState)(""),[w,x]=(0,e.useState)("#6366f1"),[y,z]=(0,e.useState)("folder"),[A,B]=(0,e.useState)(""),[C,D]=(0,e.useState)(!1),[E,F]=(0,e.useState)(""),{toast:G}=(0,l.dj)(),H=!!a,I=async b=>{if(!b.trim())return F("分类名称不能为空"),!1;if(b.length>50)return F("分类名称不能超过50个字符"),!1;try{if(await (0,m.c1)(b.trim(),H?a?.id:void 0))return F("分类名称已存在"),!1}catch(a){console.error("检查分类名称失败:",a)}return F(""),!0},J=async b=>{if(b.preventDefault(),!await I(s))return;let d=A&&(0,o.o1)(A)?A:w;try{D(!0);let b={name:s.trim(),description:u.trim()||void 0,color:d,icon:y};if(H&&a)if(console.log("\uD83D\uDE80 使用本地优先更新分类"),await n.J.updateCategory(a.id,b))G({title:"更新成功",description:"分类已成功更新"});else throw Error("更新失败");else{console.log("\uD83D\uDE80 使用本地优先创建分类");let a=await n.J.createCategory(b);if(a)G({title:"创建成功",description:"分类已成功创建"}),console.log("✅ 分类创建成功:",a);else throw Error("创建失败")}r(),c(),t(""),v(""),x("#6366f1"),z("folder"),B(""),F("")}catch(a){console.error("保存分类失败:",a),G({title:"保存失败",description:H?"更新分类时出现错误":"创建分类时出现错误",variant:"destructive"})}finally{D(!1)}};return(0,d.jsx)(f.lG,{open:b,onOpenChange:c,children:(0,d.jsxs)(f.Cf,{className:"max-w-md",children:[(0,d.jsxs)(f.c7,{children:[(0,d.jsx)(f.L3,{children:H?"编辑分类":"创建新分类"}),(0,d.jsx)(f.rr,{children:H?"修改分类的信息和外观":"创建一个新的提示词分类"})]}),(0,d.jsxs)("form",{onSubmit:J,className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{htmlFor:"name",children:"分类名称 *"}),(0,d.jsx)(h.p,{id:"name",value:s,onChange:a=>{t(a.target.value),F("")},onBlur:()=>I(s),placeholder:"输入分类名称",className:E?"border-red-500":"",required:!0}),E&&(0,d.jsx)("p",{className:"text-sm text-red-600",children:E})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{htmlFor:"description",children:"描述"}),(0,d.jsx)(i.T,{id:"description",value:u,onChange:a=>v(a.target.value),placeholder:"输入分类描述（可选）",className:"min-h-[80px]"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{children:"图标"}),(0,d.jsx)("div",{className:"grid grid-cols-5 gap-2",children:p.map(a=>(0,d.jsx)("button",{type:"button",className:`
                    flex items-center justify-center w-10 h-10 rounded-lg border-2 transition-colors
                    ${y===a.name?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}
                  `,onClick:()=>z(a.name),title:a.label,children:(0,d.jsx)(k.Icon,{name:a.name,className:"h-5 w-5"})},a.name))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{children:"颜色"}),(0,d.jsx)("div",{className:"grid grid-cols-8 gap-2",children:q.map(a=>(0,d.jsx)("button",{type:"button",className:`
                    w-8 h-8 rounded-lg border-2 transition-all
                    ${w===a?"border-gray-400 scale-110":"border-gray-200 hover:scale-105"}
                  `,style:{backgroundColor:a},onClick:()=>{x(a),B("")}},a))}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(h.p,{type:"text",value:A,onChange:a=>{var b;B(b=a.target.value),(0,o.o1)(b)&&x(b)},placeholder:"#6366f1",className:"flex-1"}),(0,d.jsx)("div",{className:"w-8 h-8 rounded border border-gray-200",style:{backgroundColor:w}})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{children:"预览"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 p-3 border rounded-lg bg-gray-50",children:[(0,d.jsx)(k.Icon,{name:y,className:"h-5 w-5",color:w}),(0,d.jsx)("span",{className:"font-medium",children:s||"分类名称"}),u&&(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["- ",u]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-4",children:[(0,d.jsx)(g.$,{type:"button",variant:"outline",onClick:c,children:"取消"}),(0,d.jsx)(g.$,{type:"submit",disabled:C||!!E,children:C?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),H?"更新中...":"创建中..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.Icon,{name:"save",className:"h-4 w-4 mr-2"}),H?"更新":"创建"]})})]})]})]})})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21382:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,64118)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,38836)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]}.children,H=["D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27027:(a,b,c)=>{Promise.resolve().then(c.bind(c,64118))},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64118:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Cursor Project\\\\prompy augment\\\\prompt\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\page.tsx","default")},73136:a=>{"use strict";a.exports=require("node:url")},74075:a=>{"use strict";a.exports=require("zlib")},74144:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>G});var d=c(60687),e=c(43210),f=c(47828),g=c(24934),h=c(32418),i=c(59821),j=c(96241);function k({categories:a,selectedCategoryId:b,onCategorySelect:c,onCategoryCreate:f,onCategoryEdit:k,onCategoryDelete:l,isCollapsed:m=!1,onToggleCollapse:n,className:o}){let[p,q]=(0,e.useState)(null),r=[...a].sort((a,b)=>a.sort_order-b.sort_order),s=a.reduce((a,b)=>a+(b.prompt_count||0),0);return(0,d.jsxs)("div",{className:(0,j.cn)("flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300",m?"w-16":"w-64","md:relative absolute md:translate-x-0 z-30",m?"md:w-16 w-0 -translate-x-full":"md:w-64 w-64 translate-x-0",o),children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[!m&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"分类"}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:[s," 个提示词"]})]}),n&&(0,d.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:n,children:(0,d.jsx)(h.Icon,{name:m?"chevron-right":"chevron-left",className:"h-4 w-4"})})]}),(0,d.jsx)("div",{className:"p-2",children:(0,d.jsxs)(g.$,{variant:void 0===b?"secondary":"ghost",className:(0,j.cn)("w-full justify-start gap-3 h-10",m&&"justify-center px-2"),onClick:()=>c(void 0),children:[(0,d.jsx)(h.Icon,{name:"home",className:"h-4 w-4 flex-shrink-0"}),!m&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"flex-1 text-left",children:"全部"}),(0,d.jsx)(i.E,{variant:"secondary",className:"ml-auto",children:s})]})]})}),(0,d.jsx)("div",{className:"flex-1 overflow-y-auto p-2 space-y-1",children:r.map(a=>(0,d.jsxs)("div",{className:"relative group",onMouseEnter:()=>q(a.id),onMouseLeave:()=>q(null),children:[(0,d.jsxs)(g.$,{variant:b===a.id?"secondary":"ghost",className:(0,j.cn)("w-full justify-start gap-3 h-10 relative",m&&"justify-center px-2"),onClick:()=>c(a.id),children:[(0,d.jsx)(h.Icon,{name:a.icon||"folder",className:"h-4 w-4 flex-shrink-0",color:a.color}),!m&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"flex-1 text-left truncate",children:a.name}),(0,d.jsx)(i.E,{variant:"secondary",className:(0,j.cn)("ml-auto transition-opacity duration-200",p===a.id?"opacity-0":"opacity-100"),style:{backgroundColor:`${a.color}20`,color:a.color},children:a.prompt_count||0})]})]}),!m&&p===a.id&&(0,d.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1 bg-white rounded shadow-sm border",children:[(0,d.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-6 w-6 hover:bg-blue-100 hover:text-blue-600",onClick:b=>{b.stopPropagation(),k(a.id)},children:(0,d.jsx)(h.Icon,{name:"edit",className:"h-3 w-3"})}),(0,d.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-6 w-6 hover:bg-red-100 hover:text-red-600",onClick:b=>{b.stopPropagation(),l(a.id)},children:(0,d.jsx)(h.Icon,{name:"trash",className:"h-3 w-3"})})]}),m&&(0,d.jsxs)("div",{className:"absolute left-full top-0 ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50",children:[a.name," (",a.prompt_count||0,")"]})]},a.id))}),(0,d.jsx)("div",{className:"p-2 border-t border-gray-200",children:(0,d.jsxs)(g.$,{variant:"outline",className:(0,j.cn)("w-full gap-2",m&&"justify-center px-2"),onClick:f,children:[(0,d.jsx)(h.Icon,{name:"plus",className:"h-4 w-4"}),!m&&"新建分类"]})})]})}var l=c(32675),m=c(130),n=c(32945),o=c(55192),p=c(44655);function q({className:a}){let[b,c]=(0,e.useState)([]),[f,j]=(0,e.useState)(!0),k=async a=>{try{await navigator.clipboard.writeText(a)}catch(a){console.error("复制失败:",a)}};return f?(0,d.jsxs)(o.Zp,{className:a,children:[(0,d.jsx)(o.aR,{children:(0,d.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(h.Icon,{name:"clipboard",className:"h-5 w-5"}),"复制历史"]})}),(0,d.jsx)(o.Wu,{children:(0,d.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]},b))})})]}):(0,d.jsxs)(o.Zp,{className:a,children:[(0,d.jsx)(o.aR,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(h.Icon,{name:"clipboard",className:"h-5 w-5"}),"复制历史"]}),(0,d.jsx)(o.BT,{children:"最近复制的提示词记录"})]}),b.length>0&&(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>{c([]);try{localStorage.removeItem("prompt-copy-history")}catch(a){console.error("清除复制历史失败:",a)}},children:[(0,d.jsx)(h.Icon,{name:"trash",className:"h-4 w-4 mr-2"}),"清除"]})]})}),(0,d.jsx)(o.Wu,{children:b.length>0?(0,d.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:b.map(a=>(0,d.jsx)("div",{className:"group p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:(0,d.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,d.jsx)("h4",{className:"font-medium text-sm truncate",children:a.promptTitle}),a.category&&(0,d.jsx)(i.E,{variant:"secondary",className:"text-xs",style:{backgroundColor:`${a.category.color}20`,color:a.category.color},children:a.category.name})]}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground mb-2",children:(0,p.EJ)(a.content,100)}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,p.fw)(a.copiedAt)})]}),(0,d.jsx)(g.$,{variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity",onClick:()=>k(a.content),children:(0,d.jsx)(h.Icon,{name:"copy",className:"h-4 w-4"})})]})},a.id))}):(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(h.Icon,{name:"clipboard",className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"暂无复制记录"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"复制提示词后会在这里显示历史记录"})]})})]})}var r=c(8522),s=c(90890),t=c(89904),u=c(14744),v=c(71702);function w({className:a,children:b,...c}){return(0,d.jsx)("div",{className:(0,j.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-700",a),...c,children:b})}function x(){return(0,d.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,d.jsx)(w,{className:"h-6 w-3/4"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(w,{className:"h-4 w-4 rounded-full"}),(0,d.jsx)(w,{className:"h-4 w-16"})]})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(w,{className:"h-8 w-8 rounded"}),(0,d.jsx)(w,{className:"h-8 w-8 rounded"}),(0,d.jsx)(w,{className:"h-8 w-8 rounded"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(w,{className:"h-4 w-full"}),(0,d.jsx)(w,{className:"h-4 w-5/6"}),(0,d.jsx)(w,{className:"h-4 w-4/5"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(w,{className:"h-4 w-4"}),(0,d.jsx)(w,{className:"h-4 w-12"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(w,{className:"h-4 w-4"}),(0,d.jsx)(w,{className:"h-4 w-16"})]})]}),(0,d.jsx)(w,{className:"h-8 w-16 rounded"})]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,d.jsx)(w,{className:"h-6 w-12 rounded-full"}),(0,d.jsx)(w,{className:"h-6 w-16 rounded-full"})]})]})}function y(){return(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-200",children:[(0,d.jsx)(w,{className:"h-8 w-8 rounded"}),(0,d.jsx)("div",{className:"flex-1 space-y-1",children:(0,d.jsx)(w,{className:"h-4 w-20"})}),(0,d.jsx)(w,{className:"h-5 w-8 rounded-full"})]})}function z(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(w,{className:"h-6 w-16"}),(0,d.jsx)(w,{className:"h-4 w-24"})]}),(0,d.jsx)(w,{className:"h-8 w-8 rounded"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(y,{}),Array.from({length:4}).map((a,b)=>(0,d.jsx)(y,{},b))]}),(0,d.jsx)(w,{className:"h-10 w-full rounded"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(w,{className:"h-4 w-4"}),(0,d.jsx)(w,{className:"h-10 flex-1 rounded"}),(0,d.jsx)(w,{className:"h-10 w-20 rounded"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(w,{className:"h-6 w-32"}),(0,d.jsx)(w,{className:"h-4 w-24"})]}),(0,d.jsx)("div",{className:"grid gap-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)(x,{},b))})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(w,{className:"h-4 w-4"}),(0,d.jsx)(w,{className:"h-5 w-20"})]}),(0,d.jsx)(w,{className:"h-4 w-40"})]}),(0,d.jsx)(w,{className:"h-8 w-16 rounded"})]}),(0,d.jsx)("div",{className:"space-y-3",children:Array.from({length:2}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-gray-200",children:[(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)(w,{className:"h-4 w-32"}),(0,d.jsx)(w,{className:"h-3 w-full"}),(0,d.jsx)(w,{className:"h-3 w-24"})]}),(0,d.jsx)(w,{className:"h-6 w-6 rounded"})]},b))})]})]})}function A({size:a="md"}){return(0,d.jsx)("div",{className:"flex items-center justify-center",children:(0,d.jsx)("div",{className:(0,j.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[a])})})}function B(){return(0,d.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,d.jsxs)("div",{className:"text-center space-y-4",children:[(0,d.jsx)(A,{size:"lg"}),(0,d.jsx)("p",{className:"text-gray-500",children:"加载中..."})]})})}var C=c(22392);function D(){let[a,b]=(0,e.useState)({pending:0,lastSync:null}),[c,f]=(0,e.useState)(!0),[i,j]=(0,e.useState)(!1),k=async()=>{j(!0);try{await C.J.manualSync(),b(C.J.getSyncStatus())}catch(a){console.error("手动同步失败:",a)}finally{j(!1)}};return(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsxs)("div",{className:`flex items-center space-x-1 ${!c?"text-gray-500":i?"text-blue-500":a.pending>0?"text-orange-500":"text-green-500"}`,children:[(0,d.jsx)(h.Icon,{name:c?i?"spinner":a.pending>0?"clock":"check-circle":"wifi-off",className:`h-4 w-4 ${i?"animate-spin":""}`}),(0,d.jsx)("span",{children:c?i?"同步中...":a.pending>0?`${a.pending} 项待同步`:"已同步":"离线模式"})]}),a.pending>0&&c&&(0,d.jsx)(g.$,{variant:"ghost",size:"sm",onClick:k,disabled:i,className:"h-6 px-2 text-xs",children:"立即同步"}),(0,d.jsx)("div",{className:"hidden md:block text-xs text-gray-500",children:(()=>{if(!a.lastSync)return"从未同步";let b=Math.floor((Date.now()-a.lastSync)/6e4);if(b<1)return"刚刚同步";if(b<60)return`${b} 分钟前`;let c=Math.floor(b/60);return`${c} 小时前`})()})]})}function E(){let[a,b]=(0,e.useState)(!0);return a?null:(0,d.jsx)("div",{className:"bg-orange-100 border-l-4 border-orange-500 p-4 mb-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(h.Icon,{name:"wifi-off",className:"h-5 w-5 text-orange-500 mr-3"}),(0,d.jsx)("div",{children:(0,d.jsxs)("p",{className:"text-sm text-orange-700",children:[(0,d.jsx)("strong",{children:"离线模式"})," - 您的更改将保存在本地，网络恢复后自动同步"]})})]})})}var F=c(34257);function G(){let[a,b]=(0,e.useState)([]),[c,i]=(0,e.useState)([]),[j,o]=(0,e.useState)([]),[p,w]=(0,e.useState)([]),[x,y]=(0,e.useState)(),[A,G]=(0,e.useState)(""),[H,I]=(0,e.useState)(!0),[J,K]=(0,e.useState)(!1),[L,M]=(0,e.useState)(!1),[N,O]=(0,e.useState)(!1),[P,Q]=(0,e.useState)(1),[R,S]=(0,e.useState)(!1),[T,U]=(0,e.useState)(0),[V,W]=(0,e.useState)(!0),[X,Y]=(0,e.useState)(null),[Z,$]=(0,e.useState)(!1),[_,aa]=(0,e.useState)(!1),[ab,ac]=(0,e.useState)(null),[ad,ae]=(0,e.useState)(!1),[af,ag]=(0,e.useState)(null),[ah,ai]=(0,e.useState)(!1),[aj,ak]=(0,e.useState)(!1),[al,am]=(0,e.useState)(null),[an,ao]=(0,e.useState)(!1),[ap,aq]=(0,e.useState)(null),[ar,as]=(0,e.useState)(!1),[at,au]=(0,e.useState)(400),[av,aw]=(0,e.useState)(!1),{toast:ax}=(0,v.dj)(),{addCopyRecord:ay}={addCopyRecord:(a,b,c,d)=>{window.addCopyRecord&&window.addCopyRecord(a,b,c,d)}};(0,f.U)(),(0,e.useCallback)((a,d)=>{let e=c;if(d&&(e=e.filter(a=>a.category_id===d)),a.trim()){let b=a.toLowerCase();e=e.filter(a=>a.title.toLowerCase().includes(b)||a.content.toLowerCase().includes(b)||a.tags?.some(a=>a.name.toLowerCase().includes(b)))}e.sort((a,b)=>new Date(b.updated_at).getTime()-new Date(a.updated_at).getTime()),b(e),U(e.length),S(!1)},[c]);let az=async()=>{try{console.log("\uD83D\uDE80 使用本地优先存储加载提示词");let a=await C.J.getPrompts();if(i(a),a.length>0){b(a.slice(0,12)),U(a.length),S(a.length>12),console.log(`✅ 立即显示 ${a.length} 个提示词`);return}console.log("\uD83D\uDCE1 本地无数据，使用远程加载");let c=await (0,F.oO)({sortBy:"updated_at",sortOrder:"desc",limit:1e3});i(c.data),A||x||(b(c.data.slice(0,12)),U(c.data.length),S(c.data.length>12))}catch(a){console.error("加载所有提示词失败:",a),W(!1)}},aA=async()=>{try{K(!0);let a=await (0,F.oO)({query:A||void 0,categoryId:x,sortBy:"updated_at",sortOrder:"desc",limit:12,offset:(P-1)*12});1===P?b(a.data):b(b=>[...b,...a.data]),S(a.hasMore),U(a.total)}catch(a){console.error("加载提示词失败:",a),ax({title:"加载失败",description:"无法加载提示词列表",variant:"destructive"})}finally{K(!1)}},aB=async()=>{try{M(!0),console.log("\uD83D\uDE80 使用本地优先存储加载分类");let a=await C.J.getCategories();o(a),console.log(`✅ 加载了 ${a.length} 个分类`)}catch(a){console.error("加载分类失败:",a)}finally{M(!1)}},aC=async()=>{try{let a=await (0,F.FA)(10);w(a)}catch(a){console.error("加载搜索历史失败:",a)}},aD=(0,e.useCallback)(async a=>{if(G(a),Q(1),a.trim())try{await (0,F.eQ)(a.trim()),aC()}catch(a){console.error("添加搜索历史失败:",a)}},[]),aE=(0,e.useCallback)(async a=>{try{if(W(!1),G(a),Q(1),a.trim())try{await (0,F.eQ)(a.trim()),aC()}catch(a){console.error("添加搜索历史失败:",a)}}catch(a){console.error("远程搜索失败:",a)}},[]),aF=async(b,c)=>{try{await (0,F.pB)(c);let d=a.find(a=>a.id===c);d&&ay(c,d.title,b,d.category?{name:d.category.name,color:d.category.color}:void 0),await aA()}catch(a){console.error("更新使用次数失败:",a)}},aG=async a=>{try{let b=await (0,F.Fw)(a);b&&(Y(b),$(!0))}catch(a){console.error("获取提示词详情失败:",a),ax({title:"加载失败",description:"无法获取提示词详情",variant:"destructive"})}},aH=async a=>{try{let b=await (0,F.Fw)(a);b&&(ac(b),aa(!0))}catch(a){console.error("获取提示词详情失败:",a),ax({title:"加载失败",description:"无法获取提示词详情",variant:"destructive"})}},aI=async b=>{let c=a.find(a=>a.id===b);c&&(ag(c),ae(!0))},aJ=()=>{ac(null),aa(!0)},aK=async()=>{if(af)try{if(ai(!0),console.log("\uD83D\uDE80 使用本地优先删除提示词"),await C.J.deletePrompt(af.id))i(a=>a.filter(a=>a.id!==af.id)),b(a=>a.filter(a=>a.id!==af.id)),ax({title:"删除成功",description:"提示词已成功删除"});else throw Error("删除失败");ae(!1),ag(null),await aA(),await aB()}catch(a){console.error("删除提示词失败:",a),ax({title:"删除失败",description:"删除提示词时出现错误",variant:"destructive"})}finally{ai(!1)}},aL=(0,e.useCallback)(async()=>{try{await (0,F.Wf)(),w([]),ax({title:"清除成功",description:"搜索历史已清除"})}catch(a){console.error("清除搜索历史失败:",a),ax({title:"清除失败",description:"清除搜索历史时出现错误",variant:"destructive"})}},[ax]),aM=async()=>{if(ap)try{if(as(!0),console.log("\uD83D\uDE80 使用本地优先删除分类（首页）"),await C.J.deleteCategory(ap.id)){o(a=>a.filter(a=>a.id!==ap.id)),ax({title:"删除成功",description:"分类已成功删除"}),ao(!1),aq(null),console.log("✅ 分类已从首页本地状态中移除");let a=C.J.getDetailedSyncStatus();console.log("\uD83D\uDD0D 首页删除后同步状态:",a)}else throw Error("删除失败")}catch(a){console.error("删除分类失败:",a),ax({title:"删除失败",description:"删除分类时出现错误",variant:"destructive"})}finally{as(!1)}};return H?(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(n.a,{}),(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)("div",{className:"w-80 bg-white border-r border-gray-200 p-6",children:(0,d.jsx)(z,{})}),(0,d.jsx)("div",{className:"flex-1 p-6",children:(0,d.jsx)(B,{})})]})]}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,d.jsx)(n.a,{onCreatePrompt:aJ,children:(0,d.jsx)(D,{})}),(0,d.jsx)(E,{}),(0,d.jsxs)("div",{className:"flex h-[calc(100vh-64px)] relative",children:[!N&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden",onClick:()=>O(!0)}),(0,d.jsx)(k,{categories:j,selectedCategoryId:x,onCategorySelect:a=>{y(a),Q(1)},onCategoryCreate:()=>{am(null),ak(!0)},onCategoryEdit:a=>{let b=j.find(b=>b.id===a);b&&(am(b),ak(!0))},onCategoryDelete:a=>{let b=j.find(b=>b.id===a);b&&(aq(b),ao(!0))},isCollapsed:N,onToggleCollapse:()=>O(!N)}),(0,d.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)(g.$,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>O(!1),children:(0,d.jsx)(h.Icon,{name:"bars",className:"h-5 w-5"})}),(0,d.jsx)("div",{className:"flex-1 max-w-2xl",children:(0,d.jsx)(l.I,{value:A,onChange:G,onSearch:aD,onRemoteSearch:aE,searchHistory:p,onClearHistory:aL,showRemoteSearch:V&&A.trim().length>0})})]})}),(0,d.jsxs)("main",{className:"flex-1 overflow-y-auto p-6",children:[(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:x?j.find(a=>a.id===x)?.name||"分类":"全部提示词"}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("p",{className:"text-muted-foreground",children:["共 ",T," 个提示词",A&&` \xb7 搜索 "${A}"`]}),A&&(0,d.jsx)("div",{className:"flex items-center gap-2",children:V?(0,d.jsx)("span",{className:"text-xs bg-green-100 text-green-700 px-2 py-1 rounded",children:"本地搜索"}):(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded",children:"在线搜索"}),(0,d.jsx)("button",{onClick:()=>W(!0),className:"text-xs text-blue-600 hover:text-blue-800 underline",children:"切换到本地搜索"})]})})]})]}),a.length>0?(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-4 md:gap-6",children:a.map(a=>(0,d.jsx)(m.z,{id:a.id,title:a.title,description:a.description||void 0,content:a.content,category:a.category,tags:a.tags,usageCount:a.usage_count,createdAt:a.created_at,updatedAt:a.updated_at,isLocal:a._isLocal,onView:aG,onEdit:aH,onDelete:aI,onCopy:aF},a.id))}):(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(h.Icon,{name:"search",className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:A?"未找到匹配的提示词":"暂无提示词"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:A?"尝试调整搜索关键词或清除筛选条件":"开始创建您的第一个提示词吧"}),(0,d.jsxs)(g.$,{onClick:()=>{A?G(""):aJ()},children:[(0,d.jsx)(h.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),A?"清除搜索":"新建提示词"]})]}),R&&(0,d.jsx)("div",{className:"text-center mt-8",children:(0,d.jsx)(g.$,{variant:"outline",onClick:()=>{R&&!H&&Q(a=>a+1)},disabled:H,children:H?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(h.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),"加载中..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(h.Icon,{name:"refresh",className:"h-4 w-4 mr-2"}),"加载更多"]})})})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex relative border-l border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",style:{width:at},children:[(0,d.jsx)("div",{className:`absolute left-0 top-0 bottom-0 w-2 cursor-col-resize hover:bg-blue-400 transition-all duration-200 group ${av?"bg-blue-500 w-3":"bg-gray-300 hover:bg-blue-400"}`,onMouseDown:a=>{aw(!0),a.preventDefault();let b=a.clientX,c=a=>{au(Math.max(300,Math.min(600,at+(b-a.clientX))))},d=()=>{aw(!1),document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",d)};document.addEventListener("mousemove",c),document.addEventListener("mouseup",d)},title:"拖拽调整宽度",children:(0,d.jsx)("div",{className:"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-8 bg-white opacity-60 group-hover:opacity-100 transition-opacity"})}),(0,d.jsx)("div",{className:"flex-1 p-4 overflow-y-auto",children:(0,d.jsx)(q,{})})]})]})]}),(0,d.jsx)(r.n,{prompt:X,isOpen:Z,onClose:()=>{$(!1),Y(null)},onEdit:aH,onDelete:aI,onCopy:aF}),(0,d.jsx)(s.G,{prompt:ab,isOpen:_,onClose:()=>{aa(!1),ac(null)},onSuccess:a=>{aa(!1),ac(null),a?(i(b=>[a,...b]),b(b=>[a,...b.slice(0,11)]),U(a=>a+1),console.log("✅ 新提示词已添加到本地状态")):az(),aB()}}),(0,d.jsx)(t.o,{isOpen:ad,onClose:()=>{ae(!1),ag(null)},onConfirm:aK,title:"删除提示词",description:"此操作无法撤销，确定要删除这个提示词吗？",itemName:af?.title,isLoading:ah}),(0,d.jsx)(u.K,{category:al,isOpen:aj,onClose:()=>{ak(!1),am(null)},onSuccess:()=>{aB()}}),(0,d.jsx)(t.o,{isOpen:an,onClose:()=>{ao(!1),aq(null)},onConfirm:aM,title:"删除分类",description:"此操作无法撤销，确定要删除这个分类吗？",itemName:ap?.name,isLoading:ar})]})}},76760:a=>{"use strict";a.exports=require("node:path")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87275:(a,b,c)=>{Promise.resolve().then(c.bind(c,74144))},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,519,522,814,884,121,438,780,568,796,924],()=>b(b.s=21382));module.exports=c})();