(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[382],{89386:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(95155),l=a(12115),n=a(35695),r=a(92669),i=a(319),c=a(97168),d=a(81704),o=a(33208),m=a(99840),u=a(89852),h=a(82714),x=a(88145),j=a(53580),p=a(34938);function g(e){let{isOpen:s,onClose:a,onSearch:n,initialParams:r={}}=e,[i,o]=(0,l.useState)(""),[g,v]=(0,l.useState)(""),[y,f]=(0,l.useState)(""),[N,w]=(0,l.useState)(""),[b,C]=(0,l.useState)([]),[S,k]=(0,l.useState)(""),[F,I]=(0,l.useState)(""),[_,J]=(0,l.useState)(""),[O,E]=(0,l.useState)(""),[M,$]=(0,l.useState)("updated_at"),[q,P]=(0,l.useState)("desc"),[A,G]=(0,l.useState)([]),[B,D]=(0,l.useState)([]),[L,Q]=(0,l.useState)(!1),{toast:T}=(0,j.dj)();(0,l.useEffect)(()=>{s&&z()},[s]),(0,l.useEffect)(()=>{if(r){var e,s;o(r.query||""),v(r.title||""),f(r.content||""),w(r.categoryId||""),C(r.tagIds||[]),k(r.dateFrom||""),I(r.dateTo||""),J((null==(e=r.usageCountMin)?void 0:e.toString())||""),E((null==(s=r.usageCountMax)?void 0:s.toString())||""),$(r.sortBy||"updated_at"),P(r.sortOrder||"desc")}},[JSON.stringify(r)]);let z=async()=>{try{Q(!0);let[e,s]=await Promise.all([(0,p.bW)(),(0,p.Q2)()]);G(e),D(s)}catch(e){console.error("加载数据失败:",e),T({title:"加载失败",description:"无法加载分类和标签数据",variant:"destructive"})}finally{Q(!1)}},H=e=>{C(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},V=B.filter(e=>b.includes(e.id));return(0,t.jsx)(m.lG,{open:s,onOpenChange:a,children:(0,t.jsxs)(m.Cf,{className:"max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col",children:[(0,t.jsxs)(m.c7,{children:[(0,t.jsx)(m.L3,{children:"高级搜索"}),(0,t.jsx)(m.rr,{children:"使用多个条件精确搜索提示词"})]}),L?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(d.Icon,{name:"spinner",className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"加载中..."})]}):(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault();let s={query:i.trim()||void 0,title:g.trim()||void 0,content:y.trim()||void 0,categoryId:N||void 0,tagIds:b.length>0?b:void 0,dateFrom:S||void 0,dateTo:F||void 0,usageCountMin:_?parseInt(_):void 0,usageCountMax:O?parseInt(O):void 0,sortBy:M,sortOrder:q};n(s),a()},className:"flex-1 overflow-hidden flex flex-col",children:[(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"query",children:"关键词搜索"}),(0,t.jsx)(u.p,{id:"query",value:i,onChange:e=>o(e.target.value),placeholder:"在标题、描述、内容中搜索"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"title",children:"标题"}),(0,t.jsx)(u.p,{id:"title",value:g,onChange:e=>v(e.target.value),placeholder:"搜索标题"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"content",children:"内容"}),(0,t.jsx)(u.p,{id:"content",value:y,onChange:e=>f(e.target.value),placeholder:"搜索内容"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"category",children:"分类"}),(0,t.jsxs)("select",{id:"category",value:N,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,t.jsx)("option",{value:"",children:"所有分类"}),A.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{children:"标签"}),V.length>0&&(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:V.map(e=>(0,t.jsxs)(x.E,{variant:"secondary",className:"cursor-pointer",style:{backgroundColor:"".concat(e.color,"20"),color:e.color},onClick:()=>H(e.id),children:[e.name,(0,t.jsx)(d.Icon,{name:"times",className:"h-3 w-3 ml-1"})]},e.id))}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 max-h-32 overflow-y-auto",children:B.filter(e=>!b.includes(e.id)).map(e=>(0,t.jsxs)(x.E,{variant:"outline",className:"cursor-pointer hover:bg-gray-100",onClick:()=>H(e.id),children:[(0,t.jsx)(d.Icon,{name:"plus",className:"h-3 w-3 mr-1"}),e.name]},e.id))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"dateFrom",children:"创建日期从"}),(0,t.jsx)(u.p,{id:"dateFrom",type:"date",value:S,onChange:e=>k(e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"dateTo",children:"创建日期到"}),(0,t.jsx)(u.p,{id:"dateTo",type:"date",value:F,onChange:e=>I(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"usageCountMin",children:"最少使用次数"}),(0,t.jsx)(u.p,{id:"usageCountMin",type:"number",min:"0",value:_,onChange:e=>J(e.target.value),placeholder:"0"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"usageCountMax",children:"最多使用次数"}),(0,t.jsx)(u.p,{id:"usageCountMax",type:"number",min:"0",value:O,onChange:e=>E(e.target.value),placeholder:"无限制"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"sortBy",children:"排序字段"}),(0,t.jsxs)("select",{id:"sortBy",value:M,onChange:e=>$(e.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,t.jsx)("option",{value:"updated_at",children:"更新时间"}),(0,t.jsx)("option",{value:"created_at",children:"创建时间"}),(0,t.jsx)("option",{value:"usage_count",children:"使用次数"}),(0,t.jsx)("option",{value:"title",children:"标题"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"sortOrder",children:"排序方向"}),(0,t.jsxs)("select",{id:"sortOrder",value:q,onChange:e=>P(e.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,t.jsx)("option",{value:"desc",children:"降序"}),(0,t.jsx)("option",{value:"asc",children:"升序"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,t.jsxs)(c.$,{type:"button",variant:"outline",onClick:()=>{o(""),v(""),f(""),w(""),C([]),k(""),I(""),J(""),E(""),$("updated_at"),P("desc")},children:[(0,t.jsx)(d.Icon,{name:"refresh",className:"h-4 w-4 mr-2"}),"重置"]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(c.$,{type:"button",variant:"outline",onClick:a,children:"取消"}),(0,t.jsxs)(c.$,{type:"submit",children:[(0,t.jsx)(d.Icon,{name:"search",className:"h-4 w-4 mr-2"}),"搜索"]})]})]})]})]})})}function v(e){let{results:s,searchQuery:a,total:n,isLoading:r,hasMore:i,onLoadMore:m,onPromptView:u,onPromptEdit:h,onPromptDelete:x,onPromptCopy:j,onAdvancedSearch:p,onClearSearch:v}=e,[y,f]=(0,l.useState)(!1);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"搜索结果"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:a?(0,t.jsxs)(t.Fragment,{children:["找到 ",(0,t.jsx)("span",{className:"font-medium",children:n}),' 个包含 "',(0,t.jsx)("span",{className:"font-medium text-blue-600",children:a}),'" 的提示词']}):(0,t.jsxs)(t.Fragment,{children:["共 ",(0,t.jsx)("span",{className:"font-medium",children:n})," 个提示词"]})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>f(!0),children:[(0,t.jsx)(d.Icon,{name:"filter",className:"h-4 w-4 sm:mr-2"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"高级搜索"})]}),a&&(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:v,children:[(0,t.jsx)(d.Icon,{name:"times",className:"h-4 w-4 sm:mr-2"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"清除搜索"})]})]})]}),a&&0===s.length&&!r&&(0,t.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(d.Icon,{name:"lightbulb",className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-yellow-800 dark:text-yellow-200 mb-1",children:"搜索建议"}),(0,t.jsxs)("ul",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:[(0,t.jsx)("li",{children:"• 检查拼写是否正确"}),(0,t.jsx)("li",{children:"• 尝试使用更通用的关键词"}),(0,t.jsx)("li",{children:"• 使用高级搜索进行更精确的筛选"}),(0,t.jsx)("li",{children:"• 检查是否选择了正确的分类"})]})]})]})}),s.length>0?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6",children:s.map(e=>(0,t.jsx)(o.z,{id:e.id,title:e.title,description:e.description||void 0,content:e.content,category:e.category,tags:e.tags,usageCount:e.usage_count,createdAt:e.created_at,updatedAt:e.updated_at,onView:u,onEdit:h,onDelete:x,onCopy:j,className:"search-result-card"},e.id))}),i&&(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(c.$,{variant:"outline",onClick:m,disabled:r,children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),"加载中..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Icon,{name:"refresh",className:"h-4 w-4 mr-2"}),"加载更多"]})})})]}):r?null:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(d.Icon,{name:"search",className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:a?"未找到匹配的提示词":"暂无提示词"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:a?"尝试调整搜索关键词或使用高级搜索":"开始创建您的第一个提示词吧"}),(0,t.jsx)("div",{className:"flex items-center justify-center gap-2",children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(c.$,{variant:"outline",onClick:()=>f(!0),children:[(0,t.jsx)(d.Icon,{name:"filter",className:"h-4 w-4 mr-2"}),"高级搜索"]}),(0,t.jsxs)(c.$,{onClick:v,children:[(0,t.jsx)(d.Icon,{name:"times",className:"h-4 w-4 mr-2"}),"清除搜索"]})]}):(0,t.jsxs)(c.$,{onClick:()=>{},children:[(0,t.jsx)(d.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),"新建提示词"]})})]}),r&&0===s.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(d.Icon,{name:"spinner",className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"搜索中..."})]}),(0,t.jsx)(g,{isOpen:y,onClose:()=>f(!1),onSearch:e=>{p(e),f(!1)}})]})}var y=a(20728),f=a(26724),N=a(57462);function w(){let e=(0,n.useSearchParams)().get("q")||"",[s,a]=(0,l.useState)(e),[c,d]=(0,l.useState)([]),[o,m]=(0,l.useState)([]),[u,h]=(0,l.useState)(!1),[x,g]=(0,l.useState)(0),[w,b]=(0,l.useState)(1),[C,S]=(0,l.useState)(!1),[k,F]=(0,l.useState)({}),[I,_]=(0,l.useState)(null),[J,O]=(0,l.useState)(!1),[E,M]=(0,l.useState)(!1),[$,q]=(0,l.useState)(null),[P,A]=(0,l.useState)(!1),[G,B]=(0,l.useState)(null),[D,L]=(0,l.useState)(!1),{toast:Q}=(0,j.dj)(),T=(0,l.useCallback)(async()=>{try{let e=await (0,p.FA)(10);m(e)}catch(e){console.error("加载搜索历史失败:",e)}},[]);(0,l.useEffect)(()=>{(async()=>{try{if(await T(),e){h(!0),b(1);let s={query:e.trim()||void 0,limit:12,offset:0};F(s);let a=await (0,p.jG)(s);if(d(a),g(a.length),S(12===a.length),e.trim()){await (0,p.eQ)(e.trim());let s=await (0,p.FA)(10);m(s)}}}catch(e){console.error("初始化页面失败:",e)}finally{h(!1)}})()},[]);let z=(0,l.useCallback)(async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{h(!0),b(1);let a={query:e.trim()||void 0,...s,limit:12,offset:0};F(a);let t=await (0,p.jG)(a);d(t),g(t.length),S(12===t.length),e.trim()&&(await (0,p.eQ)(e.trim()),await T())}catch(e){console.error("搜索失败:",e)}finally{h(!1)}},[]),H=async e=>{await z("",e)},V=async()=>{if(C&&!u)try{h(!0);let e=w+1,s={...k,offset:(e-1)*12},a=await (0,p.jG)(s);d(e=>[...e,...a]),b(e),S(12===a.length)}catch(e){console.error("加载更多失败:",e),Q({title:"加载失败",description:"加载更多结果时出现错误",variant:"destructive"})}finally{h(!1)}},W=async e=>{try{let s=await (0,p.Fw)(e);s&&(_(s),O(!0))}catch(e){console.error("获取提示词详情失败:",e),Q({title:"加载失败",description:"无法获取提示词详情",variant:"destructive"})}},K=async e=>{try{let s=await (0,p.Fw)(e);s&&(q(s),M(!0))}catch(e){console.error("获取提示词详情失败:",e),Q({title:"加载失败",description:"无法获取提示词详情",variant:"destructive"})}},R=async e=>{let s=c.find(s=>s.id===e);s&&(B(s),A(!0))},U=async(e,s)=>{try{await (0,p.pB)(s),d(e=>e.map(e=>e.id===s?{...e,usage_count:e.usage_count+1}:e))}catch(e){console.error("更新使用次数失败:",e)}},X=async()=>{if(G)try{L(!0),await (0,p.Ir)(G.id),Q({title:"删除成功",description:"提示词已成功删除"}),A(!1),B(null),d(e=>e.filter(e=>e.id!==G.id)),g(e=>e-1)}catch(e){console.error("删除提示词失败:",e),Q({title:"删除失败",description:"删除提示词时出现错误",variant:"destructive"})}finally{L(!1)}},Y=async()=>{try{await (0,p.Wf)(),m([]),Q({title:"清除成功",description:"搜索历史已清除"})}catch(e){console.error("清除搜索历史失败:",e),Q({title:"清除失败",description:"清除搜索历史时出现错误",variant:"destructive"})}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,t.jsx)(r.a,{}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto p-6",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)("div",{className:"max-w-2xl",children:(0,t.jsx)(i.I,{value:s,onChange:a,onSearch:z,searchHistory:o,onClearHistory:Y,placeholder:"搜索提示词..."})})}),(0,t.jsx)(v,{results:c,searchQuery:s,total:x,isLoading:u,hasMore:C,onLoadMore:V,onPromptView:W,onPromptEdit:K,onPromptDelete:R,onPromptCopy:U,onAdvancedSearch:H,onClearSearch:()=>{a(""),d([]),g(0),b(1),S(!1),F({})}})]}),(0,t.jsx)(y.n,{prompt:I,isOpen:J,onClose:()=>{O(!1),_(null)},onEdit:K,onDelete:R,onCopy:U}),(0,t.jsx)(f.G,{prompt:$,isOpen:E,onClose:()=>{M(!1),q(null)},onSuccess:()=>{(k.query||Object.keys(k).length>1)&&z(s,k)}}),(0,t.jsx)(N.o,{isOpen:P,onClose:()=>{A(!1),B(null)},onConfirm:X,title:"删除提示词",description:"此操作无法撤销，确定要删除这个提示词吗？",itemName:null==G?void 0:G.title,isLoading:D})]})}function b(){return(0,t.jsx)(l.Suspense,{fallback:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:"加载中..."}),children:(0,t.jsx)(w,{})})}},90027:(e,s,a)=>{Promise.resolve().then(a.bind(a,89386))}},e=>{e.O(0,[266,352,865,874,576,561,949,505,802,938,647,194,441,964,358],()=>e(e.s=90027)),_N_E=e.O()}]);