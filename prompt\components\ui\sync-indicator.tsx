/**
 * 同步状态指示器
 * 显示本地数据与远程数据的同步状态
 */

'use client'

import { useState, useEffect } from 'react'
import { localFirstStore } from '@/lib/local-first-store'
import { Icon } from '@/components/ui/icon'
import { Button } from '@/components/ui/button'

export function SyncIndicator() {
  const [syncStatus, setSyncStatus] = useState({ pending: 0, lastSync: null as number | null })
  const [isOnline, setIsOnline] = useState(true)
  const [isSyncing, setIsSyncing] = useState(false)

  useEffect(() => {
    // 更新同步状态
    const updateStatus = () => {
      setSyncStatus(localFirstStore.getSyncStatus())
    }

    // 初始更新
    updateStatus()

    // 定期更新状态
    const interval = setInterval(updateStatus, 5000)

    // 监听网络状态
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      clearInterval(interval)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleManualSync = async () => {
    setIsSyncing(true)
    try {
      await localFirstStore.manualSync()
      setSyncStatus(localFirstStore.getSyncStatus())
    } catch (error) {
      console.error('手动同步失败:', error)
    } finally {
      setIsSyncing(false)
    }
  }

  const getStatusText = () => {
    if (!isOnline) return '离线模式'
    if (isSyncing) return '同步中...'
    if (syncStatus.pending > 0) return `${syncStatus.pending} 项待同步`
    return '已同步'
  }

  const getStatusColor = () => {
    if (!isOnline) return 'text-gray-500'
    if (isSyncing) return 'text-blue-500'
    if (syncStatus.pending > 0) return 'text-orange-500'
    return 'text-green-500'
  }

  const getStatusIcon = () => {
    if (!isOnline) return 'wifi-off'
    if (isSyncing) return 'spinner'
    if (syncStatus.pending > 0) return 'clock'
    return 'check-circle'
  }

  const formatLastSync = () => {
    if (!syncStatus.lastSync) return '从未同步'
    const diff = Date.now() - syncStatus.lastSync
    const minutes = Math.floor(diff / 60000)
    if (minutes < 1) return '刚刚同步'
    if (minutes < 60) return `${minutes} 分钟前`
    const hours = Math.floor(minutes / 60)
    return `${hours} 小时前`
  }

  return (
    <div className="flex items-center space-x-2 text-sm">
      <div className={`flex items-center space-x-1 ${getStatusColor()}`}>
        <Icon 
          name={getStatusIcon()} 
          className={`h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`} 
        />
        <span>{getStatusText()}</span>
      </div>

      {syncStatus.pending > 0 && isOnline && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleManualSync}
          disabled={isSyncing}
          className="h-6 px-2 text-xs"
        >
          立即同步
        </Button>
      )}

      {/* 详细状态提示 */}
      <div className="hidden md:block text-xs text-gray-500">
        {formatLastSync()}
      </div>
    </div>
  )
}

/**
 * 离线提示横幅
 */
export function OfflineBanner() {
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    setIsOnline(navigator.onLine)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  if (isOnline) return null

  return (
    <div className="bg-orange-100 border-l-4 border-orange-500 p-4 mb-4">
      <div className="flex items-center">
        <Icon name="wifi-off" className="h-5 w-5 text-orange-500 mr-3" />
        <div>
          <p className="text-sm text-orange-700">
            <strong>离线模式</strong> - 您的更改将保存在本地，网络恢复后自动同步
          </p>
        </div>
      </div>
    </div>
  )
}

/**
 * 本地数据指示器
 */
export function LocalDataBadge({ isLocal }: { isLocal: boolean }) {
  if (!isLocal) return null

  return (
    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
      <Icon name="smartphone" className="h-3 w-3 mr-1" />
      本地
    </span>
  )
}

/**
 * 同步状态详情模态框
 */
export function SyncStatusModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const [syncStatus, setSyncStatus] = useState({ pending: 0, lastSync: null as number | null })

  useEffect(() => {
    if (isOpen) {
      setSyncStatus(localFirstStore.getSyncStatus())
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">同步状态</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icon name="x" className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">待同步项目</span>
            <span className="font-medium">{syncStatus.pending}</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">最后同步</span>
            <span className="font-medium">
              {syncStatus.lastSync 
                ? new Date(syncStatus.lastSync).toLocaleString()
                : '从未同步'
              }
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">网络状态</span>
            <span className={`font-medium ${navigator.onLine ? 'text-green-600' : 'text-red-600'}`}>
              {navigator.onLine ? '在线' : '离线'}
            </span>
          </div>

          <div className="pt-4 border-t">
            <Button
              onClick={async () => {
                await localFirstStore.manualSync()
                setSyncStatus(localFirstStore.getSyncStatus())
              }}
              className="w-full"
              disabled={!navigator.onLine}
            >
              <Icon name="refresh-cw" className="h-4 w-4 mr-2" />
              立即同步
            </Button>
          </div>

          <div className="text-xs text-gray-500">
            <p>• 所有操作都会先保存到本地</p>
            <p>• 网络可用时自动同步到云端</p>
            <p>• 离线时仍可正常使用应用</p>
          </div>
        </div>
      </div>
    </div>
  )
}
