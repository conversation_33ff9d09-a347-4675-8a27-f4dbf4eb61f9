"use client"

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Sidebar } from '@/components/sidebar'
import { SearchBar } from '@/components/search-bar'
import { PromptCard } from '@/components/prompt-card'
import { DashboardHeader } from '@/components/dashboard-header'
import { CopyHistory, useCopyHistory } from '@/components/copy-history'
import { PromptDetailModal } from '@/components/prompt-detail-modal'
import { PromptFormModal } from '@/components/prompt-form-modal'
import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog'
import { CategoryFormModal } from '@/components/category-form-modal'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { useToast } from '@/hooks/use-toast'
import { DashboardSkeleton, LoadingPage } from '@/components/ui/skeleton'
import { localFirstStore } from '@/lib/local-first-store'
import { SyncIndicator, OfflineBanner } from '@/components/ui/sync-indicator'

import {
  getPrompts,
  getCategories,
  getSearchHistory,
  getPromptById,
  incrementPromptUsage,
  addSearchHistory,
  deletePrompt,
  deleteCategory,
  clearSearchHistory,
  initializeUserData
} from '@/lib/database'
import type { 
  PromptWithDetails, 
  CategoryWithCount, 
  SearchHistory,
  SearchParams 
} from '@/types/database'

export default function DashboardPage() {
  const [prompts, setPrompts] = useState<PromptWithDetails[]>([])
  const [allPrompts, setAllPrompts] = useState<PromptWithDetails[]>([]) // 本地缓存所有数据
  const [categories, setCategories] = useState<CategoryWithCount[]>([])
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | undefined>()
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingPrompts, setIsLoadingPrompts] = useState(false)
  const [isLoadingCategories, setIsLoadingCategories] = useState(false)
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)
  const [total, setTotal] = useState(0)
  const [isLocalSearch, setIsLocalSearch] = useState(true) // 标记是否使用本地搜索

  // 模态框状态
  const [selectedPrompt, setSelectedPrompt] = useState<PromptWithDetails | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [isFormModalOpen, setIsFormModalOpen] = useState(false)
  const [editingPrompt, setEditingPrompt] = useState<PromptWithDetails | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deletingPrompt, setDeletingPrompt] = useState<PromptWithDetails | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // 分类管理状态
  const [isCategoryFormModalOpen, setIsCategoryFormModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<CategoryWithCount | null>(null)
  const [isCategoryDeleteDialogOpen, setIsCategoryDeleteDialogOpen] = useState(false)
  const [deletingCategory, setDeletingCategory] = useState<CategoryWithCount | null>(null)
  const [isDeletingCategory, setIsDeletingCategory] = useState(false)
  const [copyHistoryWidth, setCopyHistoryWidth] = useState(400) // 复制历史面板宽度
  const [isResizing, setIsResizing] = useState(false)

  const { toast } = useToast()
  const { addCopyRecord } = useCopyHistory()
  const supabase = createClient()

  // 初始化数据
  useEffect(() => {
    initializeApp()
  }, [])

  // 本地搜索函数
  const performLocalSearch = useCallback((query: string, categoryId?: string) => {
    let filteredPrompts = allPrompts

    // 按分类过滤
    if (categoryId) {
      filteredPrompts = filteredPrompts.filter(prompt => prompt.category_id === categoryId)
    }

    // 按搜索词过滤
    if (query.trim()) {
      const searchTerm = query.toLowerCase()
      filteredPrompts = filteredPrompts.filter(prompt =>
        prompt.title.toLowerCase().includes(searchTerm) ||
        prompt.content.toLowerCase().includes(searchTerm) ||
        prompt.tags?.some(tag => tag.name.toLowerCase().includes(searchTerm))
      )
    }

    // 按更新时间排序
    filteredPrompts.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())

    setPrompts(filteredPrompts)
    setTotal(filteredPrompts.length)
    setHasMore(false) // 本地搜索不需要分页
  }, [allPrompts])

  // 监听搜索和分类变化
  useEffect(() => {
    if (isLocalSearch && allPrompts.length > 0) {
      // 使用本地搜索
      performLocalSearch(searchQuery, selectedCategoryId)
    } else {
      // 使用远程搜索
      loadPrompts()
    }
  }, [selectedCategoryId, searchQuery, currentPage, isLocalSearch, allPrompts.length, performLocalSearch])

  const initializeApp = async () => {
    try {
      setIsLoading(true)

      console.log('🚀 开始初始化应用 - 本地优先模式')

      // 第一步：立即从本地获取数据，不进行任何远程请求
      const { prompts: localPrompts, categories: localCategories } = localFirstStore.getLocalDataOnly()

      // 立即设置本地数据，让用户立即看到内容
      if (localPrompts.length > 0) {
        setAllPrompts(localPrompts)
        setPrompts(localPrompts.slice(0, 12))
        setTotal(localPrompts.length)
        setHasMore(localPrompts.length > 12)
        console.log(`✅ 立即显示 ${localPrompts.length} 个本地提示词`)
      }

      if (localCategories.length > 0) {
        setCategories(localCategories)
        console.log(`✅ 立即显示 ${localCategories.length} 个本地分类`)
      }

      // 立即结束加载状态，让用户可以开始使用
      setIsLoading(false)
      console.log('✅ 本地数据加载完成，用户可以开始使用')

      // 第二步：在后台异步初始化和同步（不阻塞用户界面）
      setTimeout(async () => {
        try {
          console.log('🔄 开始后台初始化和同步')

          // 初始化用户数据（首次登录时创建默认分类等）
          await initializeUserData()

          // 后台加载和同步数据
          await Promise.all([
            loadCategories(),
            loadSearchHistory(),
            loadAllPrompts()
          ])

          console.log('✅ 后台同步完成')
        } catch (error) {
          console.error('后台同步失败:', error)
          // 后台同步失败不影响用户使用
        }
      }, 100) // 100ms 后开始后台同步
    } catch (error) {
      console.error('初始化应用失败:', error)
      toast({
        title: "初始化失败",
        description: "应用初始化时出现错误，请刷新页面重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 加载所有提示词到本地缓存
  const loadAllPrompts = async () => {
    try {
      console.log('🚀 使用本地优先存储加载提示词')
      // 使用本地优先存储
      const prompts = await localFirstStore.getPrompts()
      setAllPrompts(prompts)

      // 如果是第一次加载且有数据，立即显示
      if (prompts.length > 0) {
        setPrompts(prompts.slice(0, 12)) // 显示前12个
        setTotal(prompts.length)
        setHasMore(prompts.length > 12)
        console.log(`✅ 立即显示 ${prompts.length} 个提示词`)
        return // 有本地数据就直接返回，不需要远程加载
      }

      // 备用：如果本地无数据，使用原来的方法
      console.log('📡 本地无数据，使用远程加载')
      const response = await getPrompts({
        sortBy: 'updated_at',
        sortOrder: 'desc',
        limit: 1000 // 设置一个较大的限制，获取所有数据
      })

      setAllPrompts(response.data)

      // 初始显示（无搜索条件时）
      if (!searchQuery && !selectedCategoryId) {
        setPrompts(response.data.slice(0, 12)) // 只显示前12个
        setTotal(response.data.length)
        setHasMore(response.data.length > 12)
      }
    } catch (error) {
      console.error('加载所有提示词失败:', error)
      // 如果加载失败，回退到远程搜索模式
      setIsLocalSearch(false)
    }
  }

  const loadPrompts = async () => {
    try {
      setIsLoadingPrompts(true)
      const params: SearchParams = {
        query: searchQuery || undefined,
        categoryId: selectedCategoryId,
        sortBy: 'updated_at',
        sortOrder: 'desc',
        limit: 12,
        offset: (currentPage - 1) * 12
      }

      const response = await getPrompts(params)

      if (currentPage === 1) {
        setPrompts(response.data)
      } else {
        setPrompts(prev => [...prev, ...response.data])
      }

      setHasMore(response.hasMore)
      setTotal(response.total)
    } catch (error) {
      console.error('加载提示词失败:', error)
      toast({
        title: "加载失败",
        description: "无法加载提示词列表",
        variant: "destructive",
      })
    } finally {
      setIsLoadingPrompts(false)
    }
  }

  const loadCategories = async () => {
    try {
      setIsLoadingCategories(true)
      console.log('🚀 使用本地优先存储加载分类')
      // 使用本地优先存储
      const data = await localFirstStore.getCategories()
      setCategories(data)
      console.log(`✅ 加载了 ${data.length} 个分类`)
    } catch (error) {
      console.error('加载分类失败:', error)
    } finally {
      setIsLoadingCategories(false)
    }
  }

  const loadSearchHistory = async () => {
    try {
      const data = await getSearchHistory(10)
      setSearchHistory(data)
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  }

  const handleSearch = useCallback(async (query: string) => {
    // 实时搜索：直接在当前页面进行搜索
    setSearchQuery(query)
    setCurrentPage(1)

    // 如果有搜索词，添加到搜索历史
    if (query.trim()) {
      try {
        await addSearchHistory(query.trim())
        // 重新加载搜索历史
        loadSearchHistory()
      } catch (error) {
        console.error('添加搜索历史失败:', error)
      }
    }
  }, [])

  // 处理远程搜索
  const handleRemoteSearch = useCallback(async (query: string) => {
    try {
      setIsLocalSearch(false) // 切换到远程搜索模式
      setSearchQuery(query)
      setCurrentPage(1)

      // 添加到搜索历史
      if (query.trim()) {
        try {
          await addSearchHistory(query.trim())
          loadSearchHistory()
        } catch (error) {
          console.error('添加搜索历史失败:', error)
        }
      }
    } catch (error) {
      console.error('远程搜索失败:', error)
    }
  }, [])

  const handleCategorySelect = (categoryId: string | undefined) => {
    setSelectedCategoryId(categoryId)
    setCurrentPage(1)
  }

  // 处理复制历史面板宽度调整
  const handleResizeStart = (e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()

    const startX = e.clientX
    const startWidth = copyHistoryWidth

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = startX - e.clientX
      const newWidth = Math.max(300, Math.min(600, startWidth + deltaX))
      setCopyHistoryWidth(newWidth)
    }

    const handleMouseUp = () => {
      setIsResizing(false)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  const handlePromptCopy = async (content: string, promptId: string) => {
    try {
      await incrementPromptUsage(promptId)

      // 添加到复制历史
      const prompt = prompts.find(p => p.id === promptId)
      if (prompt) {
        addCopyRecord(
          promptId,
          prompt.title,
          content,
          prompt.category ? {
            name: prompt.category.name,
            color: prompt.category.color
          } : undefined
        )
      }

      // 重新加载提示词以更新使用次数
      await loadPrompts()
    } catch (error) {
      console.error('更新使用次数失败:', error)
    }
  }

  const handlePromptView = async (promptId: string) => {
    try {
      const prompt = await getPromptById(promptId)
      if (prompt) {
        setSelectedPrompt(prompt)
        setIsDetailModalOpen(true)
      }
    } catch (error) {
      console.error('获取提示词详情失败:', error)
      toast({
        title: "加载失败",
        description: "无法获取提示词详情",
        variant: "destructive",
      })
    }
  }

  const handlePromptEdit = async (promptId: string) => {
    try {
      const prompt = await getPromptById(promptId)
      if (prompt) {
        setEditingPrompt(prompt)
        setIsFormModalOpen(true)
      }
    } catch (error) {
      console.error('获取提示词详情失败:', error)
      toast({
        title: "加载失败",
        description: "无法获取提示词详情",
        variant: "destructive",
      })
    }
  }

  const handlePromptDelete = async (promptId: string) => {
    const prompt = prompts.find(p => p.id === promptId)
    if (prompt) {
      setDeletingPrompt(prompt)
      setIsDeleteDialogOpen(true)
    }
  }

  const handleCategoryCreate = () => {
    setEditingCategory(null)
    setIsCategoryFormModalOpen(true)
  }

  const handleCategoryEdit = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId)
    if (category) {
      setEditingCategory(category)
      setIsCategoryFormModalOpen(true)
    }
  }

  const handleCategoryDelete = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId)
    if (category) {
      setDeletingCategory(category)
      setIsCategoryDeleteDialogOpen(true)
    }
  }

  const loadMore = () => {
    if (hasMore && !isLoading) {
      setCurrentPage(prev => prev + 1)
    }
  }

  const handleCreatePrompt = () => {
    setEditingPrompt(null)
    setIsFormModalOpen(true)
  }

  const handleFormSuccess = (newPrompt?: any) => {
    setIsFormModalOpen(false)
    setEditingPrompt(null)

    // 如果是新创建的提示词，立即更新本地状态
    if (newPrompt) {
      setAllPrompts(prev => [newPrompt, ...prev])
      setPrompts(prev => [newPrompt, ...prev.slice(0, 11)]) // 保持12个
      setTotal(prev => prev + 1)
      console.log('✅ 新提示词已添加到本地状态')
    } else {
      // 编辑模式，重新加载数据
      loadAllPrompts()
    }

    loadCategories()
  }

  const handleDeleteConfirm = async () => {
    if (!deletingPrompt) return

    try {
      setIsDeleting(true)
      console.log('🚀 使用本地优先删除提示词')

      // 使用本地优先删除
      const success = await localFirstStore.deletePrompt(deletingPrompt.id)

      if (success) {
        // 立即从本地状态中移除
        setAllPrompts(prev => prev.filter(p => p.id !== deletingPrompt.id))
        setPrompts(prev => prev.filter(p => p.id !== deletingPrompt.id))

        toast({
          title: "删除成功",
          description: "提示词已成功删除",
        })
      } else {
        throw new Error('删除失败')
      }

      setIsDeleteDialogOpen(false)
      setDeletingPrompt(null)
      await loadPrompts()
      await loadCategories()
    } catch (error) {
      console.error('删除提示词失败:', error)
      toast({
        title: "删除失败",
        description: "删除提示词时出现错误",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleClearSearchHistory = useCallback(async () => {
    try {
      await clearSearchHistory()
      setSearchHistory([])
      toast({
        title: "清除成功",
        description: "搜索历史已清除",
      })
    } catch (error) {
      console.error('清除搜索历史失败:', error)
      toast({
        title: "清除失败",
        description: "清除搜索历史时出现错误",
        variant: "destructive",
      })
    }
  }, [toast])

  const handleCategoryFormSuccess = () => {
    loadCategories()
  }

  const handleCategoryDeleteConfirm = async () => {
    if (!deletingCategory) return

    try {
      setIsDeletingCategory(true)
      console.log('🚀 使用本地优先删除分类（首页）')

      // 使用本地优先删除
      const success = await localFirstStore.deleteCategory(deletingCategory.id)

      if (success) {
        // 立即从本地状态中移除
        setCategories(prev => prev.filter(c => c.id !== deletingCategory.id))

        toast({
          title: "删除成功",
          description: "分类已成功删除",
        })

        setIsCategoryDeleteDialogOpen(false)
        setDeletingCategory(null)
        console.log('✅ 分类已从首页本地状态中移除')

        // 输出同步状态用于调试
        const syncStatus = localFirstStore.getDetailedSyncStatus()
        console.log('🔍 首页删除后同步状态:', syncStatus)
      } else {
        throw new Error('删除失败')
      }
    } catch (error) {
      console.error('删除分类失败:', error)
      toast({
        title: "删除失败",
        description: "删除分类时出现错误",
        variant: "destructive",
      })
    } finally {
      setIsDeletingCategory(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <DashboardHeader />
        <div className="flex">
          <div className="w-80 bg-white border-r border-gray-200 p-6">
            <DashboardSkeleton />
          </div>
          <div className="flex-1 p-6">
            <LoadingPage />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 顶部导航 */}
      <DashboardHeader onCreatePrompt={handleCreatePrompt}>
        <SyncIndicator />
      </DashboardHeader>

      {/* 离线提示 */}
      <OfflineBanner />



      <div className="flex h-[calc(100vh-64px)] relative">
        {/* 移动端遮罩层 */}
        {!isSidebarCollapsed && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
            onClick={() => setIsSidebarCollapsed(true)}
          />
        )}

        {/* 侧边栏 */}
        <Sidebar
          categories={categories}
          selectedCategoryId={selectedCategoryId}
          onCategorySelect={handleCategorySelect}
          onCategoryCreate={handleCategoryCreate}
          onCategoryEdit={handleCategoryEdit}
          onCategoryDelete={handleCategoryDelete}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
        />

        {/* 主内容区 */}
        <div className="flex-1 flex overflow-hidden">
          {/* 左侧主内容 */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* 搜索栏 */}
            <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center gap-4">
                {/* 移动端菜单按钮 */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="md:hidden"
                  onClick={() => setIsSidebarCollapsed(false)}
                >
                  <Icon name="bars" className="h-5 w-5" />
                </Button>

                <div className="flex-1 max-w-2xl">
                  <SearchBar
                    value={searchQuery}
                    onChange={setSearchQuery}
                    onSearch={handleSearch}
                    onRemoteSearch={handleRemoteSearch}
                    searchHistory={searchHistory}
                    onClearHistory={handleClearSearchHistory}
                    showRemoteSearch={isLocalSearch && searchQuery.trim().length > 0}
                  />
                </div>
              </div>
            </div>

            {/* 内容区域 */}
            <main className="flex-1 overflow-y-auto p-6">
            {/* 统计信息 */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {selectedCategoryId 
                  ? categories.find(c => c.id === selectedCategoryId)?.name || '分类'
                  : '全部提示词'
                }
              </h1>
              <div className="flex items-center gap-4">
                <p className="text-muted-foreground">
                  共 {total} 个提示词
                  {searchQuery && ` · 搜索 "${searchQuery}"`}
                </p>
                {searchQuery && (
                  <div className="flex items-center gap-2">
                    {isLocalSearch ? (
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                        本地搜索
                      </span>
                    ) : (
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                          在线搜索
                        </span>
                        <button
                          onClick={() => setIsLocalSearch(true)}
                          className="text-xs text-blue-600 hover:text-blue-800 underline"
                        >
                          切换到本地搜索
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* 提示词网格 */}
            {prompts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-4 md:gap-6">
                {prompts.map((prompt) => (
                  <PromptCard
                    key={prompt.id}
                    id={prompt.id}
                    title={prompt.title}
                    description={prompt.description || undefined}
                    content={prompt.content}
                    category={prompt.category}
                    tags={prompt.tags}
                    usageCount={prompt.usage_count}
                    createdAt={prompt.created_at}
                    updatedAt={prompt.updated_at}

                    isLocal={prompt._isLocal}
                    onView={handlePromptView}
                    onEdit={handlePromptEdit}
                    onDelete={handlePromptDelete}
                    onCopy={handlePromptCopy}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Icon name="search" className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {searchQuery ? '未找到匹配的提示词' : '暂无提示词'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery 
                    ? '尝试调整搜索关键词或清除筛选条件'
                    : '开始创建您的第一个提示词吧'
                  }
                </p>
                <Button
                  onClick={() => {
                    if (searchQuery) {
                      setSearchQuery('')
                    } else {
                      handleCreatePrompt()
                    }
                  }}
                >
                  <Icon name="plus" className="h-4 w-4 mr-2" />
                  {searchQuery ? '清除搜索' : '新建提示词'}
                </Button>
              </div>
            )}

            {/* 加载更多 */}
            {hasMore && (
              <div className="text-center mt-8">
                <Button
                  variant="outline"
                  onClick={loadMore}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Icon name="spinner" className="h-4 w-4 mr-2 animate-spin" />
                      加载中...
                    </>
                  ) : (
                    <>
                      <Icon name="refresh" className="h-4 w-4 mr-2" />
                      加载更多
                    </>
                  )}
                </Button>
              </div>
            )}
          </main>
          </div>

          {/* 右侧复制历史 - 仅在大屏幕显示 */}
          <div
            className="hidden xl:flex relative border-l border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
            style={{ width: copyHistoryWidth }}
          >
            {/* 拖拽调整手柄 */}
            <div
              className={`absolute left-0 top-0 bottom-0 w-2 cursor-col-resize hover:bg-blue-400 transition-all duration-200 group ${
                isResizing ? 'bg-blue-500 w-3' : 'bg-gray-300 hover:bg-blue-400'
              }`}
              onMouseDown={handleResizeStart}
              title="拖拽调整宽度"
            >
              {/* 拖拽指示器 */}
              <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-8 bg-white opacity-60 group-hover:opacity-100 transition-opacity" />
            </div>

            {/* 复制历史内容 */}
            <div className="flex-1 p-4 overflow-y-auto">
              <CopyHistory />
            </div>
          </div>
        </div>
      </div>

      {/* 模态框 */}
      <PromptDetailModal
        prompt={selectedPrompt}
        isOpen={isDetailModalOpen}
        onClose={() => {
          setIsDetailModalOpen(false)
          setSelectedPrompt(null)
        }}
        onEdit={handlePromptEdit}
        onDelete={handlePromptDelete}
        onCopy={handlePromptCopy}
      />

      <PromptFormModal
        prompt={editingPrompt}
        isOpen={isFormModalOpen}
        onClose={() => {
          setIsFormModalOpen(false)
          setEditingPrompt(null)
        }}
        onSuccess={handleFormSuccess}
      />

      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => {
          setIsDeleteDialogOpen(false)
          setDeletingPrompt(null)
        }}
        onConfirm={handleDeleteConfirm}
        title="删除提示词"
        description="此操作无法撤销，确定要删除这个提示词吗？"
        itemName={deletingPrompt?.title}
        isLoading={isDeleting}
      />

      <CategoryFormModal
        category={editingCategory}
        isOpen={isCategoryFormModalOpen}
        onClose={() => {
          setIsCategoryFormModalOpen(false)
          setEditingCategory(null)
        }}
        onSuccess={handleCategoryFormSuccess}
      />

      <DeleteConfirmDialog
        isOpen={isCategoryDeleteDialogOpen}
        onClose={() => {
          setIsCategoryDeleteDialogOpen(false)
          setDeletingCategory(null)
        }}
        onConfirm={handleCategoryDeleteConfirm}
        title="删除分类"
        description="此操作无法撤销，确定要删除这个分类吗？"
        itemName={deletingCategory?.name}
        isLoading={isDeletingCategory}
      />
    </div>
  )
}
