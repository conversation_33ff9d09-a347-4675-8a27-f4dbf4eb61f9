(()=>{var a={};a.id=365,a.ids=[365],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(96241);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28086:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(9866),e=c(44999);async function f(){let a=await (0,e.UL)();return(0,d.createServerClient)("https://vigxjamjjlxzmuzwxwyl.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZpZ3hqYW1qamx4em11end4d3lsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Nzc4NTAsImV4cCI6MjA2OTA1Mzg1MH0.re45eDFz2pz7Tswcx5sE1bWuCP7MH481XHgsecj578E",{cookies:{getAll:()=>a.getAll(),setAll(b){try{b.forEach(({name:b,value:c,options:d})=>a.set(b,c,d))}catch{}}}})}},28354:a=>{"use strict";a.exports=require("util")},29120:(a,b,c)=>{Promise.resolve().then(c.bind(c,53506)),Promise.resolve().then(c.bind(c,76631))},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31370:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(37413),e=c(28086),f=c(39916),g=c(53506),h=c(76631);async function i(){let a=await (0,e.U)(),{data:{user:b}}=await a.auth.getUser();return b?(0,f.redirect)("/dashboard"):(0,d.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:(0,d.jsxs)("div",{className:"max-w-md w-full mx-auto p-6",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4",children:(0,d.jsx)(h.Icon,{name:"lightbulb",className:"h-8 w-8 text-white"})}),(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"提示词管理工具"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"精美的现代化提示词管理工具"})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"核心功能"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(h.Icon,{name:"folder",className:"h-5 w-5 text-blue-600"}),(0,d.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"智能分类管理"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(h.Icon,{name:"search",className:"h-5 w-5 text-green-600"}),(0,d.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"实时搜索功能"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(h.Icon,{name:"copy",className:"h-5 w-5 text-purple-600"}),(0,d.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"一键复制使用"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(h.Icon,{name:"tags",className:"h-5 w-5 text-orange-600"}),(0,d.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"标签系统管理"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(h.Icon,{name:"mobile",className:"h-5 w-5 text-red-600"}),(0,d.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"响应式设计"})]})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center",children:"开始使用"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4 text-center",children:"使用您的账户登录以开始管理您的提示词"}),(0,d.jsx)("div",{className:"flex justify-center",children:(0,d.jsx)(g.AuthButton,{})})]}),(0,d.jsx)("div",{className:"text-center mt-6",children:(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"安全、快速、易用的提示词管理解决方案"})})]})})}},32418:(a,b,c)=>{"use strict";c.d(b,{Icon:()=>j});var d=c(60687),e=c(4161),f=c(39163),g=c(97e3);f.Yv.add(g.ao0,g.Uj9,g.jTw,g.LFz,g.e4L,g.KMJ,g.eST,g.MjD,g.QLR,g.jPR,g.MT7,g.yLS,g.pS3,g.DX_,g.qcK,g.t5Z,g.cbP,g.JmV,g.yy,g.G06,g.mRM,g.YpS,g.TJL,g.l9V,g.ITF,g.dB,g.X46,g.GxD,g.v02,g.Jt$,g.w2A,g.Wzs,g.XkK,g.GRI,g.e68,g.zpE,g.iW_,g.wRm,g.ckx,g.vaG,g.z1G,g.Vpu,g.hSh,g.U23,g.yek,g.oZK,g.gr3,g.AaJ,g.KTq,g.Iae,g.h8M,g.ruc,g.vZS,g.Cyq,g.n2W,g.XaT,g.A4h,g.okg,g.a$,g.CYF,g.Hzw,g.CQO,g.Bwz,g.DW4,g.KKb,g.V2x,g.hem,g.D6w,g.jBL,g.ArK,g.GrJ,g.w7B,g.YBv,g.fyG,g._eQ,g.nET,g.rC2,g.p1w,g.zm_,g.kNw,g.R70,g.zqi,g.iHh,g.B9e,g.LPI,g.pvD,g.s6x,g.Pcr,g.Q9Y,g.TBz,g.e5w,g.$Fj);let h={folder:"folder","folder-open":"folder-open",code:"code","pen-to-square":"pen-to-square",bullhorn:"bullhorn",rocket:"rocket","graduation-cap":"graduation-cap",search:"search",plus:"plus",copy:"copy",edit:"edit",trash:"trash",eye:"eye",tags:"tags",heart:"heart",share:"share",download:"download",upload:"upload",star:"star",bookmark:"bookmark",filter:"filter","sort-amount-down":"sort-amount-down","sort-amount-up":"sort-amount-up",grid:"grid",list:"list",cog:"cog",user:"user","sign-out-alt":"sign-out-alt",home:"home","chevron-down":"chevron-down","chevron-up":"chevron-up","chevron-left":"chevron-left","chevron-right":"chevron-right",times:"times",check:"check","exclamation-triangle":"exclamation-triangle","info-circle":"info-circle","question-circle":"question-circle",bars:"bars","ellipsis-v":"ellipsis-v",spinner:"spinner",refresh:"refresh",save:"save",undo:"undo",redo:"redo",expand:"expand",compress:"compress","external-link-alt":"external-link-alt",clipboard:"clipboard","clipboard-check":"clipboard-check",markdown:"markdown","file-code":"file-code","file-text":"file-text",image:"image",video:"video",music:"music",file:"file",calendar:"calendar",clock:"clock",hashtag:"hashtag",at:"at",link:"link",globe:"globe",lock:"lock",unlock:"unlock",shield:"shield",database:"database",server:"server",cloud:"cloud",desktop:"desktop",mobile:"mobile",tablet:"tablet",laptop:"laptop",palette:"palette","paint-brush":"paint-brush",magic:"magic",lightbulb:"lightbulb",flash:"flash",bolt:"bolt",fire:"fire",gem:"gem",crown:"crown",trophy:"trophy",medal:"medal",award:"award",bullseye:"bullseye",flag:"flag","map-marker":"map-marker",compass:"compass",route:"route",map:"map",chart:"chart-bar"};var i=c(96241);function j({name:a,className:b,size:c,spin:f=!1,pulse:g=!1,color:j}){let k=a&&h[a]?a:"question-circle";return(0,d.jsx)(e.g,{icon:h[k],className:(0,i.cn)(b),size:c,spin:f,pulse:g,color:j})}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47828:(a,b,c)=>{"use strict";c.d(b,{U:()=>e});var d=c(59522);function e(){return(0,d.createBrowserClient)("https://vigxjamjjlxzmuzwxwyl.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZpZ3hqYW1qamx4em11end4d3lsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Nzc4NTAsImV4cCI6MjA2OTA1Mzg1MH0.re45eDFz2pz7Tswcx5sE1bWuCP7MH481XHgsecj578E")}},47990:()=>{},53506:(a,b,c)=>{"use strict";c.d(b,{AuthButton:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AuthButton() from the server but AuthButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\components\\auth-button.tsx","AuthButton")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59463:(a,b,c)=>{"use strict";c.d(b,{AuthButton:()=>l});var d=c(60687),e=c(85814),f=c.n(e),g=c(24934),h=c(47828),i=c(16189);function j(){let a=(0,i.useRouter)(),b=async()=>{let b=(0,h.U)();await b.auth.signOut(),a.push("/auth/login")};return(0,d.jsx)(g.$,{onClick:b,children:"Logout"})}var k=c(43210);function l(){let[a,b]=(0,k.useState)(null),[c,e]=(0,k.useState)(!0);return((0,h.U)(),c)?(0,d.jsx)("div",{className:"h-8 w-20 bg-gray-200 animate-pulse rounded"}):a?(0,d.jsxs)("div",{className:"flex items-center gap-4",children:["Hey, ",a.email,"!",(0,d.jsx)(j,{})]}):(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(g.$,{asChild:!0,size:"sm",variant:"outline",children:(0,d.jsx)(f(),{href:"/auth/login",children:"Sign in"})}),(0,d.jsx)(g.$,{asChild:!0,size:"sm",variant:"default",children:(0,d.jsx)(f(),{href:"/auth/sign-up",children:"Sign up"})})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},76631:(a,b,c)=>{"use strict";c.d(b,{Icon:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Icon() from the server but Icon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\components\\ui\\icon.tsx","Icon")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},92672:(a,b,c)=>{Promise.resolve().then(c.bind(c,59463)),Promise.resolve().then(c.bind(c,32418))},94735:a=>{"use strict";a.exports=require("events")},97930:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,31370)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\auth\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,38836)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]}.children,H=["D:\\Cursor Project\\prompy augment\\prompt\\app\\auth\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/auth/page",pathname:"/auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/auth/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,519,522,814,884,41,780],()=>b(b.s=97930));module.exports=c})();