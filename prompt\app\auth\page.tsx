import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { AuthButton } from "@/components/auth-button";
import { Icon } from "@/components/ui/icon";

export default async function AuthPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    return redirect("/dashboard");
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-md w-full mx-auto p-6">
        {/* Logo 和标题 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
            <Icon name="lightbulb" className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            提示词管理工具
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            精美的现代化提示词管理工具
          </p>
        </div>

        {/* 功能特色 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            核心功能
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Icon name="folder" className="h-5 w-5 text-blue-600" />
              <span className="text-gray-700 dark:text-gray-300">智能分类管理</span>
            </div>
            <div className="flex items-center gap-3">
              <Icon name="search" className="h-5 w-5 text-green-600" />
              <span className="text-gray-700 dark:text-gray-300">实时搜索功能</span>
            </div>
            <div className="flex items-center gap-3">
              <Icon name="copy" className="h-5 w-5 text-purple-600" />
              <span className="text-gray-700 dark:text-gray-300">一键复制使用</span>
            </div>
            <div className="flex items-center gap-3">
              <Icon name="tags" className="h-5 w-5 text-orange-600" />
              <span className="text-gray-700 dark:text-gray-300">标签系统管理</span>
            </div>
            <div className="flex items-center gap-3">
              <Icon name="mobile" className="h-5 w-5 text-red-600" />
              <span className="text-gray-700 dark:text-gray-300">响应式设计</span>
            </div>
          </div>
        </div>

        {/* 登录按钮 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
            开始使用
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 text-center">
            使用您的账户登录以开始管理您的提示词
          </p>
          <div className="flex justify-center">
            <AuthButton />
          </div>
        </div>

        {/* 底部信息 */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            安全、快速、易用的提示词管理解决方案
          </p>
        </div>
      </div>
    </div>
  );
}
