// Cloudflare Worker 代理脚本
// 用于解决 Vercel 在中国大陆访问问题

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  // 目标 Vercel 应用 URL
  const targetUrl = 'https://prompt-management-tool-quexwgqa8-sevens-projects-ebf7f705.vercel.app'
  
  // 获取请求的 URL
  const url = new URL(request.url)
  
  // 构建目标 URL
  const targetRequest = new URL(url.pathname + url.search, targetUrl)
  
  // 创建新的请求
  const newRequest = new Request(targetRequest, {
    method: request.method,
    headers: request.headers,
    body: request.body
  })
  
  try {
    // 发送请求到 Vercel
    const response = await fetch(newRequest)
    
    // 创建新的响应，添加 CORS 头
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...response.headers,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cache-Control': 'public, max-age=3600'
      }
    })
    
    return newResponse
  } catch (error) {
    return new Response('代理服务器错误: ' + error.message, {
      status: 500,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8'
      }
    })
  }
}

// 使用说明：
// 1. 访问 https://workers.cloudflare.com/
// 2. 注册免费账户
// 3. 创建新的 Worker
// 4. 复制此代码到 Worker 编辑器
// 5. 部署 Worker
// 6. 使用 Worker 的 URL 访问您的应用
