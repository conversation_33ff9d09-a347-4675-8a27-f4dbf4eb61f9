/**
 * 本地优先存储系统
 * 所有数据优先存储在本地，后台异步同步到 Supabase
 */

import { createClient } from '@/lib/supabase/client'
import type { 
  Prompt, 
  Category, 
  Tag, 
  PromptInsert, 
  CategoryInsert, 
  TagInsert 
} from '@/types/database'

// 本地存储键
const STORAGE_KEYS = {
  PROMPTS: 'local_prompts',
  CATEGORIES: 'local_categories', 
  TAGS: 'local_tags',
  SYNC_QUEUE: 'sync_queue',
  LAST_SYNC: 'last_sync_time',
  USER_ID: 'current_user_id'
} as const

// 同步操作类型
type SyncOperation = {
  id: string
  type: 'CREATE' | 'UPDATE' | 'DELETE'
  table: 'prompts' | 'categories' | 'tags'
  data: any
  localId: string
  timestamp: number
  retryCount: number
}

// 本地数据项
type LocalDataItem<T> = T & {
  _localId: string
  _isLocal: boolean
  _needsSync: boolean
  _lastModified: number
}

class LocalFirstStore {
  private supabase = createClient()
  private syncInProgress = false
  private syncQueue: SyncOperation[] = []

  constructor() {
    // 只在客户端执行
    if (typeof window !== 'undefined') {
      this.loadSyncQueue()
      this.startBackgroundSync()
    }
  }

  /**
   * 生成本地 ID
   */
  private generateLocalId(): string {
    return `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取当前用户 ID
   */
  private async getCurrentUserId(): Promise<string | null> {
    // 先从本地存储获取
    if (typeof window !== 'undefined') {
      const cached = localStorage.getItem(STORAGE_KEYS.USER_ID)
      if (cached) return cached
    }

    // 从 Supabase 获取
    const { data: { user } } = await this.supabase.auth.getUser()
    if (user) {
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEYS.USER_ID, user.id)
      }
      return user.id
    }
    return null
  }

  /**
   * 从本地存储获取数据
   */
  private getLocalData<T>(key: string): LocalDataItem<T>[] {
    try {
      if (typeof window === 'undefined') return []
      const data = localStorage.getItem(key)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error('获取本地数据失败:', error)
      return []
    }
  }

  /**
   * 保存数据到本地存储
   */
  private setLocalData<T>(key: string, data: LocalDataItem<T>[]): void {
    try {
      if (typeof window === 'undefined') return
      localStorage.setItem(key, JSON.stringify(data))
    } catch (error) {
      console.error('保存本地数据失败:', error)
    }
  }

  /**
   * 添加同步操作到队列
   */
  private addToSyncQueue(operation: Omit<SyncOperation, 'id' | 'timestamp' | 'retryCount'>): void {
    const syncOp: SyncOperation = {
      ...operation,
      id: this.generateLocalId(),
      timestamp: Date.now(),
      retryCount: 0
    }
    
    this.syncQueue.push(syncOp)
    this.saveSyncQueue()
  }

  /**
   * 保存同步队列
   */
  private saveSyncQueue(): void {
    if (typeof window === 'undefined') return
    localStorage.setItem(STORAGE_KEYS.SYNC_QUEUE, JSON.stringify(this.syncQueue))
  }

  /**
   * 加载同步队列
   */
  private loadSyncQueue(): void {
    try {
      if (typeof window === 'undefined') {
        this.syncQueue = []
        return
      }
      const data = localStorage.getItem(STORAGE_KEYS.SYNC_QUEUE)
      this.syncQueue = data ? JSON.parse(data) : []
    } catch (error) {
      console.error('加载同步队列失败:', error)
      this.syncQueue = []
    }
  }

  /**
   * 获取所有提示词（本地优先）
   */
  async getPrompts(): Promise<LocalDataItem<Prompt>[]> {
    console.log('📱 从本地获取提示词数据')
    const localPrompts = this.getLocalData<Prompt>(STORAGE_KEYS.PROMPTS)

    // 总是优先返回本地数据，即使为空
    if (localPrompts.length > 0) {
      console.log(`✅ 本地找到 ${localPrompts.length} 个提示词`)
      return localPrompts
    }

    // 本地没有数据，尝试从远程获取（但不阻塞）
    console.log('📱 本地无数据，尝试从远程获取...')
    try {
      const { data, error } = await this.supabase
        .from('prompts')
        .select('*, category:categories(*), prompt_tags(tag:tags(*))')
        .order('updated_at', { ascending: false })

      if (error) {
        console.warn('远程获取失败，返回空数组:', error)
        return []
      }

      const remotePrompts: LocalDataItem<Prompt>[] = (data || []).map(prompt => ({
        ...prompt,
        _localId: prompt.id,
        _isLocal: false,
        _needsSync: false,
        _lastModified: new Date(prompt.updated_at).getTime()
      }))

      this.setLocalData(STORAGE_KEYS.PROMPTS, remotePrompts)
      console.log(`✅ 从远程获取并缓存了 ${remotePrompts.length} 个提示词`)
      return remotePrompts
    } catch (error) {
      console.warn('获取远程提示词失败，返回空数组:', error)
      return []
    }
  }

  /**
   * 获取所有分类（本地优先）
   */
  async getCategories(): Promise<LocalDataItem<Category>[]> {
    console.log('📱 从本地获取分类数据')
    const localCategories = this.getLocalData<Category>(STORAGE_KEYS.CATEGORIES)

    // 总是优先返回本地数据，即使为空
    if (localCategories.length > 0) {
      console.log(`✅ 本地找到 ${localCategories.length} 个分类`)
      return localCategories
    }

    console.log('📱 本地无数据，尝试从远程获取...')
    try {
      const { data, error } = await this.supabase
        .from('categories')
        .select('*')
        .order('sort_order', { ascending: true })

      if (error) {
        console.warn('远程获取失败，返回空数组:', error)
        return []
      }

      const remoteCategories: LocalDataItem<Category>[] = (data || []).map(category => ({
        ...category,
        _localId: category.id,
        _isLocal: false,
        _needsSync: false,
        _lastModified: new Date(category.updated_at).getTime()
      }))

      this.setLocalData(STORAGE_KEYS.CATEGORIES, remoteCategories)
      console.log(`✅ 从远程获取并缓存了 ${remoteCategories.length} 个分类`)
      return remoteCategories
    } catch (error) {
      console.warn('获取远程分类失败，返回空数组:', error)
      return []
    }
  }

  /**
   * 创建提示词（本地优先）
   */
  async createPrompt(promptData: PromptInsert): Promise<LocalDataItem<Prompt>> {
    const userId = await this.getCurrentUserId()
    if (!userId) throw new Error('用户未登录')

    const localId = this.generateLocalId()
    const now = Date.now()
    
    const localPrompt: LocalDataItem<Prompt> = {
      id: localId,
      ...promptData,
      user_id: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      usage_count: 0,
      _localId: localId,
      _isLocal: true,
      _needsSync: true,
      _lastModified: now
    } as LocalDataItem<Prompt>

    // 立即保存到本地
    const prompts = this.getLocalData<Prompt>(STORAGE_KEYS.PROMPTS)
    prompts.unshift(localPrompt)
    this.setLocalData(STORAGE_KEYS.PROMPTS, prompts)

    // 添加到同步队列
    this.addToSyncQueue({
      type: 'CREATE',
      table: 'prompts',
      data: promptData,
      localId
    })

    console.log('✅ 提示词已保存到本地，等待同步')
    return localPrompt
  }

  /**
   * 创建分类（本地优先）
   */
  async createCategory(categoryData: CategoryInsert): Promise<LocalDataItem<Category>> {
    const userId = await this.getCurrentUserId()
    if (!userId) throw new Error('用户未登录')

    const localId = this.generateLocalId()
    const now = Date.now()

    const localCategory: LocalDataItem<Category> = {
      id: localId,
      ...categoryData,
      user_id: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      _localId: localId,
      _isLocal: true,
      _needsSync: true,
      _lastModified: now
    } as LocalDataItem<Category>

    // 立即保存到本地
    const categories = this.getLocalData<Category>(STORAGE_KEYS.CATEGORIES)
    categories.push(localCategory)
    this.setLocalData(STORAGE_KEYS.CATEGORIES, categories)

    // 添加到同步队列
    this.addToSyncQueue({
      type: 'CREATE',
      table: 'categories',
      data: categoryData,
      localId
    })

    console.log('✅ 分类已保存到本地，等待同步')
    return localCategory
  }

  /**
   * 更新提示词（本地优先）
   */
  async updatePrompt(id: string, promptData: Partial<PromptInsert>): Promise<LocalDataItem<Prompt> | null> {
    const prompts = this.getLocalData<Prompt>(STORAGE_KEYS.PROMPTS)
    const index = prompts.findIndex(p => p.id === id || p._localId === id)

    if (index === -1) {
      console.error('提示词不存在:', id)
      return null
    }

    const now = Date.now()
    const updatedPrompt = {
      ...prompts[index],
      ...promptData,
      updated_at: new Date().toISOString(),
      _needsSync: true,
      _lastModified: now
    }

    // 立即更新本地数据
    prompts[index] = updatedPrompt
    this.setLocalData(STORAGE_KEYS.PROMPTS, prompts)

    // 添加到同步队列
    this.addToSyncQueue({
      type: 'UPDATE',
      table: 'prompts',
      data: { id: prompts[index].id, ...promptData },
      localId: prompts[index]._localId
    })

    console.log('✅ 提示词已在本地更新，等待同步')
    return updatedPrompt
  }

  /**
   * 删除提示词（本地优先）
   */
  async deletePrompt(id: string): Promise<boolean> {
    const prompts = this.getLocalData<Prompt>(STORAGE_KEYS.PROMPTS)
    const index = prompts.findIndex(p => p.id === id || p._localId === id)

    if (index === -1) {
      console.error('提示词不存在:', id)
      return false
    }

    const promptToDelete = prompts[index]

    // 立即从本地删除
    prompts.splice(index, 1)
    this.setLocalData(STORAGE_KEYS.PROMPTS, prompts)

    // 只有非本地数据才需要同步删除
    if (!promptToDelete._isLocal) {
      this.addToSyncQueue({
        type: 'DELETE',
        table: 'prompts',
        data: { id: promptToDelete.id },
        localId: promptToDelete._localId
      })
    }

    console.log('✅ 提示词已在本地删除，等待同步')
    return true
  }

  /**
   * 更新分类（本地优先）
   */
  async updateCategory(id: string, categoryData: Partial<CategoryInsert>): Promise<LocalDataItem<Category> | null> {
    const categories = this.getLocalData<Category>(STORAGE_KEYS.CATEGORIES)
    const index = categories.findIndex(c => c.id === id || c._localId === id)

    if (index === -1) {
      console.error('分类不存在:', id)
      return null
    }

    const now = Date.now()
    const updatedCategory = {
      ...categories[index],
      ...categoryData,
      updated_at: new Date().toISOString(),
      _needsSync: true,
      _lastModified: now
    }

    // 立即更新本地数据
    categories[index] = updatedCategory
    this.setLocalData(STORAGE_KEYS.CATEGORIES, categories)

    // 添加到同步队列
    this.addToSyncQueue({
      type: 'UPDATE',
      table: 'categories',
      data: { id: categories[index].id, ...categoryData },
      localId: categories[index]._localId
    })

    console.log('✅ 分类已在本地更新，等待同步')
    return updatedCategory
  }

  /**
   * 删除分类（本地优先）
   */
  async deleteCategory(id: string): Promise<boolean> {
    const categories = this.getLocalData<Category>(STORAGE_KEYS.CATEGORIES)
    const index = categories.findIndex(c => c.id === id || c._localId === id)

    if (index === -1) {
      console.error('分类不存在:', id)
      return false
    }

    const categoryToDelete = categories[index]

    // 立即从本地删除
    categories.splice(index, 1)
    this.setLocalData(STORAGE_KEYS.CATEGORIES, categories)

    // 只有非本地数据才需要同步删除
    if (!categoryToDelete._isLocal) {
      this.addToSyncQueue({
        type: 'DELETE',
        table: 'categories',
        data: { id: categoryToDelete.id },
        localId: categoryToDelete._localId
      })
    }

    console.log('✅ 分类已在本地删除，等待同步')
    return true
  }

  /**
   * 后台同步到 Supabase
   */
  private async performSync(): Promise<void> {
    if (this.syncInProgress || this.syncQueue.length === 0) return

    this.syncInProgress = true
    console.log(`🔄 开始同步 ${this.syncQueue.length} 个操作`)

    const successfulSyncs: string[] = []

    for (const operation of this.syncQueue) {
      try {
        await this.syncSingleOperation(operation)
        successfulSyncs.push(operation.id)
        console.log(`✅ 同步成功: ${operation.type} ${operation.table}`)
      } catch (error) {
        console.error(`❌ 同步失败: ${operation.type} ${operation.table}`, error)
        operation.retryCount++
        
        // 重试超过 3 次则放弃
        if (operation.retryCount >= 3) {
          console.error(`🚫 放弃同步: ${operation.id}`)
          successfulSyncs.push(operation.id)
        }
      }
    }

    // 移除已成功同步的操作
    this.syncQueue = this.syncQueue.filter(op => !successfulSyncs.includes(op.id))
    this.saveSyncQueue()

    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, Date.now().toString())
    }
    this.syncInProgress = false
    
    console.log(`🎉 同步完成，剩余 ${this.syncQueue.length} 个操作`)
  }

  /**
   * 同步单个操作
   */
  private async syncSingleOperation(operation: SyncOperation): Promise<void> {
    const { type, table, data, localId } = operation

    try {
      if (type === 'CREATE') {
        const { data: result, error } = await this.supabase
          .from(table)
          .insert(data)
          .select()
          .single()

        if (error) throw error

        // 更新本地数据，将本地 ID 替换为远程 ID
        this.updateLocalDataAfterSync(table, localId, result)

      } else if (type === 'UPDATE') {
        const { data: result, error } = await this.supabase
          .from(table)
          .update(data)
          .eq('id', data.id)
          .select()
          .single()

        if (error) throw error

        // 更新本地数据
        this.updateLocalDataAfterSync(table, localId, result)

      } else if (type === 'DELETE') {
        console.log(`🔄 开始远程删除: ${table} ${data.id}`)
        const { error } = await this.supabase
          .from(table)
          .delete()
          .eq('id', data.id)

        if (error) {
          console.error(`❌ 远程删除失败: ${table} ${data.id}`, error)
          throw error
        }

        // 删除操作不需要更新本地数据，因为已经在本地删除了
        console.log(`✅ 远程删除成功: ${table} ${data.id}`)
      }
    } catch (error) {
      console.error(`❌ 同步操作失败: ${type} ${table}`, error)
      throw error
    }
  }

  /**
   * 同步后更新本地数据
   */
  private updateLocalDataAfterSync(table: string, localId: string, remoteData: any): void {
    const storageKey = table === 'prompts' ? STORAGE_KEYS.PROMPTS :
                      table === 'categories' ? STORAGE_KEYS.CATEGORIES :
                      STORAGE_KEYS.TAGS

    const localData = this.getLocalData(storageKey)
    const index = localData.findIndex(item => item._localId === localId)
    
    if (index !== -1) {
      localData[index] = {
        ...remoteData,
        _localId: remoteData.id,
        _isLocal: false,
        _needsSync: false,
        _lastModified: new Date(remoteData.updated_at).getTime()
      }
      this.setLocalData(storageKey, localData)
    }
  }

  /**
   * 启动后台同步
   */
  private startBackgroundSync(): void {
    // 立即执行一次同步
    setTimeout(() => this.performSync(), 1000)

    // 每 30 秒同步一次
    setInterval(() => this.performSync(), 30000)

    // 页面获得焦点时同步
    if (typeof window !== 'undefined') {
      window.addEventListener('focus', () => this.performSync())
      // 网络恢复时同步
      window.addEventListener('online', () => this.performSync())
    }
  }

  /**
   * 立即获取本地数据（不进行任何远程请求）
   * 用于应用启动时的快速显示
   */
  getLocalDataOnly(): { prompts: LocalDataItem<Prompt>[], categories: LocalDataItem<Category>[] } {
    const prompts = this.getLocalData<Prompt>(STORAGE_KEYS.PROMPTS)
    const categories = this.getLocalData<Category>(STORAGE_KEYS.CATEGORIES)

    console.log(`📱 立即获取本地数据: ${prompts.length} 个提示词, ${categories.length} 个分类`)

    return { prompts, categories }
  }

  /**
   * 预加载所有数据到本地
   * 在应用启动时调用，确保本地有数据可以立即显示
   */
  async preloadAllData(): Promise<void> {
    console.log('🚀 开始预加载所有数据到本地')

    try {
      // 并行加载所有数据
      const [prompts, categories] = await Promise.all([
        this.getPrompts(),
        this.getCategories()
      ])

      console.log(`✅ 预加载完成: ${prompts.length} 个提示词, ${categories.length} 个分类`)
    } catch (error) {
      console.error('❌ 预加载失败:', error)
    }
  }

  /**
   * 强制从远程刷新数据
   * 用于手动刷新或定期更新
   */
  async refreshFromRemote(): Promise<void> {
    console.log('🔄 从远程刷新数据')

    try {
      // 清除本地缓存
      this.setLocalData(STORAGE_KEYS.PROMPTS, [])
      this.setLocalData(STORAGE_KEYS.CATEGORIES, [])

      // 重新从远程加载
      await this.preloadAllData()

      console.log('✅ 远程数据刷新完成')
    } catch (error) {
      console.error('❌ 远程刷新失败:', error)
    }
  }

  /**
   * 手动触发同步
   */
  async manualSync(): Promise<void> {
    await this.performSync()
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): { pending: number; lastSync: number | null } {
    const lastSync = typeof window !== 'undefined'
      ? localStorage.getItem(STORAGE_KEYS.LAST_SYNC)
      : null
    return {
      pending: this.syncQueue.length,
      lastSync: lastSync ? parseInt(lastSync) : null
    }
  }

  /**
   * 获取详细的同步状态
   */
  getDetailedSyncStatus(): {
    pending: number;
    lastSync: number | null;
    queue: SyncOperation[];
    localCounts: { prompts: number; categories: number; tags: number };
  } {
    const lastSync = typeof window !== 'undefined'
      ? localStorage.getItem(STORAGE_KEYS.LAST_SYNC)
      : null

    const localPrompts = this.getLocalData<Prompt>(STORAGE_KEYS.PROMPTS)
    const localCategories = this.getLocalData<Category>(STORAGE_KEYS.CATEGORIES)
    const localTags = this.getLocalData<Tag>(STORAGE_KEYS.TAGS)

    return {
      pending: this.syncQueue.length,
      lastSync: lastSync ? parseInt(lastSync) : null,
      queue: [...this.syncQueue], // 返回副本
      localCounts: {
        prompts: localPrompts.length,
        categories: localCategories.length,
        tags: localTags.length
      }
    }
  }

  /**
   * 清除所有本地数据
   */
  clearLocalData(): void {
    if (typeof window !== 'undefined') {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key)
      })
    }
    this.syncQueue = []
    console.log('🗑️ 已清除所有本地数据')
  }
}

// 创建全局实例
export const localFirstStore = new LocalFirstStore()

// 导出类型
export type { LocalDataItem, SyncOperation }
