-- 提示词管理工具初始数据
-- 创建时间: 2025-07-25
-- 描述: 为新用户提供默认分类和示例提示词

-- 注意：这些插入语句需要在用户认证后执行，因为需要 auth.uid()

-- 创建默认分类的函数
CREATE OR REPLACE FUNCTION create_default_categories_for_user()
RETURNS VOID AS $$
DECLARE
    user_uuid UUID := auth.uid();
    category_programming UUID;
    category_writing UUID;
    category_marketing UUID;
    category_productivity UUID;
    category_learning UUID;
BEGIN
    -- 检查用户是否已有分类
    IF EXISTS (SELECT 1 FROM categories WHERE user_id = user_uuid) THEN
        RETURN;
    END IF;

    -- 创建默认分类
    INSERT INTO categories (name, description, color, icon, sort_order, user_id) VALUES
    ('编程开发', '编程相关的提示词，包括代码生成、调试、优化等', '#3b82f6', 'code', 1, user_uuid)
    RETURNING id INTO category_programming;

    INSERT INTO categories (name, description, color, icon, sort_order, user_id) VALUES
    ('文案写作', '写作相关的提示词，包括文章、邮件、创意写作等', '#10b981', 'pen-to-square', 2, user_uuid)
    RETURNING id INTO category_writing;

    INSERT INTO categories (name, description, color, icon, sort_order, user_id) VALUES
    ('营销推广', '营销相关的提示词，包括广告文案、社媒内容等', '#f59e0b', 'bullhorn', 3, user_uuid)
    RETURNING id INTO category_marketing;

    INSERT INTO categories (name, description, color, icon, sort_order, user_id) VALUES
    ('效率工具', '提高工作效率的提示词，包括总结、分析等', '#8b5cf6', 'rocket', 4, user_uuid)
    RETURNING id INTO category_productivity;

    INSERT INTO categories (name, description, color, icon, sort_order, user_id) VALUES
    ('学习教育', '学习相关的提示词，包括解释概念、制作教程等', '#ef4444', 'graduation-cap', 5, user_uuid)
    RETURNING id INTO category_learning;

    -- 创建默认标签
    INSERT INTO tags (name, color, user_id) VALUES
    ('常用', '#6366f1', user_uuid),
    ('工作', '#059669', user_uuid),
    ('学习', '#dc2626', user_uuid),
    ('创意', '#7c3aed', user_uuid),
    ('效率', '#ea580c', user_uuid);

    -- 创建示例提示词
    INSERT INTO prompts (title, description, content, category_id, user_id) VALUES
    (
        'React组件代码生成',
        '生成React函数组件的标准模板',
        '请帮我创建一个React函数组件，要求：
1. 使用TypeScript
2. 包含Props接口定义
3. 使用现代化的函数组件语法
4. 添加适当的注释
5. 组件名称：[组件名称]
6. 主要功能：[功能描述]

请生成完整的组件代码。',
        category_programming,
        user_uuid
    ),
    (
        '产品介绍文案',
        '为产品撰写吸引人的介绍文案',
        '请为以下产品撰写一份吸引人的介绍文案：

产品名称：[产品名称]
产品类型：[产品类型]
主要功能：[核心功能列表]
目标用户：[目标用户群体]
产品优势：[核心优势]

要求：
1. 文案要有吸引力和说服力
2. 突出产品的核心价值
3. 语言简洁明了
4. 包含行动号召
5. 字数控制在200-300字',
        category_marketing,
        user_uuid
    ),
    (
        '会议纪要总结',
        '将会议内容整理成结构化的纪要',
        '请帮我将以下会议内容整理成结构化的会议纪要：

会议主题：[会议主题]
参会人员：[参会人员]
会议时间：[会议时间]
会议内容：[会议原始记录]

请按以下格式整理：
## 会议基本信息
## 讨论要点
## 决策事项
## 行动计划
## 后续跟进

要求简洁明了，重点突出。',
        category_productivity,
        user_uuid
    );

    -- 创建用户偏好设置
    INSERT INTO user_preferences (user_id) VALUES (user_uuid);

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器，在用户首次登录时自动创建默认数据
-- 注意：这需要在应用层处理，因为触发器无法直接监听用户注册事件
