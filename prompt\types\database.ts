// 数据库类型定义
export interface Database {
  public: {
    Tables: {
      categories: {
        Row: Category
        Insert: CategoryInsert
        Update: CategoryUpdate
      }
      tags: {
        Row: Tag
        Insert: TagInsert
        Update: TagUpdate
      }
      prompts: {
        Row: Prompt
        Insert: PromptInsert
        Update: PromptUpdate
      }
      prompt_tags: {
        Row: PromptTag
        Insert: PromptTagInsert
        Update: PromptTagUpdate
      }
      search_history: {
        Row: SearchHistory
        Insert: SearchHistoryInsert
        Update: SearchHistoryUpdate
      }
      user_preferences: {
        Row: UserPreferences
        Insert: UserPreferencesInsert
        Update: UserPreferencesUpdate
      }
    }
  }
}

// 分类相关类型
export interface Category {
  id: string
  name: string
  description: string | null
  color: string
  icon: string
  sort_order: number
  user_id: string
  created_at: string
  updated_at: string
  deleted_at: string | null
}

export interface CategoryInsert {
  name: string
  description?: string
  color?: string
  icon?: string
  sort_order?: number
  user_id: string
}

export interface CategoryUpdate {
  name?: string
  description?: string
  color?: string
  icon?: string
  sort_order?: number
  updated_at?: string
  deleted_at?: string
}

// 标签相关类型
export interface Tag {
  id: string
  name: string
  color: string
  user_id: string
  created_at: string
}

export interface TagInsert {
  name: string
  color?: string
  user_id: string
}

export interface TagUpdate {
  name?: string
  color?: string
}

// 提示词相关类型
export interface Prompt {
  id: string
  title: string
  description: string | null
  content: string
  category_id: string | null
  usage_count: number
  user_id: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
  deleted_at: string | null
}

export interface PromptInsert {
  title: string
  description?: string
  content: string
  category_id?: string
  usage_count?: number
  user_id: string
  metadata?: Record<string, any>
}

export interface PromptUpdate {
  title?: string
  description?: string
  content?: string
  category_id?: string
  usage_count?: number
  metadata?: Record<string, any>
  updated_at?: string
  deleted_at?: string
}

// 提示词标签关联类型
export interface PromptTag {
  prompt_id: string
  tag_id: string
  created_at: string
}

export interface PromptTagInsert {
  prompt_id: string
  tag_id: string
}

export interface PromptTagUpdate {
  // 关联表通常不需要更新
}

// 搜索历史类型
export interface SearchHistory {
  id: string
  search_term: string
  search_count: number
  user_id: string
  last_searched_at: string
}

export interface SearchHistoryInsert {
  search_term: string
  search_count?: number
  user_id: string
}

export interface SearchHistoryUpdate {
  search_count?: number
  last_searched_at?: string
}

// 用户偏好类型
export interface UserPreferences {
  user_id: string
  theme: string
  default_category_id: string | null
  items_per_page: number
  show_usage_count: boolean
  auto_copy_feedback: boolean
  created_at: string
  updated_at: string
}

export interface UserPreferencesInsert {
  user_id: string
  theme?: string
  default_category_id?: string
  items_per_page?: number
  show_usage_count?: boolean
  auto_copy_feedback?: boolean
}

export interface UserPreferencesUpdate {
  theme?: string
  default_category_id?: string
  items_per_page?: number
  show_usage_count?: boolean
  auto_copy_feedback?: boolean
  updated_at?: string
}

// 扩展类型，包含关联数据
export interface PromptWithDetails extends Prompt {
  category?: Category
  tags?: Tag[]
}

export interface CategoryWithCount extends Category {
  prompt_count: number
}

// 搜索和筛选参数
export interface SearchParams {
  query?: string
  categoryId?: string
  tagIds?: string[]
  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'title'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

// API响应类型
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

// 错误类型
export interface DatabaseError {
  message: string
  code?: string
  details?: any
}
