<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词管理工具 - 中国大陆访问入口</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            margin: 1rem;
        }
        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        .description {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .access-methods {
            text-align: left;
            margin: 2rem 0;
        }
        .method {
            background: #f8f9fa;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .method h3 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }
        .method p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 0.5rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀</div>
        <h1>提示词管理工具</h1>
        <p class="description">
            由于网络限制，您可能无法直接访问 Vercel 部署的应用。<br>
            以下是几种访问方法：
        </p>
        
        <div class="warning">
            <strong>注意：</strong> 如果您在中国大陆，建议使用以下替代访问方法。
        </div>
        
        <div class="access-methods">
            <div class="method">
                <h3>方法一：代理访问</h3>
                <p>使用 VPN 或代理服务访问原始 Vercel 链接</p>
                <a href="https://prompt-management-tool-quexwgqa8-sevens-projects-ebf7f705.vercel.app" 
                   class="btn" target="_blank">
                    直接访问 (需要代理)
                </a>
            </div>
            
            <div class="method">
                <h3>方法二：Cloudflare Worker</h3>
                <p>通过 Cloudflare Worker 代理访问（推荐）</p>
                <a href="#" onclick="showCloudflareInstructions()" class="btn">
                    查看配置说明
                </a>
            </div>
            
            <div class="method">
                <h3>方法三：本地部署</h3>
                <p>在本地环境运行应用</p>
                <a href="https://github.com/your-username/prompt-management-tool" 
                   class="btn" target="_blank">
                    GitHub 仓库
                </a>
            </div>
        </div>
        
        <div id="cloudflare-instructions" style="display: none; text-align: left; margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
            <h3>Cloudflare Worker 配置步骤：</h3>
            <ol>
                <li>访问 <a href="https://workers.cloudflare.com/" target="_blank">Cloudflare Workers</a></li>
                <li>注册免费账户</li>
                <li>创建新的 Worker</li>
                <li>复制项目中的 <code>cloudflare-worker.js</code> 代码</li>
                <li>部署 Worker 并使用生成的 URL 访问</li>
            </ol>
        </div>
    </div>
    
    <script>
        function showCloudflareInstructions() {
            const instructions = document.getElementById('cloudflare-instructions');
            instructions.style.display = instructions.style.display === 'none' ? 'block' : 'none';
        }
        
        // 自动检测网络环境
        function checkAccess() {
            const img = new Image();
            img.onload = function() {
                document.querySelector('.warning').innerHTML = 
                    '<strong>✅ 网络检测：</strong> 您的网络可以直接访问 Vercel 服务。';
                document.querySelector('.warning').style.background = '#d4edda';
                document.querySelector('.warning').style.borderColor = '#c3e6cb';
                document.querySelector('.warning').style.color = '#155724';
            };
            img.onerror = function() {
                document.querySelector('.warning').innerHTML = 
                    '<strong>⚠️ 网络检测：</strong> 您的网络无法直接访问 Vercel 服务，建议使用代理方法。';
            };
            img.src = 'https://vercel.com/favicon.ico?' + Date.now();
        }
        
        // 页面加载后检测网络
        window.onload = checkAccess;
    </script>
</body>
</html>
