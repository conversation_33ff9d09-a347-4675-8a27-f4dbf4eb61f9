"use client"

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { IconName, iconMap } from '@/lib/fontawesome'
import { cn } from '@/lib/utils'

interface IconProps {
  name: IconName
  className?: string
  size?: 'xs' | 'sm' | 'lg' | 'xl' | '2xl'
  spin?: boolean
  pulse?: boolean
  color?: string
}

export function Icon({
  name,
  className,
  size,
  spin = false,
  pulse = false,
  color
}: IconProps) {
  // 如果 name 为 undefined 或不存在于 iconMap 中，使用默认图标
  const iconName = name && iconMap[name] ? name : 'question-circle'

  return (
    <FontAwesomeIcon
      icon={iconMap[iconName] as any}
      className={cn(className)}
      size={size}
      spin={spin}
      pulse={pulse}
      color={color}
    />
  )
}

export type { IconName }
