import { createClient } from '@/lib/supabase/client'
import { with<PERSON><PERSON><PERSON>ache, CLIENT_CACHE_KEYS, CLIENT_CACHE_TTL, invalidateClientCache } from '@/lib/client-cache'
import type {
  Tag,
  TagInsert,
  TagUpdate,
  DatabaseError
} from '@/types/database'

const supabase = createClient()

/**
 * 获取用户的所有标签
 */
export async function getTags(): Promise<Tag[]> {
  return withClientCache(
    'tags',
    CLIENT_CACHE_TTL.LONG,
    async () => {
      try {
        const { data, error } = await supabase
          .from('tags')
          .select('*')
          .order('name', { ascending: true })

        if (error) {
          console.error('Supabase 错误:', error)
          // 如果是表不存在的错误，返回空数组而不是抛出错误
          if (error.code === 'PGRST116' || error.message.includes('relation "tags" does not exist')) {
            console.warn('tags 表不存在，返回空数组')
            return []
          }
          throw error
        }

        return data || []
      } catch (error) {
        console.error('获取标签失败:', error)
        // 网络错误或其他问题时返回空数组，避免阻塞整个应用
        return []
      }
    }
  )
}

/**
 * 根据ID获取单个标签
 */
export async function getTagById(id: string): Promise<Tag | null> {
  try {
    const { data, error } = await supabase
      .from('tags')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // 未找到记录
      throw error
    }

    return data
  } catch (error) {
    console.error('获取标签失败:', error)
    throw new Error('获取标签失败')
  }
}

/**
 * 创建新标签
 */
export async function createTag(tag: Omit<TagInsert, 'user_id'>): Promise<Tag> {
  try {
    // 获取当前用户
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { data, error } = await supabase
      .from('tags')
      .insert({
        ...tag,
        user_id: user.id
      })
      .select()
      .single()

    if (error) throw error

    return data
  } catch (error) {
    console.error('创建标签失败:', error)
    throw new Error('创建标签失败')
  }
}

/**
 * 更新标签
 */
export async function updateTag(id: string, updates: TagUpdate): Promise<Tag> {
  try {
    const { data, error } = await supabase
      .from('tags')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    return data
  } catch (error) {
    console.error('更新标签失败:', error)
    throw new Error('更新标签失败')
  }
}

/**
 * 删除标签
 */
export async function deleteTag(id: string): Promise<void> {
  try {
    // 先删除所有关联的提示词标签关系
    await supabase
      .from('prompt_tags')
      .delete()
      .eq('tag_id', id)

    // 然后删除标签本身
    const { error } = await supabase
      .from('tags')
      .delete()
      .eq('id', id)

    if (error) throw error
  } catch (error) {
    console.error('删除标签失败:', error)
    throw new Error('删除标签失败')
  }
}

/**
 * 检查标签名称是否已存在
 */
export async function checkTagNameExists(name: string, excludeId?: string): Promise<boolean> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    let query = supabase
      .from('tags')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', name)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) throw error

    return data.length > 0
  } catch (error) {
    console.error('检查标签名称失败:', error)
    return false
  }
}

/**
 * 获取标签的使用次数
 */
export async function getTagUsageCount(tagId: string): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('prompt_tags')
      .select('*', { count: 'exact', head: true })
      .eq('tag_id', tagId)

    if (error) throw error

    return count || 0
  } catch (error) {
    console.error('获取标签使用次数失败:', error)
    return 0
  }
}

/**
 * 获取热门标签（按使用次数排序）
 */
export async function getPopularTags(limit: number = 10): Promise<Array<Tag & { usage_count: number }>> {
  try {
    const { data, error } = await supabase
      .from('tags')
      .select(`
        *,
        prompt_tags(count)
      `)
      .order('name', { ascending: true })

    if (error) throw error

    // 计算使用次数并排序
    const tagsWithCount = data?.map(tag => ({
      ...tag,
      usage_count: tag.prompt_tags?.length || 0
    }))
    .sort((a, b) => b.usage_count - a.usage_count)
    .slice(0, limit) || []

    return tagsWithCount
  } catch (error) {
    console.error('获取热门标签失败:', error)
    throw new Error('获取热门标签失败')
  }
}

/**
 * 批量创建标签
 */
export async function createTags(tags: Array<Omit<TagInsert, 'user_id'>>): Promise<Tag[]> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { data, error } = await supabase
      .from('tags')
      .insert(
        tags.map(tag => ({
          ...tag,
          user_id: user.id
        }))
      )
      .select()

    if (error) throw error

    return data
  } catch (error) {
    console.error('批量创建标签失败:', error)
    throw new Error('批量创建标签失败')
  }
}

/**
 * 搜索标签
 */
export async function searchTags(query: string): Promise<Tag[]> {
  try {
    const { data, error } = await supabase
      .from('tags')
      .select('*')
      .ilike('name', `%${query}%`)
      .order('name', { ascending: true })
      .limit(20)

    if (error) throw error

    return data || []
  } catch (error) {
    console.error('搜索标签失败:', error)
    throw new Error('搜索标签失败')
  }
}

/**
 * 获取提示词的标签
 */
export async function getPromptTags(promptId: string): Promise<Tag[]> {
  try {
    const { data, error } = await supabase
      .from('prompt_tags')
      .select(`
        tag:tags(*)
      `)
      .eq('prompt_id', promptId)

    if (error) throw error

    return data?.map(item => item.tag).filter(Boolean) || []
  } catch (error) {
    console.error('获取提示词标签失败:', error)
    throw new Error('获取提示词标签失败')
  }
}

/**
 * 为提示词添加标签
 */
export async function addTagToPrompt(promptId: string, tagId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('prompt_tags')
      .insert({
        prompt_id: promptId,
        tag_id: tagId
      })

    if (error) throw error
  } catch (error) {
    console.error('添加标签到提示词失败:', error)
    throw new Error('添加标签到提示词失败')
  }
}

/**
 * 从提示词移除标签
 */
export async function removeTagFromPrompt(promptId: string, tagId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('prompt_tags')
      .delete()
      .eq('prompt_id', promptId)
      .eq('tag_id', tagId)

    if (error) throw error
  } catch (error) {
    console.error('从提示词移除标签失败:', error)
    throw new Error('从提示词移除标签失败')
  }
}
