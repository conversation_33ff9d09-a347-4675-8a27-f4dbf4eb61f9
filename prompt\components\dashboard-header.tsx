"use client"

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { AuthButton } from '@/components/auth-button'
import { ThemeSwitcher } from '@/components/theme-switcher'

interface DashboardHeaderProps {
  onCreatePrompt?: () => void
  children?: React.ReactNode
}

export function DashboardHeader({ onCreatePrompt, children }: DashboardHeaderProps) {
  const pathname = usePathname()

  const navItems = [
    {
      href: '/dashboard',
      label: '提示词',
      icon: 'home' as const,
      active: pathname === '/dashboard'
    },
    {
      href: '/dashboard/search',
      label: '搜索',
      icon: 'search' as const,
      active: pathname === '/dashboard/search'
    },
    {
      href: '/dashboard/categories',
      label: '分类管理',
      icon: 'folder' as const,
      active: pathname === '/dashboard/categories'
    },
    {
      href: '/dashboard/stats',
      label: '数据统计',
      icon: 'chart' as const,
      active: pathname === '/dashboard/stats'
    }
  ]

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* 左侧：Logo 和导航 */}
          <div className="flex items-center gap-8">
            <Link href="/dashboard" className="flex items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg">
                <Icon name="lightbulb" className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                提示词管理
              </span>
            </Link>

            <nav className="hidden md:flex items-center gap-1">
              {navItems.map((item) => (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant={item.active ? "secondary" : "ghost"}
                    size="sm"
                    className="gap-2"
                  >
                    <Icon name={item.icon} className="h-4 w-4" />
                    {item.label}
                  </Button>
                </Link>
              ))}
            </nav>
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex items-center gap-2">
            {onCreatePrompt && (
              <Button
                variant="default"
                size="sm"
                onClick={onCreatePrompt}
                className="hidden sm:flex bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200"
              >
                <Icon name="plus" className="h-4 w-4 mr-2" />
                新建提示词
              </Button>
            )}
            
            {children}
            <ThemeSwitcher />
            <AuthButton />
          </div>
        </div>

        {/* 移动端导航 */}
        <div className="md:hidden border-t border-gray-200 dark:border-gray-700">
          <nav className="flex items-center gap-1 py-2">
            {navItems.map((item) => (
              <Link key={item.href} href={item.href} className="flex-1">
                <Button
                  variant={item.active ? "secondary" : "ghost"}
                  size="sm"
                  className="w-full gap-2"
                >
                  <Icon name={item.icon} className="h-4 w-4" />
                  {item.label}
                </Button>
              </Link>
            ))}
          </nav>
        </div>
      </div>
    </header>
  )
}
