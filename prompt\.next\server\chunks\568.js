exports.id=568,exports.ids=[568],exports.modules={24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(96241);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},32418:(a,b,c)=>{"use strict";c.d(b,{Icon:()=>j});var d=c(60687),e=c(4161),f=c(39163),g=c(97e3);f.Yv.add(g.ao0,g.Uj9,g.jTw,g.LFz,g.e4L,g.KMJ,g.eST,g.MjD,g.QLR,g.jPR,g.MT7,g.yLS,g.pS3,g.DX_,g.qcK,g.t5Z,g.cbP,g.JmV,g.yy,g.G06,g.mRM,g.YpS,g.TJL,g.l9V,g.ITF,g.dB,g.X46,g.GxD,g.v02,g.Jt$,g.w2A,g.Wzs,g.XkK,g.GRI,g.e68,g.zpE,g.iW_,g.wRm,g.ckx,g.vaG,g.z1G,g.Vpu,g.hSh,g.U23,g.yek,g.oZK,g.gr3,g.AaJ,g.KTq,g.Iae,g.h8M,g.ruc,g.vZS,g.Cyq,g.n2W,g.XaT,g.A4h,g.okg,g.a$,g.CYF,g.Hzw,g.CQO,g.Bwz,g.DW4,g.KKb,g.V2x,g.hem,g.D6w,g.jBL,g.ArK,g.GrJ,g.w7B,g.YBv,g.fyG,g._eQ,g.nET,g.rC2,g.p1w,g.zm_,g.kNw,g.R70,g.zqi,g.iHh,g.B9e,g.LPI,g.pvD,g.s6x,g.Pcr,g.Q9Y,g.TBz,g.e5w,g.$Fj);let h={folder:"folder","folder-open":"folder-open",code:"code","pen-to-square":"pen-to-square",bullhorn:"bullhorn",rocket:"rocket","graduation-cap":"graduation-cap",search:"search",plus:"plus",copy:"copy",edit:"edit",trash:"trash",eye:"eye",tags:"tags",heart:"heart",share:"share",download:"download",upload:"upload",star:"star",bookmark:"bookmark",filter:"filter","sort-amount-down":"sort-amount-down","sort-amount-up":"sort-amount-up",grid:"grid",list:"list",cog:"cog",user:"user","sign-out-alt":"sign-out-alt",home:"home","chevron-down":"chevron-down","chevron-up":"chevron-up","chevron-left":"chevron-left","chevron-right":"chevron-right",times:"times",check:"check","exclamation-triangle":"exclamation-triangle","info-circle":"info-circle","question-circle":"question-circle",bars:"bars","ellipsis-v":"ellipsis-v",spinner:"spinner",refresh:"refresh",save:"save",undo:"undo",redo:"redo",expand:"expand",compress:"compress","external-link-alt":"external-link-alt",clipboard:"clipboard","clipboard-check":"clipboard-check",markdown:"markdown","file-code":"file-code","file-text":"file-text",image:"image",video:"video",music:"music",file:"file",calendar:"calendar",clock:"clock",hashtag:"hashtag",at:"at",link:"link",globe:"globe",lock:"lock",unlock:"unlock",shield:"shield",database:"database",server:"server",cloud:"cloud",desktop:"desktop",mobile:"mobile",tablet:"tablet",laptop:"laptop",palette:"palette","paint-brush":"paint-brush",magic:"magic",lightbulb:"lightbulb",flash:"flash",bolt:"bolt",fire:"fire",gem:"gem",crown:"crown",trophy:"trophy",medal:"medal",award:"award",bullseye:"bullseye",flag:"flag","map-marker":"map-marker",compass:"compass",route:"route",map:"map",chart:"chart-bar"};var i=c(96241);function j({name:a,className:b,size:c,spin:f=!1,pulse:g=!1,color:j}){let k=a&&h[a]?a:"question-circle";return(0,d.jsx)(e.g,{icon:h[k],className:(0,i.cn)(b),size:c,spin:f,pulse:g,color:j})}},32945:(a,b,c)=>{"use strict";c.d(b,{a:()=>l});var d=c(60687),e=c(16189),f=c(85814),g=c.n(f),h=c(24934),i=c(32418),j=c(59463),k=c(49196);function l({onCreatePrompt:a,children:b}){let c=(0,e.usePathname)(),f=[{href:"/dashboard",label:"提示词",icon:"home",active:"/dashboard"===c},{href:"/dashboard/search",label:"搜索",icon:"search",active:"/dashboard/search"===c},{href:"/dashboard/categories",label:"分类管理",icon:"folder",active:"/dashboard/categories"===c},{href:"/dashboard/stats",label:"数据统计",icon:"chart",active:"/dashboard/stats"===c}];return(0,d.jsx)("header",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,d.jsxs)("div",{className:"flex items-center gap-8",children:[(0,d.jsxs)(g(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg",children:(0,d.jsx)(i.Icon,{name:"lightbulb",className:"h-5 w-5 text-white"})}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"提示词管理"})]}),(0,d.jsx)("nav",{className:"hidden md:flex items-center gap-1",children:f.map(a=>(0,d.jsx)(g(),{href:a.href,children:(0,d.jsxs)(h.$,{variant:a.active?"secondary":"ghost",size:"sm",className:"gap-2",children:[(0,d.jsx)(i.Icon,{name:a.icon,className:"h-4 w-4"}),a.label]})},a.href))})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[a&&(0,d.jsxs)(h.$,{variant:"default",size:"sm",onClick:a,className:"hidden sm:flex bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200",children:[(0,d.jsx)(i.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),"新建提示词"]}),b,(0,d.jsx)(k.ThemeSwitcher,{}),(0,d.jsx)(j.AuthButton,{})]})]}),(0,d.jsx)("div",{className:"md:hidden border-t border-gray-200 dark:border-gray-700",children:(0,d.jsx)("nav",{className:"flex items-center gap-1 py-2",children:f.map(a=>(0,d.jsx)(g(),{href:a.href,className:"flex-1",children:(0,d.jsxs)(h.$,{variant:a.active?"secondary":"ghost",size:"sm",className:"w-full gap-2",children:[(0,d.jsx)(i.Icon,{name:a.icon,className:"h-4 w-4"}),a.label]})},a.href))})})]})})}},34257:(a,b,c)=>{"use strict";c.d(b,{eQ:()=>H,jG:()=>K,c1:()=>r,Wf:()=>I,VZ:()=>D,Ir:()=>y,bW:()=>p,MN:()=>s,tf:()=>A,R9:()=>J,PH:()=>E,Fw:()=>w,oO:()=>u,FA:()=>G,Q2:()=>C,pB:()=>z,Yv:()=>O,wp:()=>q,qj:()=>x});var d=c(47828);class e{set(a,b,c=3e5){this.cache.set(a,{data:b,timestamp:Date.now(),ttl:c})}get(a){let b=this.cache.get(a);return b?Date.now()-b.timestamp>b.ttl?(this.cache.delete(a),null):b.data:null}delete(a){this.cache.delete(a)}clear(){this.cache.clear()}deletePattern(a){let b=new RegExp(a);for(let a of this.cache.keys())b.test(a)&&this.cache.delete(a)}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}constructor(){this.cache=new Map}}let f=new e,g={CATEGORIES:"categories"},h={MEDIUM:3e5};async function i(a,b,c){let d=f.get(a);if(null!==d)return console.log(`Cache hit: ${a}`),d;console.log(`Cache miss: ${a}`);let e=await c();return f.set(a,e,b),e}class j{generateKey(a,b){if(!b)return a;let c=this.sortObject(b);return`${a}:${JSON.stringify(c)}`}sortObject(a){if(null===a||"object"!=typeof a)return a;if(Array.isArray(a))return a.map(a=>this.sortObject(a));let b={};return Object.keys(a).sort().forEach(c=>{b[c]=this.sortObject(a[c])}),b}set(a,b,c=3e5,d){let e=this.generateKey(a,d),f={data:b,timestamp:Date.now(),ttl:c,version:this.version};this.memoryCache.set(e,f)}get(a,b){let c=this.generateKey(a,b),d=this.memoryCache.get(c);return d?d.version!==this.version||Date.now()-d.timestamp>d.ttl?(this.delete(a,b),null):d.data:null}delete(a,b){let c=this.generateKey(a,b);this.memoryCache.delete(c)}clear(){this.memoryCache.clear()}clearPattern(a){for(let b of this.memoryCache.keys())b.includes(a)&&this.memoryCache.delete(b)}getStats(){return{memorySize:this.memoryCache.size,localStorageSize:0,keys:Array.from(new Set([...Array.from(this.memoryCache.keys())]))}}constructor(){this.memoryCache=new Map,this.version="1.0.0"}}let k=new j,l={CATEGORIES:"categories",PROMPTS:"prompts"},m={MEDIUM:12e4,LONG:6e5};async function n(a,b,c,d){let e=k.get(a,d);if(null!==e)return console.log(`Client cache hit: ${a}`,d),e;console.log(`Client cache miss: ${a}`,d);let f=await c();return k.set(a,f,b,d),f}let o=(0,d.U)();async function p(){return n(l.CATEGORIES,m.LONG,async()=>{try{let{data:{user:a}}=await o.auth.getUser();if(!a)throw Error("用户未登录");let{data:b,error:c}=await o.from("categories").select("*").eq("user_id",a.id).is("deleted_at",null).order("sort_order",{ascending:!0});if(c)throw c;let{data:d,error:e}=await o.from("prompts").select("category_id").eq("user_id",a.id).is("deleted_at",null);if(e)throw e;let f=new Map;return d?.forEach(a=>{let b=a.category_id;b&&f.set(b,(f.get(b)||0)+1)}),b.map(a=>({...a,prompt_count:f.get(a.id)||0}))}catch(a){throw console.error("获取分类失败:",a),Error("获取分类失败")}})}async function q(a){try{var b,c;let{data:{user:d}}=await o.auth.getUser();if(!d)throw Error("用户未登录");let{error:e}=await o.rpc("update_categories_order",{category_ids:a,user_uuid:d.id});if(e)throw e;(b=[g.CATEGORIES]).forEach(a=>{f.deletePattern(a)}),console.log(`Invalidated cache patterns: ${b.join(", ")}`),(c=[l.CATEGORIES]).forEach(a=>{k.clearPattern(a)}),console.log(`Invalidated client cache patterns: ${c.join(", ")}`)}catch(a){throw console.error("更新分类排序失败:",a),Error("更新分类排序失败")}}async function r(a,b){try{let{data:{user:c}}=await o.auth.getUser();if(!c)throw Error("用户未登录");let d=o.from("categories").select("id").eq("user_id",c.id).eq("name",a).is("deleted_at",null);b&&(d=d.neq("id",b));let{data:e,error:f}=await d;if(f)throw f;return e.length>0}catch(a){return console.error("检查分类名称失败:",a),!1}}async function s(a){try{let{count:b,error:c}=await o.from("prompts").select("*",{count:"exact",head:!0}).eq("category_id",a).is("deleted_at",null);if(c)throw c;return b||0}catch(a){return console.error("获取分类提示词数量失败:",a),0}}let t=(0,d.U)();async function u(a={}){let{query:b,categoryId:c,tagIds:d=[],sortBy:e="updated_at",sortOrder:f="desc",limit:g=12,offset:h=0}=a;return!b&&0===h&&g<=12?n(l.PROMPTS,m.MEDIUM,()=>v(a),a):v(a)}async function v(a){try{let{query:b,categoryId:c,tagIds:d=[],sortBy:e="updated_at",sortOrder:f="desc",limit:g=12,offset:h=0}=a,i=t.from("prompts").select(`
        *,
        category:categories(*),
        prompt_tags(
          tag:tags(*)
        )
      `).is("deleted_at",null);c&&(i=i.eq("category_id",c)),d.length>0&&(i=i.in("id",t.from("prompt_tags").select("prompt_id").in("tag_id",d))),b&&(i=i.or(`title.ilike.%${b}%,description.ilike.%${b}%,content.ilike.%${b}%`)),i=(i=i.order(e,{ascending:"asc"===f})).range(h,h+g-1);let{data:j,error:k,count:l}=await i;if(k)throw k;let m=j?.map(a=>({...a,tags:a.prompt_tags?.map(a=>a.tag).filter(Boolean)||[]}))||[],n=t.from("prompts").select("*",{count:"exact",head:!0}).is("deleted_at",null);c&&(n=n.eq("category_id",c)),d.length>0&&(n=n.in("id",t.from("prompt_tags").select("prompt_id").in("tag_id",d))),b&&(n=n.or(`title.ilike.%${b}%,description.ilike.%${b}%,content.ilike.%${b}%`));let{count:o}=await n;return{data:m,total:o||0,page:Math.floor(h/g)+1,pageSize:g,hasMore:h+g<(o||0)}}catch(a){throw console.error("获取提示词列表失败:",a),Error("获取提示词列表失败")}}async function w(a){try{if(a.startsWith("local_"))return console.warn("尝试查询本地ID，跳过远程查询:",a),null;let{data:b,error:c}=await t.from("prompts").select(`
        *,
        category:categories(*),
        prompt_tags(
          tag:tags(*)
        )
      `).eq("id",a).is("deleted_at",null).single();if(c){if("PGRST116"===c.code)return null;throw c}return{...b,tags:b.prompt_tags?.map(a=>a.tag).filter(Boolean)||[]}}catch(a){throw console.error("获取提示词失败:",a),Error("获取提示词失败")}}async function x(a,b,c){try{let{data:d,error:e}=await t.from("prompts").update({...b,updated_at:new Date().toISOString()}).eq("id",a).select().single();if(e)throw e;if(void 0!==c&&(await t.from("prompt_tags").delete().eq("prompt_id",a),c.length>0)){let{error:b}=await t.from("prompt_tags").insert(c.map(b=>({prompt_id:a,tag_id:b})));if(b)throw b}return await w(a)}catch(a){throw console.error("更新提示词失败:",a),Error("更新提示词失败")}}async function y(a){try{let{error:b}=await t.from("prompts").update({deleted_at:new Date().toISOString(),updated_at:new Date().toISOString()}).eq("id",a);if(b)throw b}catch(a){throw console.error("删除提示词失败:",a),Error("删除提示词失败")}}async function z(a){try{let{error:b}=await t.rpc("increment_prompt_usage",{prompt_id:a});if(b)throw b}catch(a){console.error("更新使用次数失败:",a)}}async function A(a=10){try{let{data:b,error:c}=await t.from("prompts").select(`
        *,
        category:categories(*),
        prompt_tags(
          tag:tags(*)
        )
      `).is("deleted_at",null).order("usage_count",{ascending:!1}).limit(a);if(c)throw c;return b?.map(a=>({...a,tags:a.prompt_tags?.map(a=>a.tag).filter(Boolean)||[]}))||[]}catch(a){throw console.error("获取热门提示词失败:",a),Error("获取热门提示词失败")}}let B=(0,d.U)();async function C(){return n("tags",m.LONG,async()=>{try{let{data:a,error:b}=await B.from("tags").select("*").order("name",{ascending:!0});if(b){if(console.error("Supabase 错误:",b),"PGRST116"===b.code||b.message.includes('relation "tags" does not exist'))return console.warn("tags 表不存在，返回空数组"),[];throw b}return a||[]}catch(a){return console.error("获取标签失败:",a),[]}})}async function D(a){try{let{data:{user:b}}=await B.auth.getUser();if(!b)throw Error("用户未登录");let{data:c,error:d}=await B.from("tags").insert({...a,user_id:b.id}).select().single();if(d)throw d;return c}catch(a){throw console.error("创建标签失败:",a),Error("创建标签失败")}}async function E(a=10){try{let{data:b,error:c}=await B.from("tags").select(`
        *,
        prompt_tags(count)
      `).order("name",{ascending:!0});if(c)throw c;return b?.map(a=>({...a,usage_count:a.prompt_tags?.length||0})).sort((a,b)=>b.usage_count-a.usage_count).slice(0,a)||[]}catch(a){throw console.error("获取热门标签失败:",a),Error("获取热门标签失败")}}let F=(0,d.U)();async function G(a=10){try{let{data:b,error:c}=await F.from("search_history").select("*").order("last_searched_at",{ascending:!1}).limit(a);if(c)throw c;return b||[]}catch(a){throw console.error("获取搜索历史失败:",a),Error("获取搜索历史失败")}}async function H(a){try{let{data:{user:b}}=await F.auth.getUser();if(!b)throw Error("用户未登录");let{data:c,error:d}=await F.from("search_history").select("*").eq("user_id",b.id).eq("search_term",a).single();if(d&&"PGRST116"!==d.code)throw d;if(c){let{error:a}=await F.from("search_history").update({search_count:c.search_count+1,last_searched_at:new Date().toISOString()}).eq("id",c.id);if(a)throw a}else{let{error:c}=await F.from("search_history").insert({search_term:a,search_count:1,user_id:b.id});if(c)throw c}}catch(a){console.error("添加搜索历史失败:",a)}}async function I(){try{let{data:{user:a}}=await F.auth.getUser();if(!a)throw Error("用户未登录");let{error:b}=await F.from("search_history").delete().eq("user_id",a.id);if(b)throw b}catch(a){throw console.error("清除搜索历史失败:",a),Error("清除搜索历史失败")}}async function J(a=10){try{let{data:b,error:c}=await F.from("search_history").select("*").order("search_count",{ascending:!1}).limit(a);if(c)throw c;return b||[]}catch(a){throw console.error("获取热门搜索词失败:",a),Error("获取热门搜索词失败")}}async function K(a){let b=`search:${JSON.stringify(a)}`;return!a.query||a.query.length<3?i(b,h.MEDIUM,()=>L(a)):L(a)}async function L(a){try{let{data:{user:b}}=await F.auth.getUser();if(!b)throw Error("用户未登录");let{query:c,title:d,content:e,categoryId:f,tagIds:g=[],dateFrom:h,dateTo:i,usageCountMin:j,usageCountMax:k,sortBy:l="updated_at",sortOrder:m="desc",limit:n=20,offset:o=0}=a,p=F.from("prompts").select(`
        *,
        category:categories(*),
        prompt_tags(
          tag:tags(*)
        )
      `).eq("user_id",b.id).is("deleted_at",null);c&&(p=p.or(`title.ilike.%${c}%,description.ilike.%${c}%,content.ilike.%${c}%`)),d&&(p=p.ilike("title",`%${d}%`)),e&&(p=p.ilike("content",`%${e}%`)),f&&(p=p.eq("category_id",f)),g.length>0&&(p=p.in("id",F.from("prompt_tags").select("prompt_id").in("tag_id",g))),h&&(p=p.gte("created_at",h)),i&&(p=p.lte("created_at",i)),void 0!==j&&(p=p.gte("usage_count",j)),void 0!==k&&(p=p.lte("usage_count",k)),p=p.order(l,{ascending:"asc"===m}).range(o,o+n-1);let{data:q,error:r}=await p;if(r)throw r;return q?.map(a=>({...a,tags:a.prompt_tags?.map(a=>a.tag).filter(Boolean)||[]}))||[]}catch(a){throw console.error("高级搜索失败:",a),Error("高级搜索失败")}}let M=(0,d.U)();async function N(){try{let{data:{user:a}}=await M.auth.getUser();if(!a)throw Error("用户未登录");let b={user_id:a.id,theme:"system",items_per_page:12,show_usage_count:!0,auto_copy_feedback:!0},{data:c,error:d}=await M.from("user_preferences").insert(b).select().single();if(d)throw d;return c}catch(a){throw console.error("创建默认用户偏好设置失败:",a),Error("创建默认用户偏好设置失败")}}async function O(){try{let{data:{user:a}}=await M.auth.getUser();if(!a)throw Error("用户未登录");let{data:b}=await M.from("user_preferences").select("user_id").eq("user_id",a.id).single();if(!b){await N();let{error:a}=await M.rpc("create_default_categories_for_user");a&&console.error("创建默认分类失败:",a)}}catch(a){console.error("初始化用户数据失败:",a)}}},39727:()=>{},44655:(a,b,c)=>{"use strict";function d(a,b){let c=new Date(a),d={year:"numeric",month:"short",day:"numeric",...b};return c.toLocaleDateString("zh-CN",d)}function e(a){let b=new Date(a),c=Math.floor((new Date().getTime()-b.getTime())/1e3);if(c<60)return"刚刚";let d=Math.floor(c/60);if(d<60)return`${d}分钟前`;let e=Math.floor(d/60);if(e<24)return`${e}小时前`;let f=Math.floor(e/24);if(f<7)return`${f}天前`;let g=Math.floor(f/7);if(g<4)return`${g}周前`;let h=Math.floor(f/30);if(h<12)return`${h}个月前`;let i=Math.floor(f/365);return`${i}年前`}function f(a){return"number"!=typeof a||isNaN(a)||!isFinite(a)?"0":a.toLocaleString("zh-CN")}function g(a,b,c="..."){return a.length<=b?a:a.slice(0,b-c.length)+c}function h(a){return/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(a)}c.d(b,{EJ:()=>g,Yq:()=>d,ZV:()=>f,fw:()=>e,o1:()=>h})},47828:(a,b,c)=>{"use strict";c.d(b,{U:()=>e});var d=c(59522);function e(){return(0,d.createBrowserClient)("https://vigxjamjjlxzmuzwxwyl.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZpZ3hqYW1qamx4em11end4d3lsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Nzc4NTAsImV4cCI6MjA2OTA1Mzg1MH0.re45eDFz2pz7Tswcx5sE1bWuCP7MH481XHgsecj578E")}},47990:()=>{},49196:(a,b,c)=>{"use strict";c.d(b,{ThemeSwitcher:()=>u});var d=c(60687),e=c(24934),f=c(43210),g=c(10436),h=c(14952),i=c(13964),j=c(65822),k=c(96241);let l=g.bL,m=g.l9;g.YJ,g.ZL,g.Pb;let n=g.z6;f.forwardRef(({className:a,inset:b,children:c,...e},f)=>(0,d.jsxs)(g.ZP,{ref:f,className:(0,k.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",b&&"pl-8",a),...e,children:[c,(0,d.jsx)(h.A,{className:"ml-auto"})]})).displayName=g.ZP.displayName,f.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.G5,{ref:c,className:(0,k.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...b})).displayName=g.G5.displayName;let o=f.forwardRef(({className:a,sideOffset:b=4,...c},e)=>(0,d.jsx)(g.ZL,{children:(0,d.jsx)(g.UC,{ref:e,sideOffset:b,className:(0,k.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...c})}));o.displayName=g.UC.displayName,f.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(g.q7,{ref:e,className:(0,k.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",b&&"pl-8",a),...c})).displayName=g.q7.displayName,f.forwardRef(({className:a,children:b,checked:c,...e},f)=>(0,d.jsxs)(g.H_,{ref:f,className:(0,k.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:c,...e,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(g.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),b]})).displayName=g.H_.displayName;let p=f.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(g.hN,{ref:e,className:(0,k.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(g.VF,{children:(0,d.jsx)(j.A,{className:"h-2 w-2 fill-current"})})}),b]}));p.displayName=g.hN.displayName,f.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(g.JU,{ref:e,className:(0,k.cn)("px-2 py-1.5 text-sm font-semibold",b&&"pl-8",a),...c})).displayName=g.JU.displayName,f.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.wv,{ref:c,className:(0,k.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=g.wv.displayName;var q=c(21134),r=c(363),s=c(34410),t=c(10218);let u=()=>{let[a,b]=(0,f.useState)(!1),{theme:c,setTheme:g}=(0,t.D)();return((0,f.useEffect)(()=>{b(!0)},[]),a)?(0,d.jsxs)(l,{children:[(0,d.jsx)(m,{asChild:!0,children:(0,d.jsx)(e.$,{variant:"ghost",size:"sm",children:"light"===c?(0,d.jsx)(q.A,{size:16,className:"text-muted-foreground"},"light"):"dark"===c?(0,d.jsx)(r.A,{size:16,className:"text-muted-foreground"},"dark"):(0,d.jsx)(s.A,{size:16,className:"text-muted-foreground"},"system")})}),(0,d.jsx)(o,{className:"w-content",align:"start",children:(0,d.jsxs)(n,{value:c,onValueChange:a=>g(a),children:[(0,d.jsxs)(p,{className:"flex gap-2",value:"light",children:[(0,d.jsx)(q.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"Light"})]}),(0,d.jsxs)(p,{className:"flex gap-2",value:"dark",children:[(0,d.jsx)(r.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"Dark"})]}),(0,d.jsxs)(p,{className:"flex gap-2",value:"system",children:[(0,d.jsx)(s.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"System"})]})]})})]}):null}},59463:(a,b,c)=>{"use strict";c.d(b,{AuthButton:()=>l});var d=c(60687),e=c(85814),f=c.n(e),g=c(24934),h=c(47828),i=c(16189);function j(){let a=(0,i.useRouter)(),b=async()=>{let b=(0,h.U)();await b.auth.signOut(),a.push("/auth/login")};return(0,d.jsx)(g.$,{onClick:b,children:"Logout"})}var k=c(43210);function l(){let[a,b]=(0,k.useState)(null),[c,e]=(0,k.useState)(!0);return((0,h.U)(),c)?(0,d.jsx)("div",{className:"h-8 w-20 bg-gray-200 animate-pulse rounded"}):a?(0,d.jsxs)("div",{className:"flex items-center gap-4",children:["Hey, ",a.email,"!",(0,d.jsx)(j,{})]}):(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(g.$,{asChild:!0,size:"sm",variant:"outline",children:(0,d.jsx)(f(),{href:"/auth/login",children:"Sign in"})}),(0,d.jsx)(g.$,{asChild:!0,size:"sm",variant:"default",children:(0,d.jsx)(f(),{href:"/auth/sign-up",children:"Sign up"})})]})}},59821:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(96241);let g=(0,e.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}}};