(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{19324:()=>{},53570:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},53580:(e,t,r)=>{"use strict";r.d(t,{dj:()=>c});var n=r(12115);let s=0,o=new Map,a=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=[],l={toasts:[]};function d(e){l=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(l,e),i.forEach(e=>{e(l)})}function u(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function c(){let[e,t]=n.useState(l);return n.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var n=r(52596),s=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},77216:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>ei});var n=r(95155),s=r(12115),o=r(47650),a=r(85185),i=r(6101),l=r(37328),d=r(46081),u=r(19178),c=r(34378),p=r(28905),f=r(63655),v=r(39033),m=r(5845),w=r(52712),x=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),y=s.forwardRef((e,t)=>(0,n.jsx)(f.sG.span,{...e,ref:t,style:{...x,...e.style}}));y.displayName="VisuallyHidden";var h="ToastProvider",[g,E,T]=(0,l.N)("Toast"),[b,N]=(0,d.A)("Toast",[T]),[j,R]=b(h),P=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:l}=e,[d,u]=s.useState(null),[c,p]=s.useState(0),f=s.useRef(!1),v=s.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(h,"`. Expected non-empty `string`.")),(0,n.jsx)(g.Provider,{scope:t,children:(0,n.jsx)(j,{scope:t,label:r,duration:o,swipeDirection:a,swipeThreshold:i,toastCount:c,viewport:d,onViewportChange:u,onToastAdd:s.useCallback(()=>p(e=>e+1),[]),onToastRemove:s.useCallback(()=>p(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:v,children:l})})};P.displayName=h;var S="ToastViewport",C=["F8"],A="toast.viewportPause",D="toast.viewportResume",k=s.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=C,label:a="Notifications ({hotkey})",...l}=e,d=R(S,r),c=E(r),p=s.useRef(null),v=s.useRef(null),m=s.useRef(null),w=s.useRef(null),x=(0,i.s)(t,w,d.onViewportChange),y=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),h=d.toastCount>0;s.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null==(t=w.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),s.useEffect(()=>{let e=p.current,t=w.current;if(h&&e&&t){let r=()=>{if(!d.isClosePausedRef.current){let e=new CustomEvent(A);t.dispatchEvent(e),d.isClosePausedRef.current=!0}},n=()=>{if(d.isClosePausedRef.current){let e=new CustomEvent(D);t.dispatchEvent(e),d.isClosePausedRef.current=!1}},s=t=>{e.contains(t.relatedTarget)||n()},o=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",s),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",o),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",s),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",o),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[h,d.isClosePausedRef]);let T=s.useCallback(e=>{let{tabbingDirection:t}=e,r=c().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[c]);return s.useEffect(()=>{let e=w.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,s,o;let r=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null==(n=v.current)||n.focus();return}let i=T({tabbingDirection:a?"backwards":"forwards"}),l=i.findIndex(e=>e===r);Z(i.slice(l+1))?t.preventDefault():a?null==(s=v.current)||s.focus():null==(o=m.current)||o.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,T]),(0,n.jsxs)(u.lg,{ref:p,role:"region","aria-label":a.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:h?void 0:"none"},children:[h&&(0,n.jsx)(_,{ref:v,onFocusFromOutsideViewport:()=>{Z(T({tabbingDirection:"forwards"}))}}),(0,n.jsx)(g.Slot,{scope:r,children:(0,n.jsx)(f.sG.ol,{tabIndex:-1,...l,ref:x})}),h&&(0,n.jsx)(_,{ref:m,onFocusFromOutsideViewport:()=>{Z(T({tabbingDirection:"backwards"}))}})]})});k.displayName=S;var I="ToastFocusProxy",_=s.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:s,...o}=e,a=R(I,r);return(0,n.jsx)(y,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null==(t=a.viewport)?void 0:t.contains(r))||s()}})});_.displayName=I;var L="Toast",F=s.forwardRef((e,t)=>{let{forceMount:r,open:s,defaultOpen:o,onOpenChange:i,...l}=e,[d,u]=(0,m.i)({prop:s,defaultProp:null==o||o,onChange:i,caller:L});return(0,n.jsx)(p.C,{present:r||d,children:(0,n.jsx)(K,{open:d,...l,ref:t,onClose:()=>u(!1),onPause:(0,v.c)(e.onPause),onResume:(0,v.c)(e.onResume),onSwipeStart:(0,a.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,a.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,a.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,a.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),u(!1)})})})});F.displayName=L;var[M,O]=b(L,{onClose(){}}),K=s.forwardRef((e,t)=>{let{__scopeToast:r,type:l="foreground",duration:d,open:c,onClose:p,onEscapeKeyDown:m,onPause:w,onResume:x,onSwipeStart:y,onSwipeMove:h,onSwipeCancel:E,onSwipeEnd:T,...b}=e,N=R(L,r),[j,P]=s.useState(null),S=(0,i.s)(t,e=>P(e)),C=s.useRef(null),k=s.useRef(null),I=d||N.duration,_=s.useRef(0),F=s.useRef(I),O=s.useRef(0),{onToastAdd:K,onToastRemove:V}=N,U=(0,v.c)(()=>{var e;(null==j?void 0:j.contains(document.activeElement))&&(null==(e=N.viewport)||e.focus()),p()}),X=s.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(O.current),_.current=new Date().getTime(),O.current=window.setTimeout(U,e))},[U]);s.useEffect(()=>{let e=N.viewport;if(e){let t=()=>{X(F.current),null==x||x()},r=()=>{let e=new Date().getTime()-_.current;F.current=F.current-e,window.clearTimeout(O.current),null==w||w()};return e.addEventListener(A,r),e.addEventListener(D,t),()=>{e.removeEventListener(A,r),e.removeEventListener(D,t)}}},[N.viewport,I,w,x,X]),s.useEffect(()=>{c&&!N.isClosePausedRef.current&&X(I)},[c,I,N.isClosePausedRef,X]),s.useEffect(()=>(K(),()=>V()),[K,V]);let H=s.useMemo(()=>j?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var n;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(n=t).nodeType===n.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,s=""===t.dataset.radixToastAnnounceExclude;if(!n)if(s){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}),r}(j):null,[j]);return N.viewport?(0,n.jsxs)(n.Fragment,{children:[H&&(0,n.jsx)(G,{__scopeToast:r,role:"status","aria-live":"foreground"===l?"assertive":"polite","aria-atomic":!0,children:H}),(0,n.jsx)(M,{scope:r,onClose:U,children:o.createPortal((0,n.jsx)(g.ItemSlot,{scope:r,children:(0,n.jsx)(u.bL,{asChild:!0,onEscapeKeyDown:(0,a.m)(m,()=>{N.isFocusedToastEscapeKeyDownRef.current||U(),N.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,n.jsx)(f.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":N.swipeDirection,...b,ref:S,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Escape"===e.key&&(null==m||m(e.nativeEvent),e.nativeEvent.defaultPrevented||(N.isFocusedToastEscapeKeyDownRef.current=!0,U()))}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{0===e.button&&(C.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{if(!C.current)return;let t=e.clientX-C.current.x,r=e.clientY-C.current.y,n=!!k.current,s=["left","right"].includes(N.swipeDirection),o=["left","up"].includes(N.swipeDirection)?Math.min:Math.max,a=s?o(0,t):0,i=s?0:o(0,r),l="touch"===e.pointerType?10:2,d={x:a,y:i},u={originalEvent:e,delta:d};n?(k.current=d,Y("toast.swipeMove",h,u,{discrete:!1})):Q(d,N.swipeDirection,l)?(k.current=d,Y("toast.swipeStart",y,u,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(C.current=null)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=k.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),k.current=null,C.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};Q(t,N.swipeDirection,N.swipeThreshold)?Y("toast.swipeEnd",T,n,{discrete:!0}):Y("toast.swipeCancel",E,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),N.viewport)})]}):null}),G=e=>{let{__scopeToast:t,children:r,...o}=e,a=R(L,t),[i,l]=s.useState(!1),[d,u]=s.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,v.c)(e);(0,w.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>l(!0)),s.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),d?null:(0,n.jsx)(c.Z,{asChild:!0,children:(0,n.jsx)(y,{...o,children:i&&(0,n.jsxs)(n.Fragment,{children:[a.label," ",r]})})})},V=s.forwardRef((e,t)=>{let{__scopeToast:r,...s}=e;return(0,n.jsx)(f.sG.div,{...s,ref:t})});V.displayName="ToastTitle";var U=s.forwardRef((e,t)=>{let{__scopeToast:r,...s}=e;return(0,n.jsx)(f.sG.div,{...s,ref:t})});U.displayName="ToastDescription";var X="ToastAction",H=s.forwardRef((e,t)=>{let{altText:r,...s}=e;return r.trim()?(0,n.jsx)(z,{altText:r,asChild:!0,children:(0,n.jsx)(q,{...s,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(X,"`. Expected non-empty `string`.")),null)});H.displayName=X;var W="ToastClose",q=s.forwardRef((e,t)=>{let{__scopeToast:r,...s}=e,o=O(W,r);return(0,n.jsx)(z,{asChild:!0,children:(0,n.jsx)(f.sG.button,{type:"button",...s,ref:t,onClick:(0,a.m)(e.onClick,o.onClose)})})});q.displayName=W;var z=s.forwardRef((e,t)=>{let{__scopeToast:r,altText:s,...o}=e;return(0,n.jsx)(f.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":s||void 0,...o,ref:t})});function Y(e,t,r,n){let{discrete:s}=n,o=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),s?(0,f.hO)(o,a):o.dispatchEvent(a)}var Q=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),s=Math.abs(e.y),o=n>s;return"left"===t||"right"===t?o&&n>r:!o&&s>r};function Z(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var $=r(74466),B=r(54416),J=r(53999);let ee=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(k,{ref:t,className:(0,J.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...s})});ee.displayName=k.displayName;let et=(0,$.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),er=s.forwardRef((e,t)=>{let{className:r,variant:s,...o}=e;return(0,n.jsx)(F,{ref:t,className:(0,J.cn)(et({variant:s}),r),...o})});er.displayName=F.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(H,{ref:t,className:(0,J.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...s})}).displayName=H.displayName;let en=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(q,{ref:t,className:(0,J.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...s,children:(0,n.jsx)(B.A,{className:"h-4 w-4"})})});en.displayName=q.displayName;let es=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(V,{ref:t,className:(0,J.cn)("text-sm font-semibold",r),...s})});es.displayName=V.displayName;let eo=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(U,{ref:t,className:(0,J.cn)("text-sm opacity-90",r),...s})});eo.displayName=U.displayName;var ea=r(53580);function ei(){let{toasts:e}=(0,ea.dj)();return(0,n.jsxs)(P,{children:[e.map(function(e){let{id:t,title:r,description:s,action:o,...a}=e;return(0,n.jsxs)(er,{...a,children:[(0,n.jsxs)("div",{className:"grid gap-1",children:[r&&(0,n.jsx)(es,{children:r}),s&&(0,n.jsx)(eo,{children:s})]}),o,(0,n.jsx)(en,{})]},t)}),(0,n.jsx)(ee,{})]})}},77982:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,19324,23)),Promise.resolve().then(r.bind(r,77216)),Promise.resolve().then(r.bind(r,51362)),Promise.resolve().then(r.t.bind(r,53570,23))}},e=>{e.O(0,[259,352,576,561,441,964,358],()=>e(e.s=77982)),_N_E=e.O()}]);