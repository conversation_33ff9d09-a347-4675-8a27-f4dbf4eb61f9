/**
 * 性能优化配置和工具
 */

// 性能优化的缓存策略
export const PERFORMANCE_CACHE = {
  // 静态数据 - 很少变化
  CATEGORIES: 10 * 60 * 1000,        // 10分钟
  USER_PREFERENCES: 30 * 60 * 1000,  // 30分钟
  
  // 动态数据 - 经常变化
  PROMPTS: 2 * 60 * 1000,            // 2分钟
  SEARCH_RESULTS: 1 * 60 * 1000,     // 1分钟
  
  // 实时数据 - 频繁变化
  PROMPT_DETAIL: 30 * 1000,          // 30秒
  SEARCH_HISTORY: 30 * 1000,         // 30秒
} as const

// 数据库查询优化
export const DB_OPTIMIZATION = {
  // 分页大小
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 50,
  
  // 搜索限制
  SEARCH_MIN_LENGTH: 2,
  SEARCH_MAX_RESULTS: 100,
  
  // 批量操作大小
  BATCH_SIZE: 10,
} as const

// 前端性能优化
export const FRONTEND_OPTIMIZATION = {
  // 虚拟滚动阈值
  VIRTUAL_SCROLL_THRESHOLD: 50,
  
  // 防抖延迟
  DEBOUNCE_DELAY: 300,
  
  // 图片懒加载
  LAZY_LOAD_THRESHOLD: '100px',
  
  // 预加载数量
  PRELOAD_COUNT: 5,
} as const

/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  private static timers = new Map<string, number>()
  
  /**
   * 开始计时
   */
  static start(label: string): void {
    this.timers.set(label, performance.now())
  }
  
  /**
   * 结束计时并输出结果
   */
  static end(label: string): number {
    const startTime = this.timers.get(label)
    if (!startTime) {
      console.warn(`Performance timer "${label}" not found`)
      return 0
    }
    
    const duration = performance.now() - startTime
    console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`)
    this.timers.delete(label)
    return duration
  }
  
  /**
   * 测量函数执行时间
   */
  static async measure<T>(label: string, fn: () => Promise<T>): Promise<T> {
    this.start(label)
    try {
      const result = await fn()
      this.end(label)
      return result
    } catch (error) {
      this.end(label)
      throw error
    }
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

/**
 * 批量处理函数
 */
export async function batchProcess<T, R>(
  items: T[],
  processor: (batch: T[]) => Promise<R[]>,
  batchSize: number = DB_OPTIMIZATION.BATCH_SIZE
): Promise<R[]> {
  const results: R[] = []
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    const batchResults = await processor(batch)
    results.push(...batchResults)
  }
  
  return results
}

/**
 * 预加载数据
 */
export function preloadData<T>(
  loader: () => Promise<T>,
  delay: number = 100
): Promise<T> {
  return new Promise((resolve, reject) => {
    setTimeout(async () => {
      try {
        const data = await loader()
        resolve(data)
      } catch (error) {
        reject(error)
      }
    }, delay)
  })
}

/**
 * 内存使用监控
 */
export function logMemoryUsage(label: string = 'Memory'): void {
  if (typeof window !== 'undefined' && 'memory' in performance) {
    const memory = (performance as any).memory
    console.log(`📊 ${label}:`, {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
    })
  }
}
