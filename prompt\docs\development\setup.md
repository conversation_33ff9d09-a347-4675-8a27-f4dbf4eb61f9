# 🛠️ 开发环境配置

本文档详细介绍如何设置提示词管理工具的开发环境。

## 📋 系统要求

### 必需软件
- **Node.js**: 18.0.0 或更高版本
- **npm**: 9.0.0 或更高版本（或 yarn 1.22.0+）
- **Git**: 2.30.0 或更高版本

### 推荐软件
- **VS Code**: 最新版本
- **Chrome/Edge**: 用于调试和测试
- **Postman**: API 测试（可选）

### 在线服务账户
- **Supabase**: 数据库和认证服务
- **Vercel**: 部署平台（可选）
- **GitHub**: 代码托管

## 🚀 快速开始

### 1. 克隆项目

```bash
# 克隆仓库
git clone <repository-url>
cd prompt

# 或者如果你 fork 了项目
git clone https://github.com/your-username/prompt-management-tool.git
cd prompt-management-tool
```

### 2. 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

### 3. 环境变量配置

```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

在 `.env.local` 中配置以下变量：

```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-anon-key

# 开发环境配置
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 可选配置
NEXT_PUBLIC_ENABLE_DEBUG=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false
```

### 4. 数据库设置

#### 创建 Supabase 项目
1. 访问 [Supabase Dashboard](https://app.supabase.com/)
2. 点击 "New Project"
3. 填写项目信息并创建

#### 执行数据库脚本
```bash
# 在 Supabase SQL Editor 中依次执行：
# 1. database/production/01-schema.sql
# 2. database/production/02-seed.sql
```

### 5. 启动开发服务器

```bash
# 启动开发服务器
npm run dev

# 或使用 Turbopack（更快的构建）
npm run dev --turbo
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 🔧 开发工具配置

### VS Code 配置

#### 推荐扩展
创建 `.vscode/extensions.json`：

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-playwright.playwright",
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-eslint"
  ]
}
```

#### 工作区设置
创建 `.vscode/settings.json`：

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

#### 调试配置
创建 `.vscode/launch.json`：

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/next",
      "args": ["dev"],
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "skipFiles": ["<node_internals>/**"]
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}"
    }
  ]
}
```

### Git 配置

#### Git Hooks
安装 Husky 进行代码质量检查：

```bash
# 安装 Husky
npm install --save-dev husky

# 初始化 Git hooks
npx husky install

# 添加 pre-commit hook
npx husky add .husky/pre-commit "npm run lint && npm run type-check"

# 添加 commit-msg hook
npx husky add .husky/commit-msg "npx commitlint --edit $1"
```

#### 提交信息规范
使用 Conventional Commits 规范：

```bash
# 功能添加
feat: 添加混合搜索功能

# 问题修复
fix: 修复搜索结果显示问题

# 文档更新
docs: 更新 API 文档

# 样式调整
style: 调整按钮样式

# 重构代码
refactor: 重构搜索组件

# 性能优化
perf: 优化数据库查询性能

# 测试相关
test: 添加搜索功能测试

# 构建相关
build: 更新构建配置
```

## 📁 项目结构详解

```
prompt/
├── app/                         # Next.js App Router
│   ├── auth/                    # 认证相关页面
│   ├── dashboard/               # 主应用页面
│   │   ├── categories/          # 分类管理
│   │   ├── search/              # 搜索页面
│   │   ├── stats/               # 数据统计
│   │   └── page.tsx             # 主仪表板
│   ├── globals.css              # 全局样式
│   ├── layout.tsx               # 根布局
│   └── page.tsx                 # 首页
├── components/                  # React 组件
│   ├── ui/                      # 基础 UI 组件
│   ├── auth-button.tsx          # 认证按钮
│   ├── prompt-card.tsx          # 提示词卡片
│   ├── search-bar.tsx           # 搜索栏
│   └── sidebar.tsx              # 侧边栏
├── lib/                         # 工具库
│   ├── database/                # 数据库操作
│   ├── supabase/                # Supabase 配置
│   ├── utils/                   # 工具函数
│   └── cache.ts                 # 缓存管理
├── types/                       # TypeScript 类型
│   └── database.ts              # 数据库类型
├── hooks/                       # React Hooks
│   └── use-toast.ts             # Toast Hook
├── database/                    # 数据库脚本
│   └── production/              # 生产环境脚本
├── docs/                        # 项目文档
├── tests/                       # 测试文件
├── public/                      # 静态资源
├── .env.example                 # 环境变量模板
├── .gitignore                   # Git 忽略文件
├── next.config.ts               # Next.js 配置
├── package.json                 # 项目依赖
├── tailwind.config.ts           # Tailwind 配置
├── tsconfig.json                # TypeScript 配置
└── vercel.json                  # Vercel 部署配置
```

## 🔍 开发工作流

### 1. 功能开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发功能
# 编写代码...

# 3. 运行测试
npm run test
npm run lint
npm run type-check

# 4. 提交代码
git add .
git commit -m "feat: 添加新功能"

# 5. 推送分支
git push origin feature/new-feature

# 6. 创建 Pull Request
```

### 2. 代码质量检查

```bash
# 类型检查
npm run type-check

# 代码格式检查
npm run lint

# 自动修复格式问题
npm run lint:fix

# 代码格式化
npm run format

# 构建检查
npm run build
```

### 3. 测试流程

```bash
# 运行所有测试
npm run test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行 E2E 测试
npm run test:e2e

# 生成覆盖率报告
npm run test:coverage
```

## 🐛 常见问题解决

### 1. 依赖安装问题

```bash
# 清理依赖缓存
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# 或使用 yarn
rm -rf node_modules yarn.lock
yarn cache clean
yarn install
```

### 2. TypeScript 错误

```bash
# 重启 TypeScript 服务
# 在 VS Code 中：Ctrl+Shift+P -> TypeScript: Restart TS Server

# 检查类型错误
npm run type-check

# 生成类型定义
npx supabase gen types typescript --project-id your-project-id > types/supabase.ts
```

### 3. Supabase 连接问题

```bash
# 检查环境变量
echo $NEXT_PUBLIC_SUPABASE_URL
echo $NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY

# 测试连接
npm run test:supabase
```

### 4. 样式问题

```bash
# 重新生成 Tailwind CSS
npm run build:css

# 检查 Tailwind 配置
npx tailwindcss --help
```

## 🔧 高级配置

### 1. 自定义 Next.js 配置

```typescript
// next.config.ts
import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  experimental: {
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  images: {
    domains: ['your-supabase-project.supabase.co'],
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
}

export default nextConfig
```

### 2. 自定义 Tailwind 配置

```typescript
// tailwind.config.ts
import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a',
        },
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
  ],
}

export default config
```

### 3. 环境特定配置

```bash
# 开发环境
.env.local

# 测试环境
.env.test

# 生产环境
.env.production
```

## 📚 学习资源

### 官方文档
- [Next.js 文档](https://nextjs.org/docs)
- [React 文档](https://react.dev/)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [Supabase 文档](https://supabase.com/docs)

### 社区资源
- [Next.js GitHub](https://github.com/vercel/next.js)
- [React GitHub](https://github.com/facebook/react)
- [Supabase GitHub](https://github.com/supabase/supabase)

### 教程和指南
- [Next.js 学习课程](https://nextjs.org/learn)
- [React 官方教程](https://react.dev/learn)
- [TypeScript 手册](https://www.typescriptlang.org/docs/handbook/intro.html)

通过遵循这个开发环境配置指南，您可以快速搭建一个完整的开发环境，并开始贡献代码。
