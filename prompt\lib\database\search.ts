import { createClient } from '@/lib/supabase/client'
import { withCache, CACHE_KEYS, CACHE_TTL, invalidateCache } from '@/lib/cache'
import type {
  SearchHistory,
  SearchHistoryInsert,
  SearchHistoryUpdate,
  DatabaseError
} from '@/types/database'

const supabase = createClient()

/**
 * 获取用户的搜索历史
 */
export async function getSearchHistory(limit: number = 10): Promise<SearchHistory[]> {
  try {
    const { data, error } = await supabase
      .from('search_history')
      .select('*')
      .order('last_searched_at', { ascending: false })
      .limit(limit)

    if (error) throw error

    return data || []
  } catch (error) {
    console.error('获取搜索历史失败:', error)
    throw new Error('获取搜索历史失败')
  }
}

/**
 * 添加或更新搜索历史
 */
export async function addSearchHistory(searchTerm: string): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    // 检查是否已存在该搜索词
    const { data: existing, error: checkError } = await supabase
      .from('search_history')
      .select('*')
      .eq('user_id', user.id)
      .eq('search_term', searchTerm)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError
    }

    if (existing) {
      // 更新现有记录
      const { error: updateError } = await supabase
        .from('search_history')
        .update({
          search_count: existing.search_count + 1,
          last_searched_at: new Date().toISOString()
        })
        .eq('id', existing.id)

      if (updateError) throw updateError
    } else {
      // 创建新记录
      const { error: insertError } = await supabase
        .from('search_history')
        .insert({
          search_term: searchTerm,
          search_count: 1,
          user_id: user.id
        })

      if (insertError) throw insertError
    }
  } catch (error) {
    console.error('添加搜索历史失败:', error)
    // 搜索历史失败不应该影响搜索功能，所以只记录日志
  }
}

/**
 * 清除搜索历史
 */
export async function clearSearchHistory(): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { error } = await supabase
      .from('search_history')
      .delete()
      .eq('user_id', user.id)

    if (error) throw error
  } catch (error) {
    console.error('清除搜索历史失败:', error)
    throw new Error('清除搜索历史失败')
  }
}

/**
 * 删除单个搜索历史记录
 */
export async function deleteSearchHistoryItem(id: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('search_history')
      .delete()
      .eq('id', id)

    if (error) throw error
  } catch (error) {
    console.error('删除搜索历史记录失败:', error)
    throw new Error('删除搜索历史记录失败')
  }
}

/**
 * 获取热门搜索词
 */
export async function getPopularSearchTerms(limit: number = 10): Promise<SearchHistory[]> {
  try {
    const { data, error } = await supabase
      .from('search_history')
      .select('*')
      .order('search_count', { ascending: false })
      .limit(limit)

    if (error) throw error

    return data || []
  } catch (error) {
    console.error('获取热门搜索词失败:', error)
    throw new Error('获取热门搜索词失败')
  }
}

/**
 * 搜索建议（基于历史搜索）
 */
export async function getSearchSuggestions(query: string, limit: number = 5): Promise<string[]> {
  try {
    const { data, error } = await supabase
      .from('search_history')
      .select('search_term')
      .ilike('search_term', `%${query}%`)
      .order('search_count', { ascending: false })
      .limit(limit)

    if (error) throw error

    return data?.map(item => item.search_term) || []
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    return []
  }
}

/**
 * 全文搜索提示词
 */
export async function searchPrompts(
  query: string, 
  options: {
    categoryId?: string
    tagIds?: string[]
    limit?: number
    offset?: number
  } = {}
) {
  try {
    const { categoryId, tagIds = [], limit = 20, offset = 0 } = options

    // 记录搜索历史
    if (query.trim()) {
      await addSearchHistory(query.trim())
    }

    let queryBuilder = supabase
      .from('prompts')
      .select(`
        *,
        category:categories(*),
        prompt_tags(
          tag:tags(*)
        )
      `)
      .is('deleted_at', null)

    // 全文搜索
    if (query.trim()) {
      queryBuilder = queryBuilder.or(`title.ilike.%${query}%,description.ilike.%${query}%,content.ilike.%${query}%`)
    }

    // 分类筛选
    if (categoryId) {
      queryBuilder = queryBuilder.eq('category_id', categoryId)
    }

    // 标签筛选
    if (tagIds.length > 0) {
      queryBuilder = queryBuilder.in('id', 
        supabase
          .from('prompt_tags')
          .select('prompt_id')
          .in('tag_id', tagIds)
      )
    }

    // 排序和分页
    queryBuilder = queryBuilder
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data, error } = await queryBuilder

    if (error) throw error

    // 处理数据，展平标签
    const processedData = data?.map(item => ({
      ...item,
      tags: item.prompt_tags?.map(pt => pt.tag).filter(Boolean) || []
    })) || []

    return processedData
  } catch (error) {
    console.error('搜索提示词失败:', error)
    throw new Error('搜索提示词失败')
  }
}

/**
 * 高级搜索（支持更复杂的查询）
 */
export async function advancedSearch(params: {
  query?: string
  title?: string
  content?: string
  categoryId?: string
  tagIds?: string[]
  dateFrom?: string
  dateTo?: string
  usageCountMin?: number
  usageCountMax?: number
  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'title'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}) {
  // 生成缓存键
  const cacheKey = `search:${JSON.stringify(params)}`

  // 只对简单搜索进行缓存（复杂搜索结果变化频繁）
  const shouldCache = !params.query || params.query.length < 3

  if (shouldCache) {
    return withCache(cacheKey, CACHE_TTL.MEDIUM, () => executeAdvancedSearch(params))
  } else {
    return executeAdvancedSearch(params)
  }
}

async function executeAdvancedSearch(params: {
  query?: string
  title?: string
  content?: string
  categoryId?: string
  tagIds?: string[]
  dateFrom?: string
  dateTo?: string
  usageCountMin?: number
  usageCountMax?: number
  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'title'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}) {
  try {
    // 获取当前用户
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      throw new Error('用户未登录')
    }

    const {
      query,
      title,
      content,
      categoryId,
      tagIds = [],
      dateFrom,
      dateTo,
      usageCountMin,
      usageCountMax,
      sortBy = 'updated_at',
      sortOrder = 'desc',
      limit = 20,
      offset = 0
    } = params

    let queryBuilder = supabase
      .from('prompts')
      .select(`
        *,
        category:categories(*),
        prompt_tags(
          tag:tags(*)
        )
      `)
      .eq('user_id', user.id)  // 添加用户ID过滤
      .is('deleted_at', null)

    // 通用搜索
    if (query) {
      queryBuilder = queryBuilder.or(`title.ilike.%${query}%,description.ilike.%${query}%,content.ilike.%${query}%`)
    }

    // 标题搜索
    if (title) {
      queryBuilder = queryBuilder.ilike('title', `%${title}%`)
    }

    // 内容搜索
    if (content) {
      queryBuilder = queryBuilder.ilike('content', `%${content}%`)
    }

    // 分类筛选
    if (categoryId) {
      queryBuilder = queryBuilder.eq('category_id', categoryId)
    }

    // 标签筛选
    if (tagIds.length > 0) {
      queryBuilder = queryBuilder.in('id', 
        supabase
          .from('prompt_tags')
          .select('prompt_id')
          .in('tag_id', tagIds)
      )
    }

    // 日期范围筛选
    if (dateFrom) {
      queryBuilder = queryBuilder.gte('created_at', dateFrom)
    }
    if (dateTo) {
      queryBuilder = queryBuilder.lte('created_at', dateTo)
    }

    // 使用次数筛选
    if (usageCountMin !== undefined) {
      queryBuilder = queryBuilder.gte('usage_count', usageCountMin)
    }
    if (usageCountMax !== undefined) {
      queryBuilder = queryBuilder.lte('usage_count', usageCountMax)
    }

    // 排序和分页
    queryBuilder = queryBuilder
      .order(sortBy, { ascending: sortOrder === 'asc' })
      .range(offset, offset + limit - 1)

    const { data, error } = await queryBuilder

    if (error) throw error

    // 处理数据，展平标签
    const processedData = data?.map(item => ({
      ...item,
      tags: item.prompt_tags?.map(pt => pt.tag).filter(Boolean) || []
    })) || []

    return processedData
  } catch (error) {
    console.error('高级搜索失败:', error)
    throw new Error('高级搜索失败')
  }
}
