"use client"

import { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@/components/ui/icon'
import { useToast } from '@/hooks/use-toast'
import { getCategories, getTags } from '@/lib/database'
import type { Category, Tag } from '@/types/database'

interface AdvancedSearchParams {
  query?: string
  title?: string
  content?: string
  categoryId?: string
  tagIds?: string[]
  dateFrom?: string
  dateTo?: string
  usageCountMin?: number
  usageCountMax?: number
  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'title'
  sortOrder?: 'asc' | 'desc'
}

interface AdvancedSearchModalProps {
  isOpen: boolean
  onClose: () => void
  onSearch: (params: AdvancedSearchParams) => void
  initialParams?: AdvancedSearchParams
}

export function AdvancedSearchModal({
  isOpen,
  onClose,
  onSearch,
  initialParams = {}
}: AdvancedSearchModalProps) {
  const [query, setQuery] = useState('')
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  const [selectedCategoryId, setSelectedCategoryId] = useState('')
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([])
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')
  const [usageCountMin, setUsageCountMin] = useState('')
  const [usageCountMax, setUsageCountMax] = useState('')
  const [sortBy, setSortBy] = useState<'created_at' | 'updated_at' | 'usage_count' | 'title'>('updated_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  const [categories, setCategories] = useState<Category[]>([])
  const [tags, setTags] = useState<Tag[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const { toast } = useToast()

  // 加载数据
  useEffect(() => {
    if (isOpen) {
      loadData()
    }
  }, [isOpen])

  // 填充初始参数 - 使用 JSON.stringify 来比较对象内容而不是引用
  useEffect(() => {
    if (initialParams) {
      setQuery(initialParams.query || '')
      setTitle(initialParams.title || '')
      setContent(initialParams.content || '')
      setSelectedCategoryId(initialParams.categoryId || '')
      setSelectedTagIds(initialParams.tagIds || [])
      setDateFrom(initialParams.dateFrom || '')
      setDateTo(initialParams.dateTo || '')
      setUsageCountMin(initialParams.usageCountMin?.toString() || '')
      setUsageCountMax(initialParams.usageCountMax?.toString() || '')
      setSortBy(initialParams.sortBy || 'updated_at')
      setSortOrder(initialParams.sortOrder || 'desc')
    }
  }, [JSON.stringify(initialParams)]) // 使用 JSON.stringify 避免对象引用问题

  const loadData = async () => {
    try {
      setIsLoading(true)
      const [categoriesData, tagsData] = await Promise.all([
        getCategories(),
        getTags()
      ])
      setCategories(categoriesData)
      setTags(tagsData)
    } catch (error) {
      console.error('加载数据失败:', error)
      toast({
        title: "加载失败",
        description: "无法加载分类和标签数据",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const params: AdvancedSearchParams = {
      query: query.trim() || undefined,
      title: title.trim() || undefined,
      content: content.trim() || undefined,
      categoryId: selectedCategoryId || undefined,
      tagIds: selectedTagIds.length > 0 ? selectedTagIds : undefined,
      dateFrom: dateFrom || undefined,
      dateTo: dateTo || undefined,
      usageCountMin: usageCountMin ? parseInt(usageCountMin) : undefined,
      usageCountMax: usageCountMax ? parseInt(usageCountMax) : undefined,
      sortBy,
      sortOrder
    }

    onSearch(params)
    onClose()
  }

  const handleReset = () => {
    setQuery('')
    setTitle('')
    setContent('')
    setSelectedCategoryId('')
    setSelectedTagIds([])
    setDateFrom('')
    setDateTo('')
    setUsageCountMin('')
    setUsageCountMax('')
    setSortBy('updated_at')
    setSortOrder('desc')
  }

  const toggleTag = (tagId: string) => {
    setSelectedTagIds(prev => 
      prev.includes(tagId) 
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    )
  }

  const selectedTags = tags.filter(tag => selectedTagIds.includes(tag.id))

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>高级搜索</DialogTitle>
          <DialogDescription>
            使用多个条件精确搜索提示词
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Icon name="spinner" className="h-6 w-6 animate-spin mr-2" />
            <span>加载中...</span>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="flex-1 overflow-hidden flex flex-col">
            <div className="flex-1 overflow-y-auto space-y-4">
              {/* 通用搜索 */}
              <div className="space-y-2">
                <Label htmlFor="query">关键词搜索</Label>
                <Input
                  id="query"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="在标题、描述、内容中搜索"
                />
              </div>

              {/* 标题搜索 */}
              <div className="space-y-2">
                <Label htmlFor="title">标题</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="搜索标题"
                />
              </div>

              {/* 内容搜索 */}
              <div className="space-y-2">
                <Label htmlFor="content">内容</Label>
                <Input
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="搜索内容"
                />
              </div>

              {/* 分类筛选 */}
              <div className="space-y-2">
                <Label htmlFor="category">分类</Label>
                <select
                  id="category"
                  value={selectedCategoryId}
                  onChange={(e) => setSelectedCategoryId(e.target.value)}
                  className="w-full px-3 py-2 border border-input rounded-md bg-background"
                >
                  <option value="">所有分类</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* 标签筛选 */}
              <div className="space-y-2">
                <Label>标签</Label>
                
                {/* 已选标签 */}
                {selectedTags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-2">
                    {selectedTags.map((tag) => (
                      <Badge
                        key={tag.id}
                        variant="secondary"
                        className="cursor-pointer"
                        style={{ 
                          backgroundColor: `${tag.color}20`,
                          color: tag.color 
                        }}
                        onClick={() => toggleTag(tag.id)}
                      >
                        {tag.name}
                        <Icon name="times" className="h-3 w-3 ml-1" />
                      </Badge>
                    ))}
                  </div>
                )}

                {/* 可选标签 */}
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {tags
                    .filter(tag => !selectedTagIds.includes(tag.id))
                    .map((tag) => (
                      <Badge
                        key={tag.id}
                        variant="outline"
                        className="cursor-pointer hover:bg-gray-100"
                        onClick={() => toggleTag(tag.id)}
                      >
                        <Icon name="plus" className="h-3 w-3 mr-1" />
                        {tag.name}
                      </Badge>
                    ))}
                </div>
              </div>

              {/* 日期范围 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dateFrom">创建日期从</Label>
                  <Input
                    id="dateFrom"
                    type="date"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dateTo">创建日期到</Label>
                  <Input
                    id="dateTo"
                    type="date"
                    value={dateTo}
                    onChange={(e) => setDateTo(e.target.value)}
                  />
                </div>
              </div>

              {/* 使用次数范围 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="usageCountMin">最少使用次数</Label>
                  <Input
                    id="usageCountMin"
                    type="number"
                    min="0"
                    value={usageCountMin}
                    onChange={(e) => setUsageCountMin(e.target.value)}
                    placeholder="0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="usageCountMax">最多使用次数</Label>
                  <Input
                    id="usageCountMax"
                    type="number"
                    min="0"
                    value={usageCountMax}
                    onChange={(e) => setUsageCountMax(e.target.value)}
                    placeholder="无限制"
                  />
                </div>
              </div>

              {/* 排序 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sortBy">排序字段</Label>
                  <select
                    id="sortBy"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background"
                  >
                    <option value="updated_at">更新时间</option>
                    <option value="created_at">创建时间</option>
                    <option value="usage_count">使用次数</option>
                    <option value="title">标题</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sortOrder">排序方向</Label>
                  <select
                    id="sortOrder"
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as any)}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background"
                  >
                    <option value="desc">降序</option>
                    <option value="asc">升序</option>
                  </select>
                </div>
              </div>
            </div>

            {/* 底部按钮 */}
            <div className="flex items-center justify-between pt-4 border-t">
              <Button type="button" variant="outline" onClick={handleReset}>
                <Icon name="refresh" className="h-4 w-4 mr-2" />
                重置
              </Button>
              
              <div className="flex items-center gap-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button type="submit">
                  <Icon name="search" className="h-4 w-4 mr-2" />
                  搜索
                </Button>
              </div>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
