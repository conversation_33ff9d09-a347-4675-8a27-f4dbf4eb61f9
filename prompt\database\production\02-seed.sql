-- =====================================================
-- 提示词管理工具 - 初始化数据
-- =====================================================
-- 版本: 1.0.0
-- 创建时间: 2025-07-26
-- 描述: 为新用户创建默认分类和示例数据
-- 注意: 此脚本需要在用户注册后执行，或通过应用程序自动执行
-- =====================================================

-- =====================================================
-- 1. 默认分类数据
-- =====================================================

-- 创建默认分类的函数
CREATE OR REPLACE FUNCTION create_default_categories(target_user_id UUID)
RETURNS VOID AS $$
BEGIN
    -- 检查用户是否已有分类
    IF NOT EXISTS (SELECT 1 FROM categories WHERE user_id = target_user_id) THEN
        
        -- 插入默认分类
        INSERT INTO categories (name, description, color, icon, sort_order, user_id) VALUES
        ('AI编程', '人工智能和编程相关的提示词', '#3b82f6', 'code', 1, target_user_id),
        ('文案写作', '文案创作和写作相关的提示词', '#10b981', 'pen-to-square', 2, target_user_id),
        ('数据分析', '数据处理和分析相关的提示词', '#f59e0b', 'chart-bar', 3, target_user_id),
        ('创意设计', '设计和创意相关的提示词', '#ef4444', 'palette', 4, target_user_id),
        ('学习教育', '学习和教育相关的提示词', '#8b5cf6', 'graduation-cap', 5, target_user_id),
        ('商业营销', '商业和营销相关的提示词', '#06b6d4', 'briefcase', 6, target_user_id),
        ('生活助手', '日常生活和实用工具类提示词', '#84cc16', 'house', 7, target_user_id),
        ('其他', '未分类或其他类型的提示词', '#6b7280', 'folder', 8, target_user_id);
        
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 2. 默认标签数据
-- =====================================================

-- 创建默认标签的函数
CREATE OR REPLACE FUNCTION create_default_tags(target_user_id UUID)
RETURNS VOID AS $$
BEGIN
    -- 检查用户是否已有标签
    IF NOT EXISTS (SELECT 1 FROM tags WHERE user_id = target_user_id) THEN
        
        -- 插入默认标签
        INSERT INTO tags (name, color, user_id) VALUES
        -- 技术类标签
        ('Python', '#3776ab', target_user_id),
        ('JavaScript', '#f7df1e', target_user_id),
        ('React', '#61dafb', target_user_id),
        ('Vue', '#4fc08d', target_user_id),
        ('Node.js', '#339933', target_user_id),
        ('AI', '#ff6b6b', target_user_id),
        ('机器学习', '#4ecdc4', target_user_id),
        ('深度学习', '#45b7d1', target_user_id),
        
        -- 内容类标签
        ('SEO', '#ffa726', target_user_id),
        ('社交媒体', '#ab47bc', target_user_id),
        ('邮件营销', '#26a69a', target_user_id),
        ('产品描述', '#66bb6a', target_user_id),
        ('博客文章', '#42a5f5', target_user_id),
        ('新闻稿', '#ef5350', target_user_id),
        
        -- 功能类标签
        ('翻译', '#8d6e63', target_user_id),
        ('总结', '#78909c', target_user_id),
        ('分析', '#ffd54f', target_user_id),
        ('创意', '#ff8a65', target_user_id),
        ('教学', '#a1c181', target_user_id),
        ('调试', '#f06292', target_user_id),
        
        -- 行业类标签
        ('电商', '#7986cb', target_user_id),
        ('金融', '#4db6ac', target_user_id),
        ('医疗', '#aed581', target_user_id),
        ('教育', '#ffb74d', target_user_id),
        ('游戏', '#f48fb1', target_user_id),
        
        -- 难度类标签
        ('初级', '#c8e6c9', target_user_id),
        ('中级', '#fff3e0', target_user_id),
        ('高级', '#ffcdd2', target_user_id),
        
        -- 用途类标签
        ('工作', '#e1f5fe', target_user_id),
        ('学习', '#f3e5f5', target_user_id),
        ('娱乐', '#fff8e1', target_user_id);
        
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 3. 示例提示词数据
-- =====================================================

-- 创建示例提示词的函数
CREATE OR REPLACE FUNCTION create_sample_prompts(target_user_id UUID)
RETURNS VOID AS $$
DECLARE
    ai_category_id UUID;
    writing_category_id UUID;
    data_category_id UUID;
    life_category_id UUID;
    
    ai_tag_id UUID;
    python_tag_id UUID;
    analysis_tag_id UUID;
    beginner_tag_id UUID;
    work_tag_id UUID;
BEGIN
    -- 获取分类ID
    SELECT id INTO ai_category_id FROM categories WHERE name = 'AI编程' AND user_id = target_user_id;
    SELECT id INTO writing_category_id FROM categories WHERE name = '文案写作' AND user_id = target_user_id;
    SELECT id INTO data_category_id FROM categories WHERE name = '数据分析' AND user_id = target_user_id;
    SELECT id INTO life_category_id FROM categories WHERE name = '生活助手' AND user_id = target_user_id;
    
    -- 获取标签ID
    SELECT id INTO ai_tag_id FROM tags WHERE name = 'AI' AND user_id = target_user_id;
    SELECT id INTO python_tag_id FROM tags WHERE name = 'Python' AND user_id = target_user_id;
    SELECT id INTO analysis_tag_id FROM tags WHERE name = '分析' AND user_id = target_user_id;
    SELECT id INTO beginner_tag_id FROM tags WHERE name = '初级' AND user_id = target_user_id;
    SELECT id INTO work_tag_id FROM tags WHERE name = '工作' AND user_id = target_user_id;
    
    -- 检查用户是否已有提示词
    IF NOT EXISTS (SELECT 1 FROM prompts WHERE user_id = target_user_id) THEN
        
        -- 插入示例提示词
        WITH inserted_prompts AS (
            INSERT INTO prompts (title, description, content, category_id, user_id, usage_count) VALUES
            (
                'Python代码审查助手',
                '帮助审查Python代码，发现潜在问题并提供改进建议',
                '请作为一个经验丰富的Python开发者，审查以下代码：

[在这里粘贴你的Python代码]

请从以下几个方面进行分析：
1. 代码质量和可读性
2. 性能优化建议
3. 潜在的bug或安全问题
4. 最佳实践建议
5. 代码结构和设计模式

请提供具体的改进建议和修改后的代码示例。',
                ai_category_id,
                target_user_id,
                5
            ),
            (
                '产品文案生成器',
                '为产品创建吸引人的营销文案和描述',
                '请为以下产品创建一份吸引人的营销文案：

产品名称：[产品名称]
产品类型：[产品类型]
目标用户：[目标用户群体]
主要功能：[核心功能列表]
独特卖点：[产品优势]

请生成：
1. 一句话产品描述（20字以内）
2. 详细产品介绍（100-200字）
3. 三个核心卖点
4. 适合社交媒体的短文案
5. 电商平台的产品标题

文案风格要求：[专业/活泼/温馨/科技感等]',
                writing_category_id,
                target_user_id,
                3
            ),
            (
                '数据分析报告模板',
                '生成专业的数据分析报告结构和内容',
                '请基于以下数据分析需求，生成一份专业的分析报告：

分析主题：[分析主题]
数据来源：[数据来源]
分析目标：[分析目标]
关键指标：[关键指标列表]

请按以下结构生成报告：

## 执行摘要
- 核心发现
- 主要建议

## 数据概览
- 数据来源和质量
- 分析方法

## 详细分析
- 趋势分析
- 对比分析
- 异常值分析

## 结论与建议
- 关键洞察
- 行动建议
- 风险提示

请确保报告逻辑清晰，数据支撑充分。',
                data_category_id,
                target_user_id,
                2
            ),
            (
                '会议纪要整理助手',
                '将会议录音或笔记整理成结构化的会议纪要',
                '请将以下会议内容整理成专业的会议纪要：

[在这里粘贴会议录音转文字或会议笔记]

请按以下格式整理：

## 会议信息
- 会议主题：
- 会议时间：
- 参会人员：
- 会议主持：

## 议题讨论
### 议题一：[议题名称]
- 讨论要点：
- 不同观点：
- 达成共识：

### 议题二：[议题名称]
- 讨论要点：
- 不同观点：
- 达成共识：

## 决议事项
1. [决议内容]
2. [决议内容]

## 行动计划
| 任务 | 负责人 | 截止时间 | 备注 |
|------|--------|----------|------|
|      |        |          |      |

## 下次会议
- 时间：
- 议题：',
                life_category_id,
                target_user_id,
                1
            )
            RETURNING id, title
        )
        
        -- 为示例提示词添加标签
        INSERT INTO prompt_tags (prompt_id, tag_id)
        SELECT 
            p.id,
            CASE 
                WHEN p.title = 'Python代码审查助手' THEN python_tag_id
                WHEN p.title = '产品文案生成器' THEN work_tag_id
                WHEN p.title = '数据分析报告模板' THEN analysis_tag_id
                WHEN p.title = '会议纪要整理助手' THEN work_tag_id
            END
        FROM inserted_prompts p
        WHERE CASE 
            WHEN p.title = 'Python代码审查助手' THEN python_tag_id
            WHEN p.title = '产品文案生成器' THEN work_tag_id
            WHEN p.title = '数据分析报告模板' THEN analysis_tag_id
            WHEN p.title = '会议纪要整理助手' THEN work_tag_id
        END IS NOT NULL;
        
        -- 添加第二个标签
        INSERT INTO prompt_tags (prompt_id, tag_id)
        SELECT 
            p.id,
            CASE 
                WHEN p.title = 'Python代码审查助手' THEN ai_tag_id
                WHEN p.title = '产品文案生成器' THEN beginner_tag_id
                WHEN p.title = '数据分析报告模板' THEN work_tag_id
                WHEN p.title = '会议纪要整理助手' THEN beginner_tag_id
            END
        FROM inserted_prompts p
        WHERE CASE 
            WHEN p.title = 'Python代码审查助手' THEN ai_tag_id
            WHEN p.title = '产品文案生成器' THEN beginner_tag_id
            WHEN p.title = '数据分析报告模板' THEN work_tag_id
            WHEN p.title = '会议纪要整理助手' THEN beginner_tag_id
        END IS NOT NULL;
        
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. 用户初始化函数
-- =====================================================

-- 完整的用户初始化函数
CREATE OR REPLACE FUNCTION initialize_user_data(target_user_id UUID)
RETURNS VOID AS $$
BEGIN
    -- 创建默认分类
    PERFORM create_default_categories(target_user_id);
    
    -- 创建默认标签
    PERFORM create_default_tags(target_user_id);
    
    -- 创建示例提示词
    PERFORM create_sample_prompts(target_user_id);
    
    -- 创建用户偏好设置
    INSERT INTO user_preferences (user_id)
    VALUES (target_user_id)
    ON CONFLICT (user_id) DO NOTHING;
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 初始化数据脚本完成
-- =====================================================

-- 使用说明：
-- 1. 在应用程序中，当新用户注册后调用：
--    SELECT initialize_user_data(auth.uid());
-- 
-- 2. 或者为现有用户手动初始化：
--    SELECT initialize_user_data('用户UUID');
-- 
-- 3. 也可以单独调用各个函数：
--    SELECT create_default_categories('用户UUID');
--    SELECT create_default_tags('用户UUID');
--    SELECT create_sample_prompts('用户UUID');
