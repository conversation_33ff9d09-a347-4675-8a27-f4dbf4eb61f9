"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[194],{319:(e,s,t)=>{t.d(s,{I:()=>d});var a=t(95155),r=t(12115),n=t(89852),l=t(97168),i=t(81704),c=t(88145),o=t(53999);function d(e){let{value:s,onChange:t,onSearch:d,onRemoteSearch:m,placeholder:x="搜索提示词...",searchHistory:h=[],onClearHistory:u,className:p,showRemoteSearch:j=!1}=e,[f,v]=(0,r.useState)(!1),[g,N]=(0,r.useState)(-1),b=(0,r.useRef)(null),w=(0,r.useRef)(null),y=(0,r.useRef)(null),k=(0,r.useCallback)(e=>{y.current&&clearTimeout(y.current),y.current=setTimeout(()=>{d(e)},300)},[d]),C=h.filter(e=>(null==e?void 0:e.term)&&"string"==typeof e.term&&e.term.toLowerCase().includes(s.toLowerCase())&&e.term!==s).slice(0,5);return(0,r.useEffect)(()=>{let e=e=>{if(f)switch(e.key){case"ArrowDown":e.preventDefault(),N(e=>e<C.length-1?e+1:e);break;case"ArrowUp":e.preventDefault(),N(e=>e>0?e-1:-1);break;case"Enter":if(e.preventDefault(),g>=0&&C[g]){let e=C[g].term;t(e),d(e),v(!1),N(-1)}else s.trim()&&(d(s.trim()),v(!1),N(-1));break;case"Escape":var a;v(!1),N(-1),null==(a=b.current)||a.blur()}};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[f,g,C]),(0,r.useEffect)(()=>{let e=e=>{var s;!w.current||w.current.contains(e.target)||(null==(s=b.current)?void 0:s.contains(e.target))||(v(!1),N(-1))};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e),y.current&&clearTimeout(y.current)}},[]),(0,a.jsxs)("div",{className:(0,o.cn)("relative w-full",p),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.Icon,{name:"search",className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(n.p,{ref:b,type:"text",placeholder:x,value:s,onChange:e=>{let s=e.target.value;t(s),v(s.length>0||C.length>0),N(-1),k(s)},onFocus:()=>{v(s.length>0||C.length>0)},className:"pl-10 pr-20"}),(0,a.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1",children:[s&&(0,a.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-6 w-6 hover:bg-gray-100",onClick:()=>{var e;t(""),v(!1),N(-1),null==(e=b.current)||e.focus()},children:(0,a.jsx)(i.Icon,{name:"times",className:"h-3 w-3"})}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-6 px-2 text-xs hover:bg-blue-100 hover:text-blue-600",onClick:()=>{s.trim()&&(d(s.trim()),v(!1),N(-1))},disabled:!s.trim(),children:"搜索"})]})]}),f&&(C.length>0||h.length>0)&&(0,a.jsxs)("div",{ref:w,className:"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto",children:[C.length>0&&(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-muted-foreground",children:"搜索历史"}),u&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-5 px-1 text-xs text-muted-foreground hover:text-red-600",onClick:u,children:"清除"})]}),C.map((e,s)=>(0,a.jsxs)("div",{className:(0,o.cn)("flex items-center justify-between px-2 py-1.5 rounded cursor-pointer transition-colors",g===s?"bg-blue-50 text-blue-600":"hover:bg-gray-50"),onClick:()=>{var s;t(s=e.term),d(s),v(!1),N(-1)},children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[(0,a.jsx)(i.Icon,{name:"clock",className:"h-3 w-3 text-muted-foreground flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm truncate",children:e.term})]}),(0,a.jsx)(c.E,{variant:"secondary",className:"text-xs ml-2",children:e.count})]},e.id))]}),0===C.length&&s&&(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsxs)("div",{className:"p-2 text-center text-sm text-muted-foreground",children:['正在搜索 "',s,'"...']}),j&&m&&(0,a.jsxs)("button",{className:"w-full p-2 text-sm text-blue-600 hover:bg-blue-50 rounded transition-colors",onClick:()=>{m(s),v(!1)},children:[(0,a.jsx)(i.Icon,{name:"search",className:"h-3 w-3 mr-2 inline"}),"在线搜索更多结果"]})]})]})]})}},19328:(e,s,t)=>{t.d(s,{G:()=>h});var a=t(95155),r=t(90792),n=t(12115),l=t(58497),i=t(14472),c=t(97168),o=t(81704),d=t(53580),m=t(53999);function x(e){let{children:s,className:t,inline:r}=e,[x,h]=(0,n.useState)(!1),{toast:u}=(0,d.dj)(),p=/language-(\w+)/.exec(t||""),j=p?p[1]:"",f=async()=>{try{await navigator.clipboard.writeText(s),h(!0),u({title:"复制成功",description:"代码已复制到剪贴板"}),setTimeout(()=>h(!1),2e3)}catch(e){u({title:"复制失败",description:"无法复制到剪贴板",variant:"destructive"})}};return r?(0,a.jsx)("code",{className:"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold",children:s}):(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute right-2 top-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsxs)(c.$,{variant:"outline",size:"sm",onClick:f,className:(0,m.cn)("h-8 px-2 bg-background/80 backdrop-blur-sm border-border/50",x&&"bg-green-100 border-green-300 text-green-700"),children:[(0,a.jsx)(o.Icon,{name:x?"check":"copy",className:"h-3 w-3 mr-1"}),x?"已复制":"复制"]})}),(0,a.jsx)(l.A,{style:i.A,language:j,PreTag:"div",className:"rounded-md !mt-0 !mb-0",customStyle:{margin:0,borderRadius:"0.375rem",fontSize:"0.875rem",lineHeight:"1.25rem"},children:s})]})}function h(e){let{content:s,className:t="",truncate:n=!1,maxLines:l=3}=e;return(0,a.jsx)("div",{className:"prose prose-sm max-w-none dark:prose-invert ".concat(t),children:(0,a.jsx)(r.oz,{components:{code(e){let{node:s,inline:t,className:r,children:l,...i}=e;return t?(0,a.jsx)("code",{className:"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold",children:l}):n?(0,a.jsx)("pre",{className:"bg-muted rounded p-2 text-xs overflow-hidden",children:(0,a.jsx)("code",{children:String(l).replace(/\n$/,"")})}):(0,a.jsx)(x,{inline:t,className:r,...i,children:String(l).replace(/\n$/,"")})},h1:e=>{let{children:s}=e;return n?(0,a.jsx)("span",{className:"font-bold text-base",children:s}):(0,a.jsx)("h1",{className:"text-xl font-bold mb-2",children:s})},h2:e=>{let{children:s}=e;return n?(0,a.jsx)("span",{className:"font-semibold text-sm",children:s}):(0,a.jsx)("h2",{className:"text-lg font-semibold mb-2",children:s})},h3:e=>{let{children:s}=e;return n?(0,a.jsx)("span",{className:"font-medium text-sm",children:s}):(0,a.jsx)("h3",{className:"text-base font-medium mb-1",children:s})},p:e=>{let{children:s}=e;return n?(0,a.jsxs)("span",{className:"inline",children:[s," "]}):(0,a.jsx)("p",{className:"mb-2",children:s})},ul:e=>{let{children:s}=e;return n?(0,a.jsx)("span",{className:"inline",children:s}):(0,a.jsx)("ul",{className:"list-disc list-inside mb-2",children:s})},ol:e=>{let{children:s}=e;return n?(0,a.jsx)("span",{className:"inline",children:s}):(0,a.jsx)("ol",{className:"list-decimal list-inside mb-2",children:s})},li:e=>{let{children:s}=e;return n?(0,a.jsxs)("span",{className:"inline",children:["• ",s," "]}):(0,a.jsx)("li",{className:"mb-1",children:s})},strong:e=>{let{children:s}=e;return(0,a.jsx)("strong",{className:"font-semibold",children:s})},em:e=>{let{children:s}=e;return(0,a.jsx)("em",{className:"italic",children:s})},blockquote:e=>{let{children:s}=e;return n?(0,a.jsx)("span",{className:"inline italic",children:s}):(0,a.jsx)("blockquote",{className:"border-l-4 border-muted pl-4 italic mb-2",children:s})}},children:s})})}},20728:(e,s,t)=>{t.d(s,{n:()=>u});var a=t(95155),r=t(12115),n=t(19328),l=t(99840),i=t(97168),c=t(88145),o=t(81704),d=t(53580);async function m(e){try{if(navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(e),!0;return x(e)}catch(s){return console.error("复制到剪贴板失败:",s),x(e)}}function x(e){try{let s=document.createElement("textarea");s.value=e,s.style.position="fixed",s.style.left="-999999px",s.style.top="-999999px",document.body.appendChild(s),s.focus(),s.select();let t=document.execCommand("copy");return document.body.removeChild(s),t}catch(e){return console.error("降级复制方法失败:",e),!1}}var h=t(19987);function u(e){let{prompt:s,isOpen:t,onClose:x,onEdit:u,onDelete:p,onCopy:j}=e,[f,v]=(0,r.useState)(!0),{toast:g}=(0,d.dj)();if(!s)return null;let N=async()=>{await m(s.content)?(j(s.content,s.id),g({title:"复制成功",description:"提示词已复制到剪贴板"})):g({title:"复制失败",description:"无法复制到剪贴板，请手动复制",variant:"destructive"})};return(0,a.jsx)(l.lG,{open:t,onOpenChange:x,children:(0,a.jsxs)(l.Cf,{className:"max-w-4xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col",children:[(0,a.jsxs)(l.c7,{children:[(0,a.jsxs)("div",{className:"flex items-start justify-between pr-8",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)(l.L3,{className:"text-xl font-semibold mb-2",children:s.title}),s.description&&(0,a.jsx)(l.rr,{className:"text-base",children:s.description})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4 flex-wrap",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:N,className:"flex-shrink-0",children:[(0,a.jsx)(o.Icon,{name:"copy",className:"h-4 w-4 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"复制"})]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{u(s.id),x()},className:"flex-shrink-0",children:[(0,a.jsx)(o.Icon,{name:"edit",className:"h-4 w-4 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"编辑"})]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{p(s.id),x()},className:"text-red-600 hover:text-red-700 flex-shrink-0",children:[(0,a.jsx)(o.Icon,{name:"trash",className:"h-4 w-4 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"删除"})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 pt-4 border-t",children:[s.category&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.Icon,{name:"folder",className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)(c.E,{variant:"secondary",style:{backgroundColor:"".concat(s.category.color,"20"),color:s.category.color},children:[(0,a.jsx)(o.Icon,{name:s.category.icon,className:"h-3 w-3 mr-1"}),s.category.name]})]}),s.tags&&s.tags.length>0&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.Icon,{name:"tags",className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:s.tags.map(e=>(0,a.jsx)(c.E,{variant:"outline",className:"text-xs",style:{backgroundColor:"".concat(e.color,"20"),color:e.color,borderColor:"".concat(e.color,"40")},children:e.name},e.id))})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.Icon,{name:"eye",className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["使用 ",s.usage_count," 次"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.Icon,{name:"clock",className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["创建于 ",(0,h.Yq)(s.created_at)]})]}),s.updated_at!==s.created_at&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.Icon,{name:"refresh",className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["更新于 ",(0,h.fw)(s.updated_at)]})]})]})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-hidden flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(i.$,{variant:f?"outline":"default",size:"sm",onClick:()=>v(!1),className:"relative",children:[(0,a.jsx)(o.Icon,{name:"file-text",className:"h-4 w-4 mr-2"}),"原始文本"]}),(0,a.jsxs)(i.$,{variant:f?"default":"outline",size:"sm",onClick:()=>v(!0),className:"relative",children:[(0,a.jsx)(o.Icon,{name:"eye",className:"h-4 w-4 mr-2"}),"Markdown 预览",f&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full animate-pulse"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950 px-2 py-1 rounded-md",children:[(0,a.jsx)(o.Icon,{name:"circle-info",className:"h-3 w-3"}),(0,a.jsx)("span",{children:"支持 Markdown 格式"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[s.content.length," 字符"]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto border rounded-lg p-4 bg-gray-50 dark:bg-gray-900",children:f?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"absolute top-0 right-0 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md",children:[(0,a.jsx)(o.Icon,{name:"eye",className:"h-3 w-3 inline mr-1"}),"Markdown 渲染"]}),(0,a.jsx)(n.G,{content:s.content,className:"pt-6"})]}):(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"absolute top-0 right-0 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md",children:[(0,a.jsx)(o.Icon,{name:"file-text",className:"h-3 w-3 inline mr-1"}),"原始文本"]}),(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-sm font-mono pt-6",children:s.content})]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{console.log("导出提示词")},children:[(0,a.jsx)(o.Icon,{name:"download",className:"h-4 w-4 mr-2"}),"导出"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{console.log("分享提示词")},children:[(0,a.jsx)(o.Icon,{name:"share",className:"h-4 w-4 mr-2"}),"分享"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:x,children:"关闭"}),(0,a.jsxs)(i.$,{onClick:N,children:[(0,a.jsx)(o.Icon,{name:"copy",className:"h-4 w-4 mr-2"}),"复制内容"]})]})]})]})})}},26724:(e,s,t)=>{t.d(s,{G:()=>p});var a=t(95155),r=t(12115),n=t(99840),l=t(97168),i=t(89852),c=t(99474),o=t(82714),d=t(88145),m=t(81704),x=t(53580),h=t(11790),u=t(34938);function p(e){let{prompt:s,isOpen:t,onClose:p,onSuccess:j}=e,[f,v]=(0,r.useState)(""),[g,N]=(0,r.useState)(""),[b,w]=(0,r.useState)(""),[y,k]=(0,r.useState)(""),[C,I]=(0,r.useState)([]),[z,E]=(0,r.useState)(""),[S,$]=(0,r.useState)([]),[R,T]=(0,r.useState)([]),[L,_]=(0,r.useState)(!1),[D,F]=(0,r.useState)(!1),{toast:P}=(0,x.dj)(),q=!!s;(0,r.useEffect)(()=>{t&&G()},[t]),(0,r.useEffect)(()=>{if(s){var e;v(s.title),N(s.description||""),w(s.content),k(s.category_id||""),I((null==(e=s.tags)?void 0:e.map(e=>e.id))||[])}else J()},[s]);let G=async()=>{try{F(!0);let[e,s]=await Promise.all([(0,u.bW)(),(0,u.Q2)()]);$(e),T(s)}catch(e){console.error("加载数据失败:",e),P({title:"加载失败",description:"无法加载分类和标签数据",variant:"destructive"})}finally{F(!1)}},J=()=>{v(""),N(""),w(""),k(""),I([]),E("")},M=async e=>{if(e.preventDefault(),!f.trim()||!b.trim())return void P({title:"表单验证失败",description:"标题和内容不能为空",variant:"destructive"});try{_(!0);let e={title:f.trim(),description:g.trim()||void 0,content:b.trim(),category_id:y||void 0};if(q&&s)await (0,u.qj)(s.id,e,C),P({title:"更新成功",description:"提示词已成功更新"});else{console.log("\uD83D\uDE80 使用本地优先存储创建提示词");let s=await h.J.createPrompt(e);j&&j(s),P({title:"创建成功",description:"提示词已成功创建"})}j(),p(),J()}catch(e){console.error("保存提示词失败:",e),P({title:"保存失败",description:q?"更新提示词时出现错误":"创建提示词时出现错误",variant:"destructive"})}finally{_(!1)}},Z=async()=>{if(z.trim())try{let e=await (0,u.VZ)({name:z.trim(),color:"#6366f1"});T(s=>[...s,e]),I(s=>[...s,e.id]),E(""),P({title:"标签创建成功",description:'标签 "'.concat(e.name,'" 已创建并添加')})}catch(e){console.error("创建标签失败:",e),P({title:"创建标签失败",description:"无法创建新标签",variant:"destructive"})}},A=e=>{I(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},B=R.filter(e=>C.includes(e.id));return(0,a.jsx)(n.lG,{open:t,onOpenChange:p,children:(0,a.jsxs)(n.Cf,{className:"max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col",children:[(0,a.jsxs)(n.c7,{children:[(0,a.jsx)(n.L3,{children:q?"编辑提示词":"创建新提示词"}),(0,a.jsx)(n.rr,{children:q?"修改提示词的信息和内容":"填写提示词的基本信息和内容"})]}),D?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)(m.Icon,{name:"spinner",className:"h-6 w-6 animate-spin mr-2"}),(0,a.jsx)("span",{children:"加载中..."})]}):(0,a.jsxs)("form",{onSubmit:M,className:"flex-1 overflow-hidden flex flex-col",children:[(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"title",children:"标题 *"}),(0,a.jsx)(i.p,{id:"title",value:f,onChange:e=>v(e.target.value),placeholder:"输入提示词标题",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"description",children:"描述"}),(0,a.jsx)(i.p,{id:"description",value:g,onChange:e=>N(e.target.value),placeholder:"输入提示词的简短描述（可选）"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"category",children:"分类"}),(0,a.jsxs)("select",{id:"category",value:y,onChange:e=>k(e.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,a.jsx)("option",{value:"",children:"选择分类（可选）"}),S.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{children:"标签"}),B.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:B.map(e=>(0,a.jsxs)(d.E,{variant:"secondary",className:"cursor-pointer",style:{backgroundColor:"".concat(e.color,"20"),color:e.color},onClick:()=>A(e.id),children:[e.name,(0,a.jsx)(m.Icon,{name:"times",className:"h-3 w-3 ml-1"})]},e.id))}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:R.filter(e=>!C.includes(e.id)).map(e=>(0,a.jsxs)(d.E,{variant:"outline",className:"cursor-pointer hover:bg-gray-100",onClick:()=>A(e.id),children:[(0,a.jsx)(m.Icon,{name:"plus",className:"h-3 w-3 mr-1"}),e.name]},e.id))}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.p,{value:z,onChange:e=>E(e.target.value),placeholder:"创建新标签",className:"flex-1",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),Z())}}),(0,a.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:Z,disabled:!z.trim(),children:(0,a.jsx)(m.Icon,{name:"plus",className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"content",children:"内容 *"}),(0,a.jsx)(c.T,{id:"content",value:b,onChange:e=>w(e.target.value),placeholder:"输入提示词内容",className:"min-h-[200px] font-mono",required:!0}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[b.length," 字符"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-4 border-t",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:p,children:"取消"}),(0,a.jsx)(l.$,{type:"submit",disabled:L,children:L?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),q?"更新中...":"创建中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Icon,{name:"save",className:"h-4 w-4 mr-2"}),q?"更新":"创建"]})})]})]})]})})}},33208:(e,s,t)=>{t.d(s,{z:()=>x});var a=t(95155),r=t(12115),n=t(88482),l=t(97168),i=t(88145),c=t(81704),o=t(53580),d=t(19328),m=t(53999);function x(e){let{id:s,title:t,description:x,content:h,category:u,tags:p=[],usageCount:j,createdAt:f,updatedAt:v,onView:g,onEdit:N,onDelete:b,onCopy:w,isLocal:y=!1,className:k}=e,[C,I]=(0,r.useState)(!1),{toast:z}=(0,o.dj)(),E=async()=>{try{await navigator.clipboard.writeText(h),w(h,s),z({title:"复制成功",description:"提示词已复制到剪贴板"})}catch(e){z({title:"复制失败",description:"无法复制到剪贴板，请手动复制",variant:"destructive"})}};return(0,a.jsxs)(n.Zp,{className:(0,m.cn)("group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02] cursor-pointer","border-l-4",(null==u?void 0:u.color)?"border-l-[".concat(u.color,"]"):"border-l-blue-500",k),onMouseEnter:()=>I(!0),onMouseLeave:()=>I(!1),onClick:()=>g(s),children:[(0,a.jsxs)(n.aR,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)(n.ZB,{className:"text-lg font-semibold mb-1 overflow-hidden",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"line-clamp-2 flex-1",children:t}),y&&(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shrink-0",children:[(0,a.jsx)(c.Icon,{name:"smartphone",className:"h-3 w-3 mr-1"}),"本地"]})]})}),x&&(0,a.jsx)(n.BT,{className:"text-sm text-muted-foreground overflow-hidden",children:(0,a.jsx)("div",{className:"line-clamp-2",children:x})})]}),(0,a.jsxs)("div",{className:(0,m.cn)("flex items-center gap-1 transition-opacity duration-200",C?"opacity-100":"opacity-0 md:opacity-0","sm:opacity-100"),children:[(0,a.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-blue-100 hover:text-blue-600",onClick:e=>{e.stopPropagation(),E()},children:(0,a.jsx)(c.Icon,{name:"copy",className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-green-100 hover:text-green-600",onClick:e=>{e.stopPropagation(),N(s)},children:(0,a.jsx)(c.Icon,{name:"edit",className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 hover:text-red-600",onClick:e=>{e.stopPropagation(),b(s)},children:(0,a.jsx)(c.Icon,{name:"trash",className:"h-4 w-4"})})]})]}),u&&(0,a.jsx)("div",{className:"flex items-center gap-2 mt-1.5",children:(0,a.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 rounded text-xs font-medium",style:{backgroundColor:"".concat(u.color,"12"),color:u.color,border:"1px solid ".concat(u.color,"25")},children:[(0,a.jsx)(c.Icon,{name:u.icon,className:"h-3 w-3"}),u.name]})})]}),(0,a.jsxs)(n.Wu,{className:"pt-0",children:[(0,a.jsxs)("div",{className:"relative text-sm text-muted-foreground mb-4 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 right-0 z-10 bg-blue-50 dark:bg-blue-950 text-blue-600 dark:text-blue-400 text-xs px-1.5 py-0.5 rounded-sm opacity-70",children:(0,a.jsx)(c.Icon,{name:"eye",className:"h-2.5 w-2.5 inline"})}),(0,a.jsx)("div",{className:"line-clamp-3 pr-8",children:(0,a.jsx)(d.G,{content:h,truncate:!0,maxLines:3,className:"text-sm text-muted-foreground prose-p:inline prose-headings:inline prose-strong:font-medium prose-em:italic"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(c.Icon,{name:"eye",className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[j," 次使用"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(c.Icon,{name:"clock",className:"h-3 w-3"}),(0,a.jsx)("span",{children:new Date(v).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric"})})]})]}),(0,a.jsxs)(l.$,{variant:"ghost",size:"sm",className:"h-6 px-2 text-xs hover:bg-blue-100 hover:text-blue-600",onClick:e=>{e.stopPropagation(),E()},children:[(0,a.jsx)(c.Icon,{name:"copy",className:"h-3 w-3 mr-1"}),"复制"]})]}),p.length>0&&(0,a.jsx)("div",{className:"mt-2 pt-2 border-t border-gray-100",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[p.slice(0,5).map(e=>(0,a.jsx)(i.E,{variant:"outline",className:"text-xs px-1.5 py-0.5 border-dashed h-5",style:{backgroundColor:"".concat(e.color,"06"),color:e.color,borderColor:"".concat(e.color,"30")},children:e.name},e.id)),p.length>5&&(0,a.jsxs)(i.E,{variant:"outline",className:"text-xs px-1.5 py-0.5 border-dashed text-muted-foreground h-5",children:["+",p.length-5]})]})})]}),(0,a.jsx)("div",{className:(0,m.cn)("absolute inset-0 border-2 border-transparent transition-colors duration-300 rounded-lg pointer-events-none",C&&"border-blue-200")})]})}},88482:(e,s,t)=>{t.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>l,aR:()=>i});var a=t(95155),r=t(12115),n=t(53999);let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...r})});l.displayName="Card";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...r})});i.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...r})});d.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"}}]);