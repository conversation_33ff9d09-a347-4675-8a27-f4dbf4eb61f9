(()=>{var a={};a.id=909,a.ids=[909],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6546:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Cursor Project\\\\prompy augment\\\\prompt\\\\app\\\\dashboard\\\\stats\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\stats\\page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},54788:(a,b,c)=>{Promise.resolve().then(c.bind(c,89982))},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79086:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["stats",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,6546)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\stats\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,38836)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]}.children,H=["D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\stats\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/stats/page",pathname:"/dashboard/stats",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/stats/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89700:(a,b,c)=>{Promise.resolve().then(c.bind(c,6546))},89982:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m});var d=c(60687),e=c(32945),f=c(43210),g=c(55192),h=c(59821),i=c(32418),j=c(44655);function k(){let[a,b]=(0,f.useState)(null),[c,e]=(0,f.useState)(!0);return c?(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)(g.Zp,{className:"animate-pulse",children:[(0,d.jsx)(g.aR,{className:"pb-2",children:(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})})]},b))})}):a?a?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"总提示词"}),(0,d.jsx)(i.Icon,{name:"file-text",className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:(0,j.ZV)(a.totalPrompts)}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"已创建的提示词数量"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"总分类"}),(0,d.jsx)(i.Icon,{name:"folder",className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:(0,j.ZV)(a.totalCategories)}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"已创建的分类数量"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"总标签"}),(0,d.jsx)(i.Icon,{name:"tags",className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:(0,j.ZV)(a.totalTags)}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"已创建的标签数量"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"总使用次数"}),(0,d.jsx)(i.Icon,{name:"eye",className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:(0,j.ZV)(a.totalUsage)}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"累计复制使用次数"})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(i.Icon,{name:"fire",className:"h-5 w-5 text-orange-500"}),"热门提示词"]}),(0,d.jsx)(g.BT,{children:"使用次数最多的提示词"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"space-y-3",children:a.popularPrompts.length>0?a.popularPrompts.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 text-orange-600 text-xs font-medium",children:b+1}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("p",{className:"font-medium truncate",children:a.title}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:["使用 ",a.usage_count," 次"]})]}),a.category&&(0,d.jsx)(h.E,{variant:"secondary",style:{backgroundColor:`${a.category.color}20`,color:a.category.color},children:a.category.name})]},a.id)):(0,d.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"暂无使用数据"})})})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(i.Icon,{name:"hashtag",className:"h-5 w-5 text-blue-500"}),"热门标签"]}),(0,d.jsx)(g.BT,{children:"使用最频繁的标签"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:a.popularTags.length>0?a.popularTags.map(a=>(0,d.jsxs)(h.E,{variant:"secondary",className:"text-sm",style:{backgroundColor:`${a.color}20`,color:a.color},children:[a.name,(0,d.jsx)("span",{className:"ml-1 text-xs opacity-75",children:a.usage_count})]},a.id)):(0,d.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4 w-full",children:"暂无标签数据"})})})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(i.Icon,{name:"search",className:"h-5 w-5 text-green-500"}),"热门搜索"]}),(0,d.jsx)(g.BT,{children:"搜索次数最多的关键词"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"space-y-2",children:a.popularSearches.length>0?a.popularSearches.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("span",{className:"text-sm font-medium text-green-600",children:["#",b+1]}),(0,d.jsx)("span",{className:"text-sm",children:a.search_term})]}),(0,d.jsxs)(h.E,{variant:"outline",children:[a.search_count," 次"]})]},a.id)):(0,d.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"暂无搜索数据"})})})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(i.Icon,{name:"clock",className:"h-5 w-5 text-purple-500"}),"最近创建"]}),(0,d.jsx)(g.BT,{children:"最新创建的提示词"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"space-y-3",children:a.recentPrompts.length>0?a.recentPrompts.map(a=>(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("p",{className:"font-medium truncate",children:a.title}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,j.fw)(a.created_at)})]}),a.category&&(0,d.jsx)(h.E,{variant:"secondary",style:{backgroundColor:`${a.category.color}20`,color:a.category.color},children:a.category.name})]},a.id)):(0,d.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"暂无提示词"})})})]})]})]}):null:(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(i.Icon,{name:"exclamation-triangle",className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"无法加载统计数据"})]})}c(34257);var l=c(24934);function m(){return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,d.jsx)(e.a,{}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto p-6",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white",children:"数据统计"}),(0,d.jsx)("p",{className:"text-muted-foreground mt-2",children:"查看您的提示词使用情况和统计数据"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{window.location.reload()},className:"flex-1 sm:flex-none",children:[(0,d.jsx)(i.Icon,{name:"refresh",className:"h-4 w-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"刷新数据"})]}),(0,d.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{console.log("导出数据")},className:"flex-1 sm:flex-none",children:[(0,d.jsx)(i.Icon,{name:"download",className:"h-4 w-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"导出数据"})]})]})]}),(0,d.jsx)(k,{}),(0,d.jsx)("div",{className:"mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6",children:(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)(i.Icon,{name:"info-circle",className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-blue-900 dark:text-blue-100 mb-2",children:"统计说明"}),(0,d.jsxs)("div",{className:"text-sm text-blue-700 dark:text-blue-200 space-y-1",children:[(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"总使用次数"}),"：所有提示词的复制使用次数总和"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"热门提示词"}),"：按使用次数排序的前5个提示词"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"热门标签"}),"：按关联提示词数量排序的标签"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"热门搜索"}),"：搜索次数最多的关键词"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"最近创建"}),"：按创建时间排序的最新提示词"]})]})]})]})}),(0,d.jsxs)("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:(0,d.jsx)(i.Icon,{name:"lightbulb",className:"h-5 w-5 text-green-600"})}),(0,d.jsx)("h3",{className:"font-medium",children:"提升效率"})]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"通过查看热门提示词，了解哪些内容最受欢迎，优化您的提示词库。"})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,d.jsx)(i.Icon,{name:"bullseye",className:"h-5 w-5 text-blue-600"})}),(0,d.jsx)("h3",{className:"font-medium",children:"精准分类"})]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"根据使用统计调整分类结构，让常用的提示词更容易找到。"})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,d.jsx)(i.Icon,{name:"chart",className:"h-5 w-5 text-purple-600"})}),(0,d.jsx)("h3",{className:"font-medium",children:"数据驱动"})]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"基于搜索和使用数据，持续优化您的提示词管理策略。"})]})]})]})]})}},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,519,522,814,884,121,780,568],()=>b(b.s=79086));module.exports=c})();