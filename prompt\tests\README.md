# 🧪 测试文档

本目录包含提示词管理工具的所有测试文件和测试文档。

## 📁 测试目录结构

```
tests/
├── README.md                    # 本文件 - 测试文档
├── unit/                        # 单元测试
│   ├── components/              # 组件测试
│   ├── hooks/                   # Hook 测试
│   ├── utils/                   # 工具函数测试
│   └── database/                # 数据库操作测试
├── integration/                 # 集成测试
│   ├── api/                     # API 集成测试
│   ├── database/                # 数据库集成测试
│   └── auth/                    # 认证集成测试
├── e2e/                         # 端到端测试
│   ├── playwright/              # Playwright 测试
│   ├── scenarios/               # 测试场景
│   └── fixtures/                # 测试数据
├── performance/                 # 性能测试
│   ├── load/                    # 负载测试
│   ├── stress/                  # 压力测试
│   └── benchmark/               # 基准测试
└── scripts/                     # 测试脚本
    ├── setup.js                 # 测试环境设置
    ├── teardown.js              # 测试清理
    └── helpers.js               # 测试辅助函数
```

## 🎯 测试策略

### 1. 测试金字塔

```
        /\
       /  \
      / E2E \     <- 少量端到端测试
     /______\
    /        \
   /Integration\ <- 适量集成测试
  /__________\
 /            \
/   Unit Tests  \  <- 大量单元测试
/________________\
```

### 2. 测试覆盖率目标

- **单元测试**: 90%+ 代码覆盖率
- **集成测试**: 80%+ API 覆盖率
- **E2E 测试**: 100% 关键用户流程
- **性能测试**: 核心功能性能基准

## 🔧 测试工具栈

### 前端测试
- **Jest**: JavaScript 测试框架
- **React Testing Library**: React 组件测试
- **MSW**: API Mock 服务
- **Playwright**: 端到端测试

### 后端测试
- **Supabase Test Client**: 数据库测试
- **PostgreSQL Test Database**: 测试数据库
- **Faker.js**: 测试数据生成

### 性能测试
- **Lighthouse CI**: 性能监控
- **Artillery**: 负载测试
- **k6**: 压力测试

## 🚀 运行测试

### 1. 环境准备

```bash
# 安装测试依赖
npm install --dev

# 设置测试环境变量
cp .env.test.example .env.test
```

### 2. 单元测试

```bash
# 运行所有单元测试
npm run test:unit

# 运行特定测试文件
npm run test:unit -- components/search-bar.test.tsx

# 监听模式运行
npm run test:unit:watch

# 生成覆盖率报告
npm run test:unit:coverage
```

### 3. 集成测试

```bash
# 运行集成测试
npm run test:integration

# 运行 API 测试
npm run test:api

# 运行数据库测试
npm run test:database
```

### 4. 端到端测试

```bash
# 安装 Playwright
npx playwright install

# 运行 E2E 测试
npm run test:e2e

# 运行特定浏览器测试
npm run test:e2e -- --project=chromium

# 调试模式运行
npm run test:e2e:debug
```

### 5. 性能测试

```bash
# 运行性能测试
npm run test:performance

# 运行负载测试
npm run test:load

# 生成性能报告
npm run test:lighthouse
```

## 📝 测试编写指南

### 1. 单元测试示例

```typescript
// components/search-bar.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { SearchBar } from '@/components/search-bar'

describe('SearchBar', () => {
  it('should render search input', () => {
    render(<SearchBar value="" onChange={() => {}} onSearch={() => {}} />)
    expect(screen.getByPlaceholderText('搜索提示词...')).toBeInTheDocument()
  })

  it('should call onChange when typing', () => {
    const handleChange = jest.fn()
    render(<SearchBar value="" onChange={handleChange} onSearch={() => {}} />)
    
    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: 'test query' }
    })
    
    expect(handleChange).toHaveBeenCalledWith('test query')
  })
})
```

### 2. 集成测试示例

```typescript
// integration/api/prompts.test.ts
import { createClient } from '@supabase/supabase-js'
import { getPrompts, createPrompt } from '@/lib/database/prompts'

describe('Prompts API', () => {
  let supabase: any
  let testUserId: string

  beforeAll(async () => {
    supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_ANON_KEY!)
    // 创建测试用户
    testUserId = await createTestUser()
  })

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData(testUserId)
  })

  it('should create and retrieve prompts', async () => {
    const promptData = {
      title: 'Test Prompt',
      content: 'Test content',
      user_id: testUserId
    }

    const created = await createPrompt(promptData)
    expect(created).toBeDefined()

    const prompts = await getPrompts({ userId: testUserId })
    expect(prompts.data).toContainEqual(
      expect.objectContaining({ title: 'Test Prompt' })
    )
  })
})
```

### 3. E2E 测试示例

```typescript
// e2e/user-workflow.spec.ts
import { test, expect } from '@playwright/test'

test.describe('User Workflow', () => {
  test('should complete full user journey', async ({ page }) => {
    // 登录
    await page.goto('/auth/login')
    await page.fill('[data-testid=email]', '<EMAIL>')
    await page.fill('[data-testid=password]', 'password123')
    await page.click('[data-testid=login-button]')

    // 创建提示词
    await page.click('[data-testid=new-prompt-button]')
    await page.fill('[data-testid=prompt-title]', 'Test Prompt')
    await page.fill('[data-testid=prompt-content]', 'Test content')
    await page.click('[data-testid=save-button]')

    // 验证创建成功
    await expect(page.locator('[data-testid=prompt-card]')).toContainText('Test Prompt')

    // 搜索提示词
    await page.fill('[data-testid=search-input]', 'Test')
    await expect(page.locator('[data-testid=prompt-card]')).toHaveCount(1)
  })
})
```

## 🔍 测试最佳实践

### 1. 测试命名规范

```typescript
// ✅ 好的测试名称
describe('SearchBar component', () => {
  it('should display search results when user types query', () => {})
  it('should clear search when clear button is clicked', () => {})
  it('should show search history when input is focused', () => {})
})

// ❌ 不好的测试名称
describe('SearchBar', () => {
  it('test search', () => {})
  it('test clear', () => {})
})
```

### 2. 测试数据管理

```typescript
// 使用工厂函数创建测试数据
const createTestPrompt = (overrides = {}) => ({
  id: 'test-id',
  title: 'Test Prompt',
  content: 'Test content',
  user_id: 'test-user-id',
  created_at: new Date().toISOString(),
  ...overrides
})

// 使用 Faker.js 生成随机数据
import { faker } from '@faker-js/faker'

const createRandomPrompt = () => ({
  id: faker.string.uuid(),
  title: faker.lorem.sentence(),
  content: faker.lorem.paragraphs(),
  user_id: faker.string.uuid()
})
```

### 3. Mock 和 Stub

```typescript
// Mock Supabase 客户端
jest.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    from: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockResolvedValue({
          data: [createTestPrompt()],
          error: null
        })
      })
    })
  })
}))
```

### 4. 异步测试

```typescript
// 正确处理异步操作
it('should load prompts on mount', async () => {
  const mockPrompts = [createTestPrompt()]
  jest.spyOn(api, 'getPrompts').mockResolvedValue({ data: mockPrompts })

  render(<PromptList />)

  // 等待异步操作完成
  await waitFor(() => {
    expect(screen.getByText('Test Prompt')).toBeInTheDocument()
  })
})
```

## 📊 测试报告

### 1. 覆盖率报告

```bash
# 生成覆盖率报告
npm run test:coverage

# 查看 HTML 报告
open coverage/lcov-report/index.html
```

### 2. 测试结果

测试结果会自动生成在以下位置：
- `test-results/` - 测试结果文件
- `coverage/` - 覆盖率报告
- `playwright-report/` - Playwright 测试报告

### 3. CI/CD 集成

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:integration
      - run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## 🐛 调试测试

### 1. 调试单元测试

```bash
# 使用 Node.js 调试器
node --inspect-brk node_modules/.bin/jest --runInBand

# 使用 VS Code 调试配置
{
  "type": "node",
  "request": "launch",
  "name": "Debug Jest Tests",
  "program": "${workspaceFolder}/node_modules/.bin/jest",
  "args": ["--runInBand"],
  "console": "integratedTerminal"
}
```

### 2. 调试 E2E 测试

```bash
# 头部模式运行
npx playwright test --headed

# 调试模式
npx playwright test --debug

# 录制测试
npx playwright codegen localhost:3000
```

## 📚 相关资源

- [Jest 文档](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright 文档](https://playwright.dev/docs/intro)
- [Supabase 测试指南](https://supabase.com/docs/guides/getting-started/local-development)

通过遵循这些测试指南和最佳实践，我们可以确保应用的质量和稳定性。
