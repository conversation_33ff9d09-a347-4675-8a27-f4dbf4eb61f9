#!/usr/bin/env node

/**
 * 自动修复常见的 TypeScript 类型错误
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 自动修复 TypeScript 类型错误...\n');

// 修复图标名称类型错误
function fixIconNames() {
  console.log('修复图标名称...');
  
  // 修复 stats 页面的 target 图标
  const statsFile = 'app/dashboard/stats/page.tsx';
  if (fs.existsSync(statsFile)) {
    let content = fs.readFileSync(statsFile, 'utf8');
    content = content.replace(/name="target"/g, 'name="bullseye"');
    fs.writeFileSync(statsFile, content);
    console.log('✅ 修复 stats 页面图标');
  }
  
  // 修复分类表单的 target 图标
  const categoryFile = 'components/category-form-modal.tsx';
  if (fs.existsSync(categoryFile)) {
    let content = fs.readFileSync(categoryFile, 'utf8');
    content = content.replace(/{ name: 'target', label: '目标' }/g, "{ name: 'bullseye', label: '目标' }");
    fs.writeFileSync(categoryFile, content);
    console.log('✅ 修复分类表单图标');
  }
  
  // 修复提示词详情的 info 图标
  const promptDetailFile = 'components/prompt-detail-modal.tsx';
  if (fs.existsSync(promptDetailFile)) {
    let content = fs.readFileSync(promptDetailFile, 'utf8');
    content = content.replace(/name="info"/g, 'name="circle-info"');
    fs.writeFileSync(promptDetailFile, content);
    console.log('✅ 修复提示词详情图标');
  }
}

// 修复搜索历史类型问题
function fixSearchHistoryTypes() {
  console.log('修复搜索历史类型...');
  
  // 检查 SearchBar 组件的类型定义
  const searchBarFile = 'components/search-bar.tsx';
  if (fs.existsSync(searchBarFile)) {
    let content = fs.readFileSync(searchBarFile, 'utf8');
    
    // 确保使用正确的类型导入
    if (!content.includes('import type { SearchHistory }')) {
      content = content.replace(
        /import.*SearchHistory.*from.*types\/database/,
        "import type { SearchHistory } from '@/types/database'"
      );
      fs.writeFileSync(searchBarFile, content);
      console.log('✅ 修复 SearchBar 类型导入');
    }
  }
}

// 修复其他常见类型错误
function fixOtherTypes() {
  console.log('修复其他类型错误...');
  
  // 修复可能的 undefined 类型错误
  const files = [
    'lib/database/prompts.ts',
    'lib/database/search.ts',
    'lib/database/tags.ts',
    'lib/database/preferences.ts'
  ];
  
  files.forEach(file => {
    if (fs.existsSync(file)) {
      let content = fs.readFileSync(file, 'utf8');
      
      // 添加可选链操作符和空值检查
      content = content.replace(
        /\.map\(item => item\.tag\)\.filter\(Boolean\)/g,
        '?.map(item => item.tag).filter(Boolean)'
      );
      
      // 确保返回值有默认值
      content = content.replace(
        /return data\.map/g,
        'return data?.map'
      );
      
      fs.writeFileSync(file, content);
      console.log(`✅ 修复 ${file} 类型错误`);
    }
  });
}

// 运行所有修复
function runAllFixes() {
  try {
    fixIconNames();
    fixSearchHistoryTypes();
    fixOtherTypes();
    
    console.log('\n🎉 类型错误修复完成！');
    console.log('\n运行以下命令验证修复：');
    console.log('npm run type-check');
  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error.message);
  }
}

runAllFixes();
