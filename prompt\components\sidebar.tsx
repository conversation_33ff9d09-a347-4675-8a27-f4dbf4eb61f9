"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Icon, IconName } from '@/components/ui/icon'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import type { CategoryWithCount } from '@/types/database'

interface SidebarCategory {
  id: string
  name: string
  description?: string
  color: string
  icon: IconName
  count: number
  sortOrder: number
}

interface SidebarProps {
  categories: CategoryWithCount[]
  selectedCategoryId?: string
  onCategorySelect: (categoryId: string | undefined) => void
  onCategoryCreate: () => void
  onCategoryEdit: (categoryId: string) => void
  onCategoryDelete: (categoryId: string) => void
  isCollapsed?: boolean
  onToggleCollapse?: () => void
  className?: string
}

export function Sidebar({
  categories,
  selectedCategoryId,
  onCategorySelect,
  onCategoryCreate,
  onCategoryEdit,
  onCategoryDelete,
  isCollapsed = false,
  onToggleCollapse,
  className
}: SidebarProps) {
  const [hoveredCategoryId, setHoveredCategoryId] = useState<string | null>(null)

  // 按排序顺序排列分类
  const sortedCategories = [...categories].sort((a, b) => a.sort_order - b.sort_order)

  // 计算总提示词数量 - 使用安全的数字处理避免 NaN
  const totalCount = categories.reduce((sum, cat) => sum + (cat.prompt_count || 0), 0)

  return (
    <div className={cn(
      "flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300",
      isCollapsed ? "w-16" : "w-64",
      "md:relative absolute md:translate-x-0 z-30",
      isCollapsed ? "md:w-16 w-0 -translate-x-full" : "md:w-64 w-64 translate-x-0",
      className
    )}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!isCollapsed && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900">分类</h2>
            <p className="text-sm text-muted-foreground">
              {totalCount} 个提示词
            </p>
          </div>
        )}
        {onToggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={onToggleCollapse}
          >
            <Icon 
              name={isCollapsed ? "chevron-right" : "chevron-left"} 
              className="h-4 w-4" 
            />
          </Button>
        )}
      </div>

      {/* 全部分类 */}
      <div className="p-2">
        <Button
          variant={selectedCategoryId === undefined ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start gap-3 h-10",
            isCollapsed && "justify-center px-2"
          )}
          onClick={() => onCategorySelect(undefined)}
        >
          <Icon name="home" className="h-4 w-4 flex-shrink-0" />
          {!isCollapsed && (
            <>
              <span className="flex-1 text-left">全部</span>
              <Badge variant="secondary" className="ml-auto">
                {totalCount}
              </Badge>
            </>
          )}
        </Button>
      </div>

      {/* 分类列表 */}
      <div className="flex-1 overflow-y-auto p-2 space-y-1">
        {sortedCategories.map((category) => (
          <div
            key={category.id}
            className="relative group"
            onMouseEnter={() => setHoveredCategoryId(category.id)}
            onMouseLeave={() => setHoveredCategoryId(null)}
          >
            <Button
              variant={selectedCategoryId === category.id ? "secondary" : "ghost"}
              className={cn(
                "w-full justify-start gap-3 h-10 relative",
                isCollapsed && "justify-center px-2"
              )}
              onClick={() => onCategorySelect(category.id)}
            >
              <Icon
                name={category.icon || 'folder'}
                className="h-4 w-4 flex-shrink-0"
                color={category.color}
              />
              {!isCollapsed && (
                <>
                  <span className="flex-1 text-left truncate">
                    {category.name}
                  </span>
                  <Badge
                    variant="secondary"
                    className={cn(
                      "ml-auto transition-opacity duration-200",
                      hoveredCategoryId === category.id ? "opacity-0" : "opacity-100"
                    )}
                    style={{
                      backgroundColor: `${category.color}20`,
                      color: category.color
                    }}
                  >
                    {category.prompt_count || 0}
                  </Badge>
                </>
              )}
            </Button>

            {/* 悬停时显示的操作按钮 */}
            {!isCollapsed && hoveredCategoryId === category.id && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1 bg-white rounded shadow-sm border">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 hover:bg-blue-100 hover:text-blue-600"
                  onClick={(e) => {
                    e.stopPropagation()
                    onCategoryEdit(category.id)
                  }}
                >
                  <Icon name="edit" className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 hover:bg-red-100 hover:text-red-600"
                  onClick={(e) => {
                    e.stopPropagation()
                    onCategoryDelete(category.id)
                  }}
                >
                  <Icon name="trash" className="h-3 w-3" />
                </Button>
              </div>
            )}

            {/* 折叠状态下的工具提示 */}
            {isCollapsed && (
              <div className="absolute left-full top-0 ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                {category.name} ({category.prompt_count || 0})
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 底部操作 */}
      <div className="p-2 border-t border-gray-200">
        <Button
          variant="outline"
          className={cn(
            "w-full gap-2",
            isCollapsed && "justify-center px-2"
          )}
          onClick={onCategoryCreate}
        >
          <Icon name="plus" className="h-4 w-4" />
          {!isCollapsed && "新建分类"}
        </Button>
      </div>
    </div>
  )
}
