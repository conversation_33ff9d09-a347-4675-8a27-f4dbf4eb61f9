# 🏗️ 系统架构设计

本文档详细介绍提示词管理工具的技术架构设计。

## 📊 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[Next.js 14 App Router]
        B[React 19 Components]
        C[TypeScript]
        D[Tailwind CSS]
        E[ShadcnUI]
    end
    
    subgraph "状态管理"
        F[React Hooks]
        G[Local Storage]
        H[Session Storage]
    end
    
    subgraph "API 层"
        I[Supabase Client]
        J[Database Operations]
        K[Authentication]
        L[Real-time Subscriptions]
    end
    
    subgraph "后端服务"
        M[Supabase]
        N[PostgreSQL]
        O[Row Level Security]
        P[Edge Functions]
    end
    
    A --> F
    B --> F
    F --> I
    I --> M
    M --> N
```

## 🎯 技术栈详解

### 前端技术栈

#### **Next.js 14 (App Router)**
- **版本**: Latest
- **特性**: 
  - App Router 架构
  - Server Components
  - Client Components
  - 自动代码分割
  - 图像优化

#### **React 19**
- **版本**: ^19.0.0
- **特性**:
  - 函数式组件
  - Hooks 状态管理
  - Suspense 异步处理
  - 并发特性

#### **TypeScript**
- **版本**: ^5
- **配置**: 严格模式
- **特性**:
  - 类型安全
  - 智能提示
  - 编译时检查

#### **样式系统**
- **Tailwind CSS**: 原子化 CSS 框架
- **ShadcnUI**: 高质量组件库
- **CSS Variables**: 主题系统
- **响应式设计**: 移动优先

### 后端技术栈

#### **Supabase**
- **数据库**: PostgreSQL
- **认证**: Supabase Auth
- **实时**: Realtime subscriptions
- **存储**: Supabase Storage
- **边缘函数**: Edge Functions

#### **数据库特性**
- **行级安全**: RLS 策略
- **自动时间戳**: created_at, updated_at
- **软删除**: deleted_at 字段
- **全文搜索**: PostgreSQL FTS

## 🏛️ 架构模式

### 1. 分层架构

```
┌─────────────────────────────────────┐
│           表现层 (Presentation)        │
│  Next.js Pages + React Components   │
├─────────────────────────────────────┤
│            业务层 (Business)          │
│     Custom Hooks + Utils           │
├─────────────────────────────────────┤
│            数据层 (Data)             │
│    Supabase Client + Database      │
├─────────────────────────────────────┤
│           基础设施层 (Infrastructure)   │
│      Supabase + Vercel            │
└─────────────────────────────────────┘
```

### 2. 组件架构

#### **页面组件** (Pages)
- 路由级别组件
- 数据获取和状态管理
- 布局组合

#### **业务组件** (Features)
- 功能特定组件
- 业务逻辑封装
- 状态管理

#### **UI 组件** (UI)
- 可复用基础组件
- 无业务逻辑
- 样式和交互

#### **工具组件** (Utils)
- 工具函数
- 类型定义
- 常量配置

### 3. 数据流架构

```mermaid
sequenceDiagram
    participant U as User
    participant C as Component
    participant H as Hook
    participant S as Supabase
    participant D as Database
    
    U->>C: 用户操作
    C->>H: 调用 Hook
    H->>S: API 请求
    S->>D: 数据库操作
    D->>S: 返回结果
    S->>H: 响应数据
    H->>C: 更新状态
    C->>U: 界面更新
```

## 🔧 核心模块设计

### 1. 搜索系统架构

#### **混合搜索策略**
```typescript
interface SearchStrategy {
  local: LocalSearchEngine    // 本地搜索引擎
  remote: RemoteSearchEngine  // 远程搜索引擎
  cache: SearchCache         // 搜索缓存
  history: SearchHistory     // 搜索历史
}
```

#### **搜索流程**
1. **本地搜索** (默认)
   - 在缓存数据中搜索
   - 实时响应 (<10ms)
   - 防抖优化 (300ms)

2. **远程搜索** (按需)
   - 数据库全文搜索
   - 更全面的结果
   - 用户主动触发

### 2. 状态管理架构

#### **状态分层**
```typescript
interface AppState {
  // 全局状态
  global: {
    user: User
    theme: Theme
    preferences: UserPreferences
  }
  
  // 页面状态
  page: {
    prompts: Prompt[]
    categories: Category[]
    searchQuery: string
    filters: FilterState
  }
  
  // 本地状态
  local: {
    copyHistory: CopyRecord[]
    searchHistory: SearchRecord[]
    uiState: UIState
  }
}
```

#### **状态持久化**
- **Session Storage**: 临时状态
- **Local Storage**: 用户偏好
- **Supabase**: 业务数据

### 3. 缓存策略

#### **多级缓存**
```typescript
interface CacheStrategy {
  memory: MemoryCache      // 内存缓存
  storage: StorageCache    // 本地存储缓存
  database: DatabaseCache  // 数据库缓存
}
```

#### **缓存策略**
- **热数据**: 内存缓存 (5分钟)
- **常用数据**: 本地存储 (1小时)
- **全量数据**: 数据库缓存 (按需)

## 🔐 安全架构

### 1. 认证授权

#### **认证流程**
```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant S as Supabase Auth
    participant D as Database
    
    U->>A: 登录请求
    A->>S: 验证凭据
    S->>A: 返回 JWT Token
    A->>D: 携带 Token 请求数据
    D->>A: 验证 RLS 返回数据
```

#### **权限控制**
- **行级安全**: RLS 策略
- **用户隔离**: user_id 过滤
- **操作权限**: CRUD 权限控制

### 2. 数据安全

#### **数据保护**
- **传输加密**: HTTPS/TLS
- **存储加密**: Supabase 加密
- **访问控制**: RLS 策略
- **审计日志**: 操作记录

## 📈 性能优化

### 1. 前端优化

#### **代码分割**
```typescript
// 路由级别分割
const DashboardPage = lazy(() => import('./dashboard/page'))
const SearchPage = lazy(() => import('./search/page'))

// 组件级别分割
const StatsModal = lazy(() => import('./stats-modal'))
```

#### **资源优化**
- **图像优化**: Next.js Image
- **字体优化**: 字体子集
- **CSS 优化**: Tailwind 清理
- **JS 优化**: Tree shaking

### 2. 数据库优化

#### **查询优化**
```sql
-- 索引优化
CREATE INDEX idx_prompts_user_id ON prompts(user_id);
CREATE INDEX idx_prompts_category_id ON prompts(category_id);
CREATE INDEX idx_prompts_search ON prompts USING gin(to_tsvector('english', title || ' ' || content));

-- 查询优化
SELECT * FROM prompts 
WHERE user_id = $1 
  AND deleted_at IS NULL
ORDER BY updated_at DESC
LIMIT 20;
```

#### **缓存策略**
- **查询缓存**: 频繁查询缓存
- **结果缓存**: 计算结果缓存
- **连接池**: 数据库连接优化

## 🔄 实时更新架构

### 1. 实时同步

#### **Supabase Realtime**
```typescript
// 实时订阅
const subscription = supabase
  .channel('prompts_changes')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'prompts',
    filter: `user_id=eq.${userId}`
  }, handleRealtimeUpdate)
  .subscribe()
```

### 2. 乐观更新

#### **更新策略**
1. **立即更新 UI** (乐观更新)
2. **发送 API 请求**
3. **处理响应结果**
4. **错误回滚** (如果失败)

## 🚀 部署架构

### 1. Vercel 部署

#### **构建流程**
```yaml
# vercel.json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "functions": {
    "app/**/*.tsx": {
      "runtime": "nodejs18.x"
    }
  }
}
```

### 2. 环境配置

#### **环境变量**
```bash
# 生产环境
NEXT_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=xxx

# 开发环境
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=xxx
```

## 📊 监控和日志

### 1. 性能监控

#### **关键指标**
- **页面加载时间**: < 2s
- **首次内容绘制**: < 1s
- **交互响应时间**: < 100ms
- **搜索响应时间**: < 300ms

### 2. 错误监控

#### **错误处理**
```typescript
// 全局错误边界
class ErrorBoundary extends Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误
    console.error('Application Error:', error, errorInfo)
    
    // 发送错误报告
    reportError(error, errorInfo)
  }
}
```

## 🔮 未来架构规划

### 1. 微服务化
- **API 网关**: 统一入口
- **服务拆分**: 按功能拆分
- **服务通信**: GraphQL/REST

### 2. 边缘计算
- **CDN 优化**: 全球分发
- **边缘函数**: 就近计算
- **缓存优化**: 多级缓存

### 3. AI 集成
- **智能推荐**: 个性化推荐
- **自动分类**: AI 分类
- **内容生成**: AI 辅助生成

这个架构设计确保了系统的可扩展性、可维护性和高性能，为未来的功能扩展奠定了坚实的基础。
