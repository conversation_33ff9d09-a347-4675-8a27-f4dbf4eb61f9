"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[647],{11790:(e,t,a)=>{a.d(t,{J:()=>l});var s=a(46414);let o={PROMPTS:"local_prompts",CATEGORIES:"local_categories",TAGS:"local_tags",SYNC_QUEUE:"sync_queue",LAST_SYNC:"last_sync_time",USER_ID:"current_user_id"};class n{generateLocalId(){return"local_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}async getCurrentUserId(){{let e=localStorage.getItem(o.USER_ID);if(e)return e}let{data:{user:e}}=await this.supabase.auth.getUser();return e?(localStorage.setItem(o.USER_ID,e.id),e.id):null}getLocalData(e){try{let t=localStorage.getItem(e);return t?JSON.parse(t):[]}catch(e){return console.error("获取本地数据失败:",e),[]}}setLocalData(e,t){try{localStorage.setItem(e,JSON.stringify(t))}catch(e){console.error("保存本地数据失败:",e)}}addToSyncQueue(e){let t={...e,id:this.generateLocalId(),timestamp:Date.now(),retryCount:0};this.syncQueue.push(t),this.saveSyncQueue()}saveSyncQueue(){localStorage.setItem(o.SYNC_QUEUE,JSON.stringify(this.syncQueue))}loadSyncQueue(){try{let e=localStorage.getItem(o.SYNC_QUEUE);this.syncQueue=e?JSON.parse(e):[]}catch(e){console.error("加载同步队列失败:",e),this.syncQueue=[]}}async getPrompts(){console.log("\uD83D\uDCF1 从本地获取提示词数据");let e=this.getLocalData(o.PROMPTS);if(e.length>0)return console.log("✅ 本地找到 ".concat(e.length," 个提示词")),e;console.log("\uD83D\uDCF1 本地无数据，尝试从远程获取...");try{let{data:e,error:t}=await this.supabase.from("prompts").select("*, category:categories(*), prompt_tags(tag:tags(*))").order("updated_at",{ascending:!1});if(t)return console.warn("远程获取失败，返回空数组:",t),[];let a=(e||[]).map(e=>({...e,_localId:e.id,_isLocal:!1,_needsSync:!1,_lastModified:new Date(e.updated_at).getTime()}));return this.setLocalData(o.PROMPTS,a),console.log("✅ 从远程获取并缓存了 ".concat(a.length," 个提示词")),a}catch(e){return console.warn("获取远程提示词失败，返回空数组:",e),[]}}async getCategories(){console.log("\uD83D\uDCF1 从本地获取分类数据");let e=this.getLocalData(o.CATEGORIES);if(e.length>0)return console.log("✅ 本地找到 ".concat(e.length," 个分类")),e;console.log("\uD83D\uDCF1 本地无数据，尝试从远程获取...");try{let{data:e,error:t}=await this.supabase.from("categories").select("*").order("sort_order",{ascending:!0});if(t)return console.warn("远程获取失败，返回空数组:",t),[];let a=(e||[]).map(e=>({...e,_localId:e.id,_isLocal:!1,_needsSync:!1,_lastModified:new Date(e.updated_at).getTime()}));return this.setLocalData(o.CATEGORIES,a),console.log("✅ 从远程获取并缓存了 ".concat(a.length," 个分类")),a}catch(e){return console.warn("获取远程分类失败，返回空数组:",e),[]}}async createPrompt(e){let t=await this.getCurrentUserId();if(!t)throw Error("用户未登录");let a=this.generateLocalId(),s=Date.now(),n={id:a,...e,user_id:t,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),usage_count:0,_localId:a,_isLocal:!0,_needsSync:!0,_lastModified:s},l=this.getLocalData(o.PROMPTS);return l.unshift(n),this.setLocalData(o.PROMPTS,l),this.addToSyncQueue({type:"CREATE",table:"prompts",data:e,localId:a}),console.log("✅ 提示词已保存到本地，等待同步"),n}async createCategory(e){let t=await this.getCurrentUserId();if(!t)throw Error("用户未登录");let a=this.generateLocalId(),s=Date.now(),n={id:a,...e,user_id:t,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),_localId:a,_isLocal:!0,_needsSync:!0,_lastModified:s},l=this.getLocalData(o.CATEGORIES);return l.push(n),this.setLocalData(o.CATEGORIES,l),this.addToSyncQueue({type:"CREATE",table:"categories",data:e,localId:a}),console.log("✅ 分类已保存到本地，等待同步"),n}async updatePrompt(e,t){let a=this.getLocalData(o.PROMPTS),s=a.findIndex(t=>t.id===e||t._localId===e);if(-1===s)return console.error("提示词不存在:",e),null;let n=Date.now(),l={...a[s],...t,updated_at:new Date().toISOString(),_needsSync:!0,_lastModified:n};return a[s]=l,this.setLocalData(o.PROMPTS,a),this.addToSyncQueue({type:"UPDATE",table:"prompts",data:{id:a[s].id,...t},localId:a[s]._localId}),console.log("✅ 提示词已在本地更新，等待同步"),l}async deletePrompt(e){let t=this.getLocalData(o.PROMPTS),a=t.findIndex(t=>t.id===e||t._localId===e);if(-1===a)return console.error("提示词不存在:",e),!1;let s=t[a];return t.splice(a,1),this.setLocalData(o.PROMPTS,t),s._isLocal||this.addToSyncQueue({type:"DELETE",table:"prompts",data:{id:s.id},localId:s._localId}),console.log("✅ 提示词已在本地删除，等待同步"),!0}async updateCategory(e,t){let a=this.getLocalData(o.CATEGORIES),s=a.findIndex(t=>t.id===e||t._localId===e);if(-1===s)return console.error("分类不存在:",e),null;let n=Date.now(),l={...a[s],...t,updated_at:new Date().toISOString(),_needsSync:!0,_lastModified:n};return a[s]=l,this.setLocalData(o.CATEGORIES,a),this.addToSyncQueue({type:"UPDATE",table:"categories",data:{id:a[s].id,...t},localId:a[s]._localId}),console.log("✅ 分类已在本地更新，等待同步"),l}async deleteCategory(e){let t=this.getLocalData(o.CATEGORIES),a=t.findIndex(t=>t.id===e||t._localId===e);if(-1===a)return console.error("分类不存在:",e),!1;let s=t[a];return t.splice(a,1),this.setLocalData(o.CATEGORIES,t),s._isLocal||this.addToSyncQueue({type:"DELETE",table:"categories",data:{id:s.id},localId:s._localId}),console.log("✅ 分类已在本地删除，等待同步"),!0}async performSync(){if(this.syncInProgress||0===this.syncQueue.length)return;this.syncInProgress=!0,console.log("\uD83D\uDD04 开始同步 ".concat(this.syncQueue.length," 个操作"));let e=[];for(let t of this.syncQueue)try{await this.syncSingleOperation(t),e.push(t.id),console.log("✅ 同步成功: ".concat(t.type," ").concat(t.table))}catch(a){console.error("❌ 同步失败: ".concat(t.type," ").concat(t.table),a),t.retryCount++,t.retryCount>=3&&(console.error("\uD83D\uDEAB 放弃同步: ".concat(t.id)),e.push(t.id))}this.syncQueue=this.syncQueue.filter(t=>!e.includes(t.id)),this.saveSyncQueue(),localStorage.setItem(o.LAST_SYNC,Date.now().toString()),this.syncInProgress=!1,console.log("\uD83C\uDF89 同步完成，剩余 ".concat(this.syncQueue.length," 个操作"))}async syncSingleOperation(e){let{type:t,table:a,data:s,localId:o}=e;try{if("CREATE"===t){let{data:e,error:t}=await this.supabase.from(a).insert(s).select().single();if(t)throw t;this.updateLocalDataAfterSync(a,o,e)}else if("UPDATE"===t){let{data:e,error:t}=await this.supabase.from(a).update(s).eq("id",s.id).select().single();if(t)throw t;this.updateLocalDataAfterSync(a,o,e)}else if("DELETE"===t){console.log("\uD83D\uDD04 开始远程删除: ".concat(a," ").concat(s.id));let{error:e}=await this.supabase.from(a).delete().eq("id",s.id);if(e)throw console.error("❌ 远程删除失败: ".concat(a," ").concat(s.id),e),e;console.log("✅ 远程删除成功: ".concat(a," ").concat(s.id))}}catch(e){throw console.error("❌ 同步操作失败: ".concat(t," ").concat(a),e),e}}updateLocalDataAfterSync(e,t,a){let s="prompts"===e?o.PROMPTS:"categories"===e?o.CATEGORIES:o.TAGS,n=this.getLocalData(s),l=n.findIndex(e=>e._localId===t);-1!==l&&(n[l]={...a,_localId:a.id,_isLocal:!1,_needsSync:!1,_lastModified:new Date(a.updated_at).getTime()},this.setLocalData(s,n))}startBackgroundSync(){setTimeout(()=>this.performSync(),1e3),setInterval(()=>this.performSync(),3e4),window.addEventListener("focus",()=>this.performSync()),window.addEventListener("online",()=>this.performSync())}getLocalDataOnly(){let e=this.getLocalData(o.PROMPTS),t=this.getLocalData(o.CATEGORIES);return console.log("\uD83D\uDCF1 立即获取本地数据: ".concat(e.length," 个提示词, ").concat(t.length," 个分类")),{prompts:e,categories:t}}async preloadAllData(){console.log("\uD83D\uDE80 开始预加载所有数据到本地");try{let[e,t]=await Promise.all([this.getPrompts(),this.getCategories()]);console.log("✅ 预加载完成: ".concat(e.length," 个提示词, ").concat(t.length," 个分类"))}catch(e){console.error("❌ 预加载失败:",e)}}async refreshFromRemote(){console.log("\uD83D\uDD04 从远程刷新数据");try{this.setLocalData(o.PROMPTS,[]),this.setLocalData(o.CATEGORIES,[]),await this.preloadAllData(),console.log("✅ 远程数据刷新完成")}catch(e){console.error("❌ 远程刷新失败:",e)}}async manualSync(){await this.performSync()}getSyncStatus(){let e=localStorage.getItem(o.LAST_SYNC);return{pending:this.syncQueue.length,lastSync:e?parseInt(e):null}}getDetailedSyncStatus(){let e=localStorage.getItem(o.LAST_SYNC),t=this.getLocalData(o.PROMPTS),a=this.getLocalData(o.CATEGORIES),s=this.getLocalData(o.TAGS);return{pending:this.syncQueue.length,lastSync:e?parseInt(e):null,queue:[...this.syncQueue],localCounts:{prompts:t.length,categories:a.length,tags:s.length}}}clearLocalData(){Object.values(o).forEach(e=>{localStorage.removeItem(e)}),this.syncQueue=[],console.log("\uD83D\uDDD1️ 已清除所有本地数据")}constructor(){this.supabase=(0,s.U)(),this.syncInProgress=!1,this.syncQueue=[],this.loadSyncQueue(),this.startBackgroundSync()}}let l=new n},53580:(e,t,a)=>{a.d(t,{dj:()=>u});var s=a(12115);let o=0,n=new Map,l=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),i({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},r=[],c={toasts:[]};function i(e){c=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?l(a):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(c,e),r.forEach(e=>{e(c)})}function d(e){let{...t}=e,a=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>i({type:"DISMISS_TOAST",toastId:a});return i({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>i({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function u(){let[e,t]=s.useState(c);return s.useEffect(()=>(r.push(t),()=>{let e=r.indexOf(t);e>-1&&r.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>i({type:"DISMISS_TOAST",toastId:e})}}},57462:(e,t,a)=>{a.d(t,{o:()=>r});var s=a(95155),o=a(99840),n=a(97168),l=a(81704);function r(e){let{isOpen:t,onClose:a,onConfirm:r,title:c,description:i,itemName:d,isLoading:u=!1}=e;return(0,s.jsx)(o.lG,{open:t,onOpenChange:a,children:(0,s.jsxs)(o.Cf,{className:"max-w-md",children:[(0,s.jsx)(o.c7,{children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-red-100 rounded-full",children:(0,s.jsx)(l.Icon,{name:"exclamation-triangle",className:"h-5 w-5 text-red-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.L3,{className:"text-lg font-semibold",children:c}),(0,s.jsx)(o.rr,{className:"mt-1",children:i})]})]})}),d&&(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-3 my-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"即将删除："}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:d})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-4",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:a,disabled:u,children:"取消"}),(0,s.jsx)(n.$,{variant:"destructive",onClick:r,disabled:u,children:u?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),"删除中..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.Icon,{name:"trash",className:"h-4 w-4 mr-2"}),"确认删除"]})})]})]})})}},82714:(e,t,a)=>{a.d(t,{J:()=>i});var s=a(95155),o=a(12115),n=a(40968),l=a(74466),r=a(53999);let c=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(n.b,{ref:t,className:(0,r.cn)(c(),a),...o})});i.displayName=n.b.displayName},89852:(e,t,a)=>{a.d(t,{p:()=>l});var s=a(95155),o=a(12115),n=a(53999);let l=o.forwardRef((e,t)=>{let{className:a,type:o,...l}=e;return(0,s.jsx)("input",{type:o,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...l})});l.displayName="Input"},99474:(e,t,a)=>{a.d(t,{T:()=>l});var s=a(95155),o=a(12115),n=a(53999);let l=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...o})});l.displayName="Textarea"},99840:(e,t,a)=>{a.d(t,{Cf:()=>u,L3:()=>h,c7:()=>g,lG:()=>c,rr:()=>p});var s=a(95155),o=a(12115),n=a(15452),l=a(54416),r=a(53999);let c=n.bL;n.l9;let i=n.ZL;n.bm;let d=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(n.hJ,{ref:t,className:(0,r.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...o})});d.displayName=n.hJ.displayName;let u=o.forwardRef((e,t)=>{let{className:a,children:o,...c}=e;return(0,s.jsxs)(i,{children:[(0,s.jsx)(d,{}),(0,s.jsxs)(n.UC,{ref:t,className:(0,r.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[o,(0,s.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"关闭"})]})]})]})});u.displayName=n.UC.displayName;let g=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,r.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};g.displayName="DialogHeader";let h=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(n.hE,{ref:t,className:(0,r.cn)("text-lg font-semibold leading-none tracking-tight",a),...o})});h.displayName=n.hE.displayName;let p=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(n.VY,{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",a),...o})});p.displayName=n.VY.displayName}}]);