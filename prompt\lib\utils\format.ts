/**
 * 格式化工具函数
 */

/**
 * 格式化日期
 */
export function formatDate(dateString: string, options?: Intl.DateTimeFormatOptions): string {
  const date = new Date(dateString)
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options
  }
  
  return date.toLocaleDateString('zh-CN', defaultOptions)
}

/**
 * 格式化相对时间
 */
export function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return '刚刚'
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours}小时前`
  }
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) {
    return `${diffInDays}天前`
  }
  
  const diffInWeeks = Math.floor(diffInDays / 7)
  if (diffInWeeks < 4) {
    return `${diffInWeeks}周前`
  }
  
  const diffInMonths = Math.floor(diffInDays / 30)
  if (diffInMonths < 12) {
    return `${diffInMonths}个月前`
  }
  
  const diffInYears = Math.floor(diffInDays / 365)
  return `${diffInYears}年前`
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化数字（添加千分位分隔符）
 */
export function formatNumber(num: number): string {
  // 处理 NaN、undefined、null 等异常值
  if (typeof num !== 'number' || isNaN(num) || !isFinite(num)) {
    return '0'
  }
  return num.toLocaleString('zh-CN')
}

/**
 * 截断文本
 */
export function truncateText(text: string, maxLength: number, suffix: string = '...'): string {
  if (text.length <= maxLength) {
    return text
  }
  
  return text.slice(0, maxLength - suffix.length) + suffix
}

/**
 * 高亮搜索关键词
 */
export function highlightSearchTerm(text: string, searchTerm: string): string {
  if (!searchTerm.trim()) {
    return text
  }
  
  const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 格式化标签列表
 */
export function formatTagList(tags: string[], maxDisplay: number = 3): string {
  if (tags.length === 0) {
    return '无标签'
  }
  
  if (tags.length <= maxDisplay) {
    return tags.join(', ')
  }
  
  const displayTags = tags.slice(0, maxDisplay)
  const remainingCount = tags.length - maxDisplay
  
  return `${displayTags.join(', ')} +${remainingCount}`
}

/**
 * 格式化使用次数
 */
export function formatUsageCount(count: number): string {
  // 处理 NaN、undefined、null 等异常值
  if (typeof count !== 'number' || isNaN(count) || !isFinite(count) || count < 0) {
    return '未使用'
  }

  if (count === 0) {
    return '未使用'
  }

  if (count === 1) {
    return '使用1次'
  }

  if (count < 1000) {
    return `使用${count}次`
  }

  if (count < 1000000) {
    const k = Math.floor(count / 100) / 10
    return `使用${k}k次`
  }

  const m = Math.floor(count / 100000) / 10
  return `使用${m}m次`
}

/**
 * 格式化颜色值
 */
export function formatColor(color: string): string {
  // 确保颜色值以 # 开头
  if (!color.startsWith('#')) {
    return `#${color}`
  }
  
  return color.toLowerCase()
}

/**
 * 生成随机颜色
 */
export function generateRandomColor(): string {
  const colors = [
    '#ef4444', // red
    '#f97316', // orange
    '#f59e0b', // amber
    '#eab308', // yellow
    '#84cc16', // lime
    '#22c55e', // green
    '#10b981', // emerald
    '#14b8a6', // teal
    '#06b6d4', // cyan
    '#0ea5e9', // sky
    '#3b82f6', // blue
    '#6366f1', // indigo
    '#8b5cf6', // violet
    '#a855f7', // purple
    '#d946ef', // fuchsia
    '#ec4899', // pink
    '#f43f5e', // rose
  ]
  
  return colors[Math.floor(Math.random() * colors.length)]
}

/**
 * 验证颜色值
 */
export function isValidColor(color: string): boolean {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  return hexColorRegex.test(color)
}

/**
 * 格式化搜索查询
 */
export function formatSearchQuery(query: string): string {
  return query
    .trim()
    .replace(/\s+/g, ' ') // 合并多个空格
    .toLowerCase()
}

/**
 * 提取文本摘要
 */
export function extractSummary(text: string, maxLength: number = 150): string {
  // 移除 Markdown 语法
  const cleanText = text
    .replace(/#{1,6}\s+/g, '') // 移除标题标记
    .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
    .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
    .replace(/`(.*?)`/g, '$1') // 移除代码标记
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，保留文本
    .replace(/\n+/g, ' ') // 换行符转空格
    .trim()
  
  return truncateText(cleanText, maxLength)
}

/**
 * 格式化 JSON 数据
 */
export function formatJSON(data: any, pretty: boolean = true): string {
  try {
    return pretty 
      ? JSON.stringify(data, null, 2)
      : JSON.stringify(data)
  } catch (error) {
    return String(data)
  }
}
