# 🚀 提示词管理工具

一个精美的现代化提示词管理工具，基于 Next.js 14 和 Supabase 构建，支持分类管理、标签系统、混合搜索、数据统计等功能。

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/prompt-management-tool)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Next.js](https://img.shields.io/badge/Next.js-000000?logo=next.js&logoColor=white)](https://nextjs.org/)
[![Supabase](https://img.shields.io/badge/Supabase-3ECF8E?logo=supabase&logoColor=white)](https://supabase.com/)

## ✨ 功能特性

### 🎯 核心功能
- **📝 提示词管理**: 创建、编辑、删除、复制提示词
- **📂 分类系统**: 支持自定义分类，拖拽排序
- **🏷️ 标签管理**: 灵活的标签系统，支持颜色标识
- **🔍 混合搜索**: 本地实时搜索 + 在线全文搜索
- **📊 数据统计**: 使用统计、热门排行、趋势分析

### 🎨 用户体验
- **📱 响应式设计**: 完美适配桌面端和移动端
- **🌙 暗色模式**: 支持明暗主题切换
- **⚡ 实时搜索**: 搜索历史记录和智能建议
- **📋 复制历史**: 本地存储复制记录，可调整面板宽度
- **🎯 一键复制**: 快速复制到剪贴板

### 🔧 技术特性
- **🏗️ 现代化架构**: Next.js 14 App Router + TypeScript
- **🗄️ 实时数据库**: Supabase 后端服务 + PostgreSQL
- **🎨 组件化设计**: ShadcnUI + Tailwind CSS
- **🎭 图标系统**: Font Awesome + Lucide React
- **🔄 状态管理**: React Hooks + 本地存储
- **🚀 性能优化**: 混合搜索系统，减少 90%+ 数据库查询

## 🚀 快速开始

### 📋 环境要求
- **Node.js**: 18.0.0 或更高版本
- **npm/yarn/pnpm**: 最新版本
- **Supabase 账户**: 免费账户即可

### ⚡ 一键部署

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/prompt-management-tool&env=NEXT_PUBLIC_SUPABASE_URL,NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY)

### 🛠️ 本地开发

1. **克隆项目**
```bash
git clone https://github.com/your-username/prompt-management-tool.git
cd prompt-management-tool
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

3. **配置环境变量**
```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件：
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-anon-key
```

4. **设置数据库**
在 Supabase SQL Editor 中依次执行：
- `database/production/01-schema.sql` - 创建表结构和函数
- `database/production/02-seed.sql` - 初始化数据

5. **启动开发服务器**
```bash
npm run dev
# 或使用 Turbopack 获得更快的构建速度
npm run dev --turbo
```

🎉 访问 [http://localhost:3000](http://localhost:3000) 查看应用！

## 📁 项目结构

```
prompt/
├── app/                    # Next.js 应用路由
│   ├── auth/              # 认证页面
│   ├── dashboard/         # 主应用页面
│   │   ├── categories/    # 分类管理
│   │   ├── search/        # 搜索页面
│   │   └── stats/         # 数据统计
│   └── layout.tsx         # 根布局
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   ├── prompt-card.tsx   # 提示词卡片
│   ├── search-bar.tsx    # 搜索栏
│   └── sidebar.tsx       # 侧边栏
├── lib/                  # 工具库
│   ├── database/         # 数据库操作
│   ├── supabase/         # Supabase 配置
│   └── utils/            # 工具函数
├── types/                # TypeScript 类型定义
├── database/             # 数据库脚本
└── hooks/                # React Hooks
```

## 🎯 主要页面

### 主仪表板 (`/dashboard`)
- 提示词网格展示
- 分类筛选侧边栏
- 搜索功能
- 复制历史记录

### 搜索页面 (`/dashboard/search`)
- 高级搜索功能
- 多条件筛选
- 搜索结果展示
- 搜索历史管理

### 分类管理 (`/dashboard/categories`)
- 分类创建和编辑
- 拖拽排序
- 图标和颜色自定义
- 使用统计

### 数据统计 (`/dashboard/stats`)
- 总览统计
- 热门排行
- 使用趋势
- 数据洞察

## 🔧 技术栈

### 前端
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **组件**: ShadcnUI
- **图标**: Font Awesome + Lucide React
- **状态**: React Hooks

### 后端
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **实时**: Supabase Realtime
- **存储**: Supabase Storage

### 开发工具
- **包管理**: npm
- **代码格式**: Prettier
- **类型检查**: TypeScript
- **构建**: Next.js

## 📊 数据库设计

### 核心表结构
- `categories` - 分类表
- `prompts` - 提示词表
- `tags` - 标签表
- `prompt_tags` - 提示词标签关联表
- `search_history` - 搜索历史表
- `user_preferences` - 用户偏好设置表

### 特性
- 行级安全 (RLS)
- 软删除支持
- 自动时间戳
- 用户数据隔离

## 🎨 设计系统

### 颜色方案
- 主色调：蓝色系
- 支持明暗主题
- 语义化颜色
- 无障碍设计

### 响应式断点
- `sm`: 640px+
- `md`: 768px+
- `lg`: 1024px+
- `xl`: 1280px+
- `2xl`: 1536px+

## 🚀 部署

### Vercel 部署
1. 连接 GitHub 仓库
2. 配置环境变量
3. 自动部署

### Supabase 配置
1. 创建新项目
2. 执行数据库脚本
3. 配置认证设置
4. 获取 API 密钥

## 📝 开发指南

### 添加新功能
1. 创建类型定义 (`types/`)
2. 实现数据库操作 (`lib/database/`)
3. 创建 UI 组件 (`components/`)
4. 添加页面路由 (`app/`)

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 ESLint 规则
- 组件使用 JSDoc 注释
- 保持代码简洁和可读性

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Supabase](https://supabase.com/) - 后端服务
- [ShadcnUI](https://ui.shadcn.com/) - UI 组件库
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Font Awesome](https://fontawesome.com/) - 图标库
