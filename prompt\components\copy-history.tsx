"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@/components/ui/icon'
import { formatRelativeTime, truncateText } from '@/lib/utils/format'

interface CopyHistoryItem {
  id: string
  promptId: string
  promptTitle: string
  content: string
  copiedAt: string
  category?: {
    name: string
    color: string
  }
}

interface CopyHistoryProps {
  className?: string
}

export function CopyHistory({ className }: CopyHistoryProps) {
  const [history, setHistory] = useState<CopyHistoryItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadCopyHistory()
  }, [])

  const loadCopyHistory = () => {
    try {
      setIsLoading(true)
      // 从 localStorage 获取复制历史
      const stored = localStorage.getItem('prompt-copy-history')
      if (stored) {
        const parsed = JSON.parse(stored)
        setHistory(parsed.slice(0, 20)) // 只保留最近20条
      }
    } catch (error) {
      console.error('加载复制历史失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const addCopyRecord = (promptId: string, promptTitle: string, content: string, category?: { name: string; color: string }) => {
    const newRecord: CopyHistoryItem = {
      id: Date.now().toString(),
      promptId,
      promptTitle,
      content,
      copiedAt: new Date().toISOString(),
      category
    }

    const updatedHistory = [newRecord, ...history].slice(0, 20)
    setHistory(updatedHistory)
    
    try {
      localStorage.setItem('prompt-copy-history', JSON.stringify(updatedHistory))
    } catch (error) {
      console.error('保存复制历史失败:', error)
    }
  }

  const clearHistory = () => {
    setHistory([])
    try {
      localStorage.removeItem('prompt-copy-history')
    } catch (error) {
      console.error('清除复制历史失败:', error)
    }
  }

  const handleCopyAgain = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      // 可以添加 toast 提示
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  // 暴露 addCopyRecord 方法给父组件使用
  useEffect(() => {
    // 将方法挂载到全局，供其他组件调用
    (window as any).addCopyRecord = addCopyRecord
    
    return () => {
      delete (window as any).addCopyRecord
    }
  }, [history])

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="clipboard" className="h-5 w-5" />
            复制历史
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Icon name="clipboard" className="h-5 w-5" />
              复制历史
            </CardTitle>
            <CardDescription>
              最近复制的提示词记录
            </CardDescription>
          </div>
          {history.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearHistory}
            >
              <Icon name="trash" className="h-4 w-4 mr-2" />
              清除
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {history.length > 0 ? (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {history.map((item) => (
              <div
                key={item.id}
                className="group p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm truncate">
                        {item.promptTitle}
                      </h4>
                      {item.category && (
                        <Badge
                          variant="secondary"
                          className="text-xs"
                          style={{ 
                            backgroundColor: `${item.category.color}20`,
                            color: item.category.color 
                          }}
                        >
                          {item.category.name}
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">
                      {truncateText(item.content, 100)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatRelativeTime(item.copiedAt)}
                    </p>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => handleCopyAgain(item.content)}
                  >
                    <Icon name="copy" className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Icon name="clipboard" className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              暂无复制记录
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              复制提示词后会在这里显示历史记录
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 导出一个 hook 供其他组件使用
export function useCopyHistory() {
  const addCopyRecord = (promptId: string, promptTitle: string, content: string, category?: { name: string; color: string }) => {
    if ((window as any).addCopyRecord) {
      (window as any).addCopyRecord(promptId, promptTitle, content, category)
    }
  }

  return { addCopyRecord }
}
