-- =====================================================
-- 提示词管理工具 - 生产环境数据库架构
-- =====================================================
-- 版本: 1.0.0
-- 创建时间: 2025-07-26
-- 描述: 完整的数据库结构，包含所有表、索引、函数和安全策略
-- =====================================================

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. 核心数据表
-- =====================================================

-- 分类表
CREATE TABLE IF NOT EXISTS categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    color VARCHAR(7) DEFAULT '#6366f1',
    icon VARCHAR(50) DEFAULT 'folder',
    sort_order INTEGER DEFAULT 0,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT categories_name_user_unique UNIQUE(name, user_id)
);

-- 标签表
CREATE TABLE IF NOT EXISTS tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    color VARCHAR(7) DEFAULT '#10b981',
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT tags_name_user_unique UNIQUE(name, user_id)
);

-- 提示词表
CREATE TABLE IF NOT EXISTS prompts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description VARCHAR(500),
    content TEXT NOT NULL,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    usage_count INTEGER DEFAULT 0 CHECK (usage_count >= 0),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- 提示词标签关联表
CREATE TABLE IF NOT EXISTS prompt_tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    prompt_id UUID REFERENCES prompts(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT prompt_tags_unique UNIQUE(prompt_id, tag_id)
);

-- 搜索历史表
CREATE TABLE IF NOT EXISTS search_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    term VARCHAR(200) NOT NULL,
    count INTEGER DEFAULT 1 CHECK (count > 0),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT search_history_term_user_unique UNIQUE(term, user_id)
);

-- 用户偏好设置表
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    theme VARCHAR(20) DEFAULT 'system',
    language VARCHAR(10) DEFAULT 'zh-CN',
    sidebar_collapsed BOOLEAN DEFAULT FALSE,
    copy_history_width INTEGER DEFAULT 300,
    items_per_page INTEGER DEFAULT 12 CHECK (items_per_page > 0),
    default_category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. 索引优化
-- =====================================================

-- 分类表索引
CREATE INDEX IF NOT EXISTS idx_categories_user_id ON categories(user_id);
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);
CREATE INDEX IF NOT EXISTS idx_categories_deleted_at ON categories(deleted_at);

-- 标签表索引
CREATE INDEX IF NOT EXISTS idx_tags_user_id ON tags(user_id);
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);

-- 提示词表索引
CREATE INDEX IF NOT EXISTS idx_prompts_user_id ON prompts(user_id);
CREATE INDEX IF NOT EXISTS idx_prompts_category_id ON prompts(category_id);
CREATE INDEX IF NOT EXISTS idx_prompts_created_at ON prompts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_prompts_updated_at ON prompts(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_prompts_usage_count ON prompts(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_prompts_deleted_at ON prompts(deleted_at);

-- 全文搜索索引
CREATE INDEX IF NOT EXISTS idx_prompts_search 
ON prompts USING gin(to_tsvector('english', title || ' ' || content));

-- 提示词标签关联表索引
CREATE INDEX IF NOT EXISTS idx_prompt_tags_prompt_id ON prompt_tags(prompt_id);
CREATE INDEX IF NOT EXISTS idx_prompt_tags_tag_id ON prompt_tags(tag_id);

-- 搜索历史表索引
CREATE INDEX IF NOT EXISTS idx_search_history_user_id ON search_history(user_id);
CREATE INDEX IF NOT EXISTS idx_search_history_updated_at ON search_history(updated_at DESC);

-- 用户偏好设置表索引
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);

-- =====================================================
-- 3. 触发器函数
-- =====================================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间戳触发器
DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;
CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_prompts_updated_at ON prompts;
CREATE TRIGGER update_prompts_updated_at
    BEFORE UPDATE ON prompts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_search_history_updated_at ON search_history;
CREATE TRIGGER update_search_history_updated_at
    BEFORE UPDATE ON search_history
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON user_preferences;
CREATE TRIGGER update_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 4. 行级安全策略 (RLS)
-- =====================================================

-- 启用 RLS
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompts ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompt_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- 分类表 RLS 策略
DROP POLICY IF EXISTS "Users can view their own categories" ON categories;
CREATE POLICY "Users can view their own categories" ON categories
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own categories" ON categories;
CREATE POLICY "Users can insert their own categories" ON categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own categories" ON categories;
CREATE POLICY "Users can update their own categories" ON categories
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own categories" ON categories;
CREATE POLICY "Users can delete their own categories" ON categories
    FOR DELETE USING (auth.uid() = user_id);

-- 标签表 RLS 策略
DROP POLICY IF EXISTS "Users can view their own tags" ON tags;
CREATE POLICY "Users can view their own tags" ON tags
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own tags" ON tags;
CREATE POLICY "Users can insert their own tags" ON tags
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own tags" ON tags;
CREATE POLICY "Users can update their own tags" ON tags
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own tags" ON tags;
CREATE POLICY "Users can delete their own tags" ON tags
    FOR DELETE USING (auth.uid() = user_id);

-- 提示词表 RLS 策略
DROP POLICY IF EXISTS "Users can view their own prompts" ON prompts;
CREATE POLICY "Users can view their own prompts" ON prompts
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own prompts" ON prompts;
CREATE POLICY "Users can insert their own prompts" ON prompts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own prompts" ON prompts;
CREATE POLICY "Users can update their own prompts" ON prompts
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own prompts" ON prompts;
CREATE POLICY "Users can delete their own prompts" ON prompts
    FOR DELETE USING (auth.uid() = user_id);

-- 提示词标签关联表 RLS 策略
DROP POLICY IF EXISTS "Users can view their own prompt_tags" ON prompt_tags;
CREATE POLICY "Users can view their own prompt_tags" ON prompt_tags
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM prompts 
            WHERE prompts.id = prompt_tags.prompt_id 
            AND prompts.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Users can insert their own prompt_tags" ON prompt_tags;
CREATE POLICY "Users can insert their own prompt_tags" ON prompt_tags
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM prompts 
            WHERE prompts.id = prompt_tags.prompt_id 
            AND prompts.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Users can delete their own prompt_tags" ON prompt_tags;
CREATE POLICY "Users can delete their own prompt_tags" ON prompt_tags
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM prompts 
            WHERE prompts.id = prompt_tags.prompt_id 
            AND prompts.user_id = auth.uid()
        )
    );

-- 搜索历史表 RLS 策略
DROP POLICY IF EXISTS "Users can view their own search_history" ON search_history;
CREATE POLICY "Users can view their own search_history" ON search_history
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own search_history" ON search_history;
CREATE POLICY "Users can insert their own search_history" ON search_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own search_history" ON search_history;
CREATE POLICY "Users can update their own search_history" ON search_history
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own search_history" ON search_history;
CREATE POLICY "Users can delete their own search_history" ON search_history
    FOR DELETE USING (auth.uid() = user_id);

-- 用户偏好设置表 RLS 策略
DROP POLICY IF EXISTS "Users can view their own preferences" ON user_preferences;
CREATE POLICY "Users can view their own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own preferences" ON user_preferences;
CREATE POLICY "Users can insert their own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own preferences" ON user_preferences;
CREATE POLICY "Users can update their own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own preferences" ON user_preferences;
CREATE POLICY "Users can delete their own preferences" ON user_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- 5. 数据库函数
-- =====================================================

-- 搜索提示词函数
CREATE OR REPLACE FUNCTION search_prompts(
    search_query TEXT DEFAULT NULL,
    category_filter UUID DEFAULT NULL,
    user_filter UUID DEFAULT NULL,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    title VARCHAR(200),
    description VARCHAR(500),
    content TEXT,
    category_id UUID,
    usage_count INTEGER,
    user_id UUID,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    category_name VARCHAR(100),
    category_color VARCHAR(7),
    category_icon VARCHAR(50),
    tag_names TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.title,
        p.description,
        p.content,
        p.category_id,
        p.usage_count,
        p.user_id,
        p.metadata,
        p.created_at,
        p.updated_at,
        c.name as category_name,
        c.color as category_color,
        c.icon as category_icon,
        COALESCE(
            ARRAY_AGG(t.name) FILTER (WHERE t.name IS NOT NULL),
            ARRAY[]::TEXT[]
        ) as tag_names
    FROM prompts p
    LEFT JOIN categories c ON p.category_id = c.id
    LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
    LEFT JOIN tags t ON pt.tag_id = t.id
    WHERE
        p.deleted_at IS NULL
        AND (user_filter IS NULL OR p.user_id = user_filter)
        AND (category_filter IS NULL OR p.category_id = category_filter)
        AND (
            search_query IS NULL
            OR search_query = ''
            OR to_tsvector('english', p.title || ' ' || p.content) @@ plainto_tsquery('english', search_query)
            OR p.title ILIKE '%' || search_query || '%'
            OR p.content ILIKE '%' || search_query || '%'
        )
    GROUP BY p.id, c.name, c.color, c.icon
    ORDER BY p.updated_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 获取分类统计函数
CREATE OR REPLACE FUNCTION get_category_stats(user_filter UUID)
RETURNS TABLE (
    id UUID,
    name VARCHAR(100),
    description VARCHAR(500),
    color VARCHAR(7),
    icon VARCHAR(50),
    sort_order INTEGER,
    prompt_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id,
        c.name,
        c.description,
        c.color,
        c.icon,
        c.sort_order,
        COUNT(p.id) as prompt_count
    FROM categories c
    LEFT JOIN prompts p ON c.id = p.category_id AND p.deleted_at IS NULL
    WHERE
        c.deleted_at IS NULL
        AND c.user_id = user_filter
    GROUP BY c.id, c.name, c.description, c.color, c.icon, c.sort_order
    ORDER BY c.sort_order, c.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 增加使用次数函数
CREATE OR REPLACE FUNCTION increment_usage_count(prompt_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE prompts
    SET usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE id = prompt_uuid
    AND user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 添加或更新搜索历史函数
CREATE OR REPLACE FUNCTION upsert_search_history(search_term TEXT, user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO search_history (term, user_id, count)
    VALUES (search_term, user_uuid, 1)
    ON CONFLICT (term, user_id)
    DO UPDATE SET
        count = search_history.count + 1,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 架构创建完成
-- =====================================================
