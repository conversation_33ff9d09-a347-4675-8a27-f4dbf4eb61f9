{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "s-maxage=60, stale-while-revalidate=300"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/favicon.ico", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}], "redirects": [{"source": "/", "destination": "/dashboard", "permanent": false}]}