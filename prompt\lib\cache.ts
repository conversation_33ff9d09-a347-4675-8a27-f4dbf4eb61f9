/**
 * 简单的内存缓存工具
 * 用于缓存数据库查询结果，减少重复请求
 */

interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number // 生存时间（毫秒）
}

class SimpleCache {
  private cache = new Map<string, CacheItem<any>>()

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttl 生存时间（毫秒），默认5分钟
   */
  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据或null
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data as T
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  delete(key: string): void {
    this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 删除匹配模式的缓存
   * @param pattern 匹配模式
   */
  deletePattern(pattern: string): void {
    const regex = new RegExp(pattern)
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 创建全局缓存实例
export const cache = new SimpleCache()

// 缓存键常量
export const CACHE_KEYS = {
  CATEGORIES: 'categories',
  PROMPTS: (params: string) => `prompts:${params}`,
  SEARCH_HISTORY: 'search_history',
  PROMPT_DETAIL: (id: string) => `prompt:${id}`,
} as const

// 缓存时间常量（毫秒）
export const CACHE_TTL = {
  SHORT: 1 * 60 * 1000,      // 1分钟
  MEDIUM: 5 * 60 * 1000,     // 5分钟
  LONG: 15 * 60 * 1000,      // 15分钟
  VERY_LONG: 60 * 60 * 1000, // 1小时
} as const

/**
 * 缓存装饰器函数
 * @param cacheKey 缓存键
 * @param ttl 生存时间
 * @param fn 要缓存的函数
 */
export async function withCache<T>(
  cacheKey: string,
  ttl: number,
  fn: () => Promise<T>
): Promise<T> {
  // 尝试从缓存获取
  const cached = cache.get<T>(cacheKey)
  if (cached !== null) {
    console.log(`Cache hit: ${cacheKey}`)
    return cached
  }

  // 缓存未命中，执行函数
  console.log(`Cache miss: ${cacheKey}`)
  const result = await fn()
  
  // 存入缓存
  cache.set(cacheKey, result, ttl)
  
  return result
}

/**
 * 清除相关缓存
 * @param patterns 要清除的缓存模式
 */
export function invalidateCache(patterns: string[]): void {
  patterns.forEach(pattern => {
    cache.deletePattern(pattern)
  })
  console.log(`Invalidated cache patterns: ${patterns.join(', ')}`)
}
