"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[938],{18064:(e,t,r)=>{r.d(t,{ThemeSwitcher:()=>v});var a=r(95155),o=r(97168),s=r(12115),n=r(29020),i=r(13052),c=r(5196),l=r(9428),d=r(53999);let h=n.bL,u=n.l9;n.YJ,n.ZL,n.Pb;let m=n.z6;s.forwardRef((e,t)=>{let{className:r,inset:o,children:s,...c}=e;return(0,a.jsxs)(n.ZP,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",o&&"pl-8",r),...c,children:[s,(0,a.jsx)(i.A,{className:"ml-auto"})]})}).displayName=n.ZP.displayName,s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(n.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",r),...o})}).displayName=n.G5.displayName;let f=s.forwardRef((e,t)=>{let{className:r,sideOffset:o=4,...s}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{ref:t,sideOffset:o,className:(0,d.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",r),...s})})});f.displayName=n.UC.displayName,s.forwardRef((e,t)=>{let{className:r,inset:o,...s}=e;return(0,a.jsx)(n.q7,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",o&&"pl-8",r),...s})}).displayName=n.q7.displayName,s.forwardRef((e,t)=>{let{className:r,children:o,checked:s,...i}=e;return(0,a.jsxs)(n.H_,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:s,...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})}),o]})}).displayName=n.H_.displayName;let g=s.forwardRef((e,t)=>{let{className:r,children:o,...s}=e;return(0,a.jsxs)(n.hN,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),o]})});g.displayName=n.hN.displayName,s.forwardRef((e,t)=>{let{className:r,inset:o,...s}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",o&&"pl-8",r),...s})}).displayName=n.JU.displayName,s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...o})}).displayName=n.wv.displayName;var p=r(62098),w=r(93509),y=r(42148),x=r(51362);let v=()=>{let[e,t]=(0,s.useState)(!1),{theme:r,setTheme:n}=(0,x.D)();return((0,s.useEffect)(()=>{t(!0)},[]),e)?(0,a.jsxs)(h,{children:[(0,a.jsx)(u,{asChild:!0,children:(0,a.jsx)(o.$,{variant:"ghost",size:"sm",children:"light"===r?(0,a.jsx)(p.A,{size:16,className:"text-muted-foreground"},"light"):"dark"===r?(0,a.jsx)(w.A,{size:16,className:"text-muted-foreground"},"dark"):(0,a.jsx)(y.A,{size:16,className:"text-muted-foreground"},"system")})}),(0,a.jsx)(f,{className:"w-content",align:"start",children:(0,a.jsxs)(m,{value:r,onValueChange:e=>n(e),children:[(0,a.jsxs)(g,{className:"flex gap-2",value:"light",children:[(0,a.jsx)(p.A,{size:16,className:"text-muted-foreground"})," ",(0,a.jsx)("span",{children:"Light"})]}),(0,a.jsxs)(g,{className:"flex gap-2",value:"dark",children:[(0,a.jsx)(w.A,{size:16,className:"text-muted-foreground"})," ",(0,a.jsx)("span",{children:"Dark"})]}),(0,a.jsxs)(g,{className:"flex gap-2",value:"system",children:[(0,a.jsx)(y.A,{size:16,className:"text-muted-foreground"})," ",(0,a.jsx)("span",{children:"System"})]})]})})]}):null}},19987:(e,t,r)=>{function a(e,t){let r=new Date(e),a={year:"numeric",month:"short",day:"numeric",...t};return r.toLocaleDateString("zh-CN",a)}function o(e){let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/1e3);if(r<60)return"刚刚";let a=Math.floor(r/60);if(a<60)return"".concat(a,"分钟前");let o=Math.floor(a/60);if(o<24)return"".concat(o,"小时前");let s=Math.floor(o/24);if(s<7)return"".concat(s,"天前");let n=Math.floor(s/7);if(n<4)return"".concat(n,"周前");let i=Math.floor(s/30);if(i<12)return"".concat(i,"个月前");let c=Math.floor(s/365);return"".concat(c,"年前")}function s(e){return"number"!=typeof e||isNaN(e)||!isFinite(e)?"0":e.toLocaleString("zh-CN")}function n(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return e.length<=t?e:e.slice(0,t-r.length)+r}function i(e){return/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(e)}r.d(t,{EJ:()=>n,Yq:()=>a,ZV:()=>s,fw:()=>o,o1:()=>i})},34938:(e,t,r)=>{r.d(t,{eQ:()=>O,jG:()=>U,c1:()=>w,Wf:()=>A,VZ:()=>I,Ir:()=>N,bW:()=>g,MN:()=>y,tf:()=>S,R9:()=>R,PH:()=>C,Fw:()=>_,oO:()=>v,FA:()=>M,Q2:()=>z,pB:()=>k,Yv:()=>P,wp:()=>p,qj:()=>j});var a=r(46414);class o{set(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5;this.cache.set(e,{data:t,timestamp:Date.now(),ttl:r})}get(e){let t=this.cache.get(e);return t?Date.now()-t.timestamp>t.ttl?(this.cache.delete(e),null):t.data:null}delete(e){this.cache.delete(e)}clear(){this.cache.clear()}deletePattern(e){let t=new RegExp(e);for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}constructor(){this.cache=new Map}}let s=new o,n={CATEGORIES:"categories"},i={MEDIUM:3e5};async function c(e,t,r){let a=s.get(e);if(null!==a)return console.log("Cache hit: ".concat(e)),a;console.log("Cache miss: ".concat(e));let o=await r();return s.set(e,o,t),o}class l{generateKey(e,t){if(!t)return e;let r=this.sortObject(t);return"".concat(e,":").concat(JSON.stringify(r))}sortObject(e){if(null===e||"object"!=typeof e)return e;if(Array.isArray(e))return e.map(e=>this.sortObject(e));let t={};return Object.keys(e).sort().forEach(r=>{t[r]=this.sortObject(e[r])}),t}set(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5,a=arguments.length>3?arguments[3]:void 0,o=this.generateKey(e,a),s={data:t,timestamp:Date.now(),ttl:r,version:this.version};this.memoryCache.set(o,s);try{localStorage.setItem("cache_".concat(o),JSON.stringify(s))}catch(e){console.warn("localStorage 缓存失败:",e)}}get(e,t){let r=this.generateKey(e,t),a=this.memoryCache.get(r);if(!a)try{let e=localStorage.getItem("cache_".concat(r));e&&(a=JSON.parse(e))&&this.memoryCache.set(r,a)}catch(e){console.warn("localStorage 读取失败:",e)}return a?a.version!==this.version||Date.now()-a.timestamp>a.ttl?(this.delete(e,t),null):a.data:null}delete(e,t){let r=this.generateKey(e,t);this.memoryCache.delete(r),localStorage.removeItem("cache_".concat(r))}clear(){this.memoryCache.clear(),Object.keys(localStorage).forEach(e=>{e.startsWith("cache_")&&localStorage.removeItem(e)})}clearPattern(e){for(let t of this.memoryCache.keys())t.includes(e)&&this.memoryCache.delete(t);Object.keys(localStorage).forEach(t=>{t.startsWith("cache_")&&t.includes(e)&&localStorage.removeItem(t)})}getStats(){let e=0,t=[];return Object.keys(localStorage).forEach(r=>{r.startsWith("cache_")&&(e++,t.push(r.replace("cache_","")))}),{memorySize:this.memoryCache.size,localStorageSize:e,keys:Array.from(new Set([...Array.from(this.memoryCache.keys()),...t]))}}constructor(){this.memoryCache=new Map,this.version="1.0.0"}}let d=new l,h={CATEGORIES:"categories",PROMPTS:"prompts"},u={MEDIUM:12e4,LONG:6e5};async function m(e,t,r,a){let o=d.get(e,a);if(null!==o)return console.log("Client cache hit: ".concat(e),a),o;console.log("Client cache miss: ".concat(e),a);let s=await r();return d.set(e,s,t,a),s}let f=(0,a.U)();async function g(){return m(h.CATEGORIES,u.LONG,async()=>{try{let{data:{user:e}}=await f.auth.getUser();if(!e)throw Error("用户未登录");let{data:t,error:r}=await f.from("categories").select("*").eq("user_id",e.id).is("deleted_at",null).order("sort_order",{ascending:!0});if(r)throw r;let{data:a,error:o}=await f.from("prompts").select("category_id").eq("user_id",e.id).is("deleted_at",null);if(o)throw o;let s=new Map;return null==a||a.forEach(e=>{let t=e.category_id;t&&s.set(t,(s.get(t)||0)+1)}),t.map(e=>({...e,prompt_count:s.get(e.id)||0}))}catch(e){throw console.error("获取分类失败:",e),Error("获取分类失败")}})}async function p(e){try{var t,r;let{data:{user:a}}=await f.auth.getUser();if(!a)throw Error("用户未登录");let{error:o}=await f.rpc("update_categories_order",{category_ids:e,user_uuid:a.id});if(o)throw o;(t=[n.CATEGORIES]).forEach(e=>{s.deletePattern(e)}),console.log("Invalidated cache patterns: ".concat(t.join(", "))),(r=[h.CATEGORIES]).forEach(e=>{d.clearPattern(e)}),console.log("Invalidated client cache patterns: ".concat(r.join(", ")))}catch(e){throw console.error("更新分类排序失败:",e),Error("更新分类排序失败")}}async function w(e,t){try{let{data:{user:r}}=await f.auth.getUser();if(!r)throw Error("用户未登录");let a=f.from("categories").select("id").eq("user_id",r.id).eq("name",e).is("deleted_at",null);t&&(a=a.neq("id",t));let{data:o,error:s}=await a;if(s)throw s;return o.length>0}catch(e){return console.error("检查分类名称失败:",e),!1}}async function y(e){try{let{count:t,error:r}=await f.from("prompts").select("*",{count:"exact",head:!0}).eq("category_id",e).is("deleted_at",null);if(r)throw r;return t||0}catch(e){return console.error("获取分类提示词数量失败:",e),0}}let x=(0,a.U)();async function v(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{query:t,categoryId:r,tagIds:a=[],sortBy:o="updated_at",sortOrder:s="desc",limit:n=12,offset:i=0}=e;return!t&&0===i&&n<=12?m(h.PROMPTS,u.MEDIUM,()=>b(e),e):b(e)}async function b(e){try{let{query:t,categoryId:r,tagIds:a=[],sortBy:o="updated_at",sortOrder:s="desc",limit:n=12,offset:i=0}=e,c=x.from("prompts").select("\n        *,\n        category:categories(*),\n        prompt_tags(\n          tag:tags(*)\n        )\n      ").is("deleted_at",null);r&&(c=c.eq("category_id",r)),a.length>0&&(c=c.in("id",x.from("prompt_tags").select("prompt_id").in("tag_id",a))),t&&(c=c.or("title.ilike.%".concat(t,"%,description.ilike.%").concat(t,"%,content.ilike.%").concat(t,"%"))),c=(c=c.order(o,{ascending:"asc"===s})).range(i,i+n-1);let{data:l,error:d,count:h}=await c;if(d)throw d;let u=(null==l?void 0:l.map(e=>{var t;return{...e,tags:(null==(t=e.prompt_tags)?void 0:t.map(e=>e.tag).filter(Boolean))||[]}}))||[],m=x.from("prompts").select("*",{count:"exact",head:!0}).is("deleted_at",null);r&&(m=m.eq("category_id",r)),a.length>0&&(m=m.in("id",x.from("prompt_tags").select("prompt_id").in("tag_id",a))),t&&(m=m.or("title.ilike.%".concat(t,"%,description.ilike.%").concat(t,"%,content.ilike.%").concat(t,"%")));let{count:f}=await m;return{data:u,total:f||0,page:Math.floor(i/n)+1,pageSize:n,hasMore:i+n<(f||0)}}catch(e){throw console.error("获取提示词列表失败:",e),Error("获取提示词列表失败")}}async function _(e){try{var t;if(e.startsWith("local_"))return console.warn("尝试查询本地ID，跳过远程查询:",e),null;let{data:r,error:a}=await x.from("prompts").select("\n        *,\n        category:categories(*),\n        prompt_tags(\n          tag:tags(*)\n        )\n      ").eq("id",e).is("deleted_at",null).single();if(a){if("PGRST116"===a.code)return null;throw a}return{...r,tags:(null==(t=r.prompt_tags)?void 0:t.map(e=>e.tag).filter(Boolean))||[]}}catch(e){throw console.error("获取提示词失败:",e),Error("获取提示词失败")}}async function j(e,t,r){try{let{data:a,error:o}=await x.from("prompts").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(o)throw o;if(void 0!==r&&(await x.from("prompt_tags").delete().eq("prompt_id",e),r.length>0)){let{error:t}=await x.from("prompt_tags").insert(r.map(t=>({prompt_id:e,tag_id:t})));if(t)throw t}return await _(e)}catch(e){throw console.error("更新提示词失败:",e),Error("更新提示词失败")}}async function N(e){try{let{error:t}=await x.from("prompts").update({deleted_at:new Date().toISOString(),updated_at:new Date().toISOString()}).eq("id",e);if(t)throw t}catch(e){throw console.error("删除提示词失败:",e),Error("删除提示词失败")}}async function k(e){try{let{error:t}=await x.rpc("increment_prompt_usage",{prompt_id:e});if(t)throw t}catch(e){console.error("更新使用次数失败:",e)}}async function S(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{let{data:t,error:r}=await x.from("prompts").select("\n        *,\n        category:categories(*),\n        prompt_tags(\n          tag:tags(*)\n        )\n      ").is("deleted_at",null).order("usage_count",{ascending:!1}).limit(e);if(r)throw r;return(null==t?void 0:t.map(e=>{var t;return{...e,tags:(null==(t=e.prompt_tags)?void 0:t.map(e=>e.tag).filter(Boolean))||[]}}))||[]}catch(e){throw console.error("获取热门提示词失败:",e),Error("获取热门提示词失败")}}let E=(0,a.U)();async function z(){return m("tags",u.LONG,async()=>{try{let{data:e,error:t}=await E.from("tags").select("*").order("name",{ascending:!0});if(t){if(console.error("Supabase 错误:",t),"PGRST116"===t.code||t.message.includes('relation "tags" does not exist'))return console.warn("tags 表不存在，返回空数组"),[];throw t}return e||[]}catch(e){return console.error("获取标签失败:",e),[]}})}async function I(e){try{let{data:{user:t}}=await E.auth.getUser();if(!t)throw Error("用户未登录");let{data:r,error:a}=await E.from("tags").insert({...e,user_id:t.id}).select().single();if(a)throw a;return r}catch(e){throw console.error("创建标签失败:",e),Error("创建标签失败")}}async function C(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{let{data:t,error:r}=await E.from("tags").select("\n        *,\n        prompt_tags(count)\n      ").order("name",{ascending:!0});if(r)throw r;return(null==t?void 0:t.map(e=>{var t;return{...e,usage_count:(null==(t=e.prompt_tags)?void 0:t.length)||0}}).sort((e,t)=>t.usage_count-e.usage_count).slice(0,e))||[]}catch(e){throw console.error("获取热门标签失败:",e),Error("获取热门标签失败")}}let q=(0,a.U)();async function M(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{let{data:t,error:r}=await q.from("search_history").select("*").order("last_searched_at",{ascending:!1}).limit(e);if(r)throw r;return t||[]}catch(e){throw console.error("获取搜索历史失败:",e),Error("获取搜索历史失败")}}async function O(e){try{let{data:{user:t}}=await q.auth.getUser();if(!t)throw Error("用户未登录");let{data:r,error:a}=await q.from("search_history").select("*").eq("user_id",t.id).eq("search_term",e).single();if(a&&"PGRST116"!==a.code)throw a;if(r){let{error:e}=await q.from("search_history").update({search_count:r.search_count+1,last_searched_at:new Date().toISOString()}).eq("id",r.id);if(e)throw e}else{let{error:r}=await q.from("search_history").insert({search_term:e,search_count:1,user_id:t.id});if(r)throw r}}catch(e){console.error("添加搜索历史失败:",e)}}async function A(){try{let{data:{user:e}}=await q.auth.getUser();if(!e)throw Error("用户未登录");let{error:t}=await q.from("search_history").delete().eq("user_id",e.id);if(t)throw t}catch(e){throw console.error("清除搜索历史失败:",e),Error("清除搜索历史失败")}}async function R(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{let{data:t,error:r}=await q.from("search_history").select("*").order("search_count",{ascending:!1}).limit(e);if(r)throw r;return t||[]}catch(e){throw console.error("获取热门搜索词失败:",e),Error("获取热门搜索词失败")}}async function U(e){let t="search:".concat(JSON.stringify(e));return!e.query||e.query.length<3?c(t,i.MEDIUM,()=>D(e)):D(e)}async function D(e){try{let{data:{user:t}}=await q.auth.getUser();if(!t)throw Error("用户未登录");let{query:r,title:a,content:o,categoryId:s,tagIds:n=[],dateFrom:i,dateTo:c,usageCountMin:l,usageCountMax:d,sortBy:h="updated_at",sortOrder:u="desc",limit:m=20,offset:f=0}=e,g=q.from("prompts").select("\n        *,\n        category:categories(*),\n        prompt_tags(\n          tag:tags(*)\n        )\n      ").eq("user_id",t.id).is("deleted_at",null);r&&(g=g.or("title.ilike.%".concat(r,"%,description.ilike.%").concat(r,"%,content.ilike.%").concat(r,"%"))),a&&(g=g.ilike("title","%".concat(a,"%"))),o&&(g=g.ilike("content","%".concat(o,"%"))),s&&(g=g.eq("category_id",s)),n.length>0&&(g=g.in("id",q.from("prompt_tags").select("prompt_id").in("tag_id",n))),i&&(g=g.gte("created_at",i)),c&&(g=g.lte("created_at",c)),void 0!==l&&(g=g.gte("usage_count",l)),void 0!==d&&(g=g.lte("usage_count",d)),g=g.order(h,{ascending:"asc"===u}).range(f,f+m-1);let{data:p,error:w}=await g;if(w)throw w;return(null==p?void 0:p.map(e=>{var t;return{...e,tags:(null==(t=e.prompt_tags)?void 0:t.map(e=>e.tag).filter(Boolean))||[]}}))||[]}catch(e){throw console.error("高级搜索失败:",e),Error("高级搜索失败")}}let T=(0,a.U)();async function G(){try{let{data:{user:e}}=await T.auth.getUser();if(!e)throw Error("用户未登录");let t={user_id:e.id,theme:"system",items_per_page:12,show_usage_count:!0,auto_copy_feedback:!0},{data:r,error:a}=await T.from("user_preferences").insert(t).select().single();if(a)throw a;return r}catch(e){throw console.error("创建默认用户偏好设置失败:",e),Error("创建默认用户偏好设置失败")}}async function P(){try{let{data:{user:e}}=await T.auth.getUser();if(!e)throw Error("用户未登录");let{data:t}=await T.from("user_preferences").select("user_id").eq("user_id",e.id).single();if(!t){await G();let{error:e}=await T.rpc("create_default_categories_for_user");e&&console.error("创建默认分类失败:",e)}}catch(e){console.error("初始化用户数据失败:",e)}}},46414:(e,t,r)=>{r.d(t,{U:()=>o});var a=r(43865);function o(){return(0,a.createBrowserClient)("https://vigxjamjjlxzmuzwxwyl.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZpZ3hqYW1qamx4em11end4d3lsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Nzc4NTAsImV4cCI6MjA2OTA1Mzg1MH0.re45eDFz2pz7Tswcx5sE1bWuCP7MH481XHgsecj578E")}},53999:(e,t,r)=>{r.d(t,{cn:()=>s});var a=r(52596),o=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,a.$)(t))}},58607:(e,t,r)=>{r.d(t,{AuthButton:()=>h});var a=r(95155),o=r(6874),s=r.n(o),n=r(97168),i=r(46414),c=r(35695);function l(){let e=(0,c.useRouter)(),t=async()=>{let t=(0,i.U)();await t.auth.signOut(),e.push("/auth/login")};return(0,a.jsx)(n.$,{onClick:t,children:"Logout"})}var d=r(12115);function h(){let[e,t]=(0,d.useState)(null),[r,o]=(0,d.useState)(!0),c=(0,i.U)();return((0,d.useEffect)(()=>{(async()=>{let{data:{user:e}}=await c.auth.getUser();t(e),o(!1)})();let{data:{subscription:e}}=c.auth.onAuthStateChange((e,r)=>{var a;t(null!=(a=null==r?void 0:r.user)?a:null),o(!1)});return()=>e.unsubscribe()},[c.auth]),r)?(0,a.jsx)("div",{className:"h-8 w-20 bg-gray-200 animate-pulse rounded"}):e?(0,a.jsxs)("div",{className:"flex items-center gap-4",children:["Hey, ",e.email,"!",(0,a.jsx)(l,{})]}):(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{asChild:!0,size:"sm",variant:"outline",children:(0,a.jsx)(s(),{href:"/auth/login",children:"Sign in"})}),(0,a.jsx)(n.$,{asChild:!0,size:"sm",variant:"default",children:(0,a.jsx)(s(),{href:"/auth/sign-up",children:"Sign up"})})]})}},81704:(e,t,r)=>{r.d(t,{Icon:()=>l});var a=r(95155),o=r(36505),s=r(4181),n=r(3578);s.Yv.add(n.ao0,n.Uj9,n.jTw,n.LFz,n.e4L,n.KMJ,n.eST,n.MjD,n.QLR,n.jPR,n.MT7,n.yLS,n.pS3,n.DX_,n.qcK,n.t5Z,n.cbP,n.JmV,n.yy,n.G06,n.mRM,n.YpS,n.TJL,n.l9V,n.ITF,n.dB,n.X46,n.GxD,n.v02,n.Jt$,n.w2A,n.Wzs,n.XkK,n.GRI,n.e68,n.zpE,n.iW_,n.wRm,n.ckx,n.vaG,n.z1G,n.Vpu,n.hSh,n.U23,n.yek,n.oZK,n.gr3,n.AaJ,n.KTq,n.Iae,n.h8M,n.ruc,n.vZS,n.Cyq,n.n2W,n.XaT,n.A4h,n.okg,n.a$,n.CYF,n.Hzw,n.CQO,n.Bwz,n.DW4,n.KKb,n.V2x,n.hem,n.D6w,n.jBL,n.ArK,n.GrJ,n.w7B,n.YBv,n.fyG,n._eQ,n.nET,n.rC2,n.p1w,n.zm_,n.kNw,n.R70,n.zqi,n.iHh,n.B9e,n.LPI,n.pvD,n.s6x,n.Pcr,n.Q9Y,n.TBz,n.e5w,n.$Fj);let i={folder:"folder","folder-open":"folder-open",code:"code","pen-to-square":"pen-to-square",bullhorn:"bullhorn",rocket:"rocket","graduation-cap":"graduation-cap",search:"search",plus:"plus",copy:"copy",edit:"edit",trash:"trash",eye:"eye",tags:"tags",heart:"heart",share:"share",download:"download",upload:"upload",star:"star",bookmark:"bookmark",filter:"filter","sort-amount-down":"sort-amount-down","sort-amount-up":"sort-amount-up",grid:"grid",list:"list",cog:"cog",user:"user","sign-out-alt":"sign-out-alt",home:"home","chevron-down":"chevron-down","chevron-up":"chevron-up","chevron-left":"chevron-left","chevron-right":"chevron-right",times:"times",check:"check","exclamation-triangle":"exclamation-triangle","info-circle":"info-circle","question-circle":"question-circle",bars:"bars","ellipsis-v":"ellipsis-v",spinner:"spinner",refresh:"refresh",save:"save",undo:"undo",redo:"redo",expand:"expand",compress:"compress","external-link-alt":"external-link-alt",clipboard:"clipboard","clipboard-check":"clipboard-check",markdown:"markdown","file-code":"file-code","file-text":"file-text",image:"image",video:"video",music:"music",file:"file",calendar:"calendar",clock:"clock",hashtag:"hashtag",at:"at",link:"link",globe:"globe",lock:"lock",unlock:"unlock",shield:"shield",database:"database",server:"server",cloud:"cloud",desktop:"desktop",mobile:"mobile",tablet:"tablet",laptop:"laptop",palette:"palette","paint-brush":"paint-brush",magic:"magic",lightbulb:"lightbulb",flash:"flash",bolt:"bolt",fire:"fire",gem:"gem",crown:"crown",trophy:"trophy",medal:"medal",award:"award",bullseye:"bullseye",flag:"flag","map-marker":"map-marker",compass:"compass",route:"route",map:"map",chart:"chart-bar"};var c=r(53999);function l(e){let{name:t,className:r,size:s,spin:n=!1,pulse:l=!1,color:d}=e,h=t&&i[t]?t:"question-circle";return(0,a.jsx)(o.g,{icon:i[h],className:(0,c.cn)(r),size:s,spin:n,pulse:l,color:d})}},88145:(e,t,r)=>{r.d(t,{E:()=>i});var a=r(95155);r(12115);var o=r(74466),s=r(53999);let n=(0,o.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...o}=e;return(0,a.jsx)("div",{className:(0,s.cn)(n({variant:r}),t),...o})}},92669:(e,t,r)=>{r.d(t,{a:()=>h});var a=r(95155),o=r(35695),s=r(6874),n=r.n(s),i=r(97168),c=r(81704),l=r(58607),d=r(18064);function h(e){let{onCreatePrompt:t,children:r}=e,s=(0,o.usePathname)(),h=[{href:"/dashboard",label:"提示词",icon:"home",active:"/dashboard"===s},{href:"/dashboard/search",label:"搜索",icon:"search",active:"/dashboard/search"===s},{href:"/dashboard/categories",label:"分类管理",icon:"folder",active:"/dashboard/categories"===s},{href:"/dashboard/stats",label:"数据统计",icon:"chart",active:"/dashboard/stats"===s}];return(0,a.jsx)("header",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center gap-8",children:[(0,a.jsxs)(n(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg",children:(0,a.jsx)(c.Icon,{name:"lightbulb",className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"提示词管理"})]}),(0,a.jsx)("nav",{className:"hidden md:flex items-center gap-1",children:h.map(e=>(0,a.jsx)(n(),{href:e.href,children:(0,a.jsxs)(i.$,{variant:e.active?"secondary":"ghost",size:"sm",className:"gap-2",children:[(0,a.jsx)(c.Icon,{name:e.icon,className:"h-4 w-4"}),e.label]})},e.href))})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t&&(0,a.jsxs)(i.$,{variant:"default",size:"sm",onClick:t,className:"hidden sm:flex bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200",children:[(0,a.jsx)(c.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),"新建提示词"]}),r,(0,a.jsx)(d.ThemeSwitcher,{}),(0,a.jsx)(l.AuthButton,{})]})]}),(0,a.jsx)("div",{className:"md:hidden border-t border-gray-200 dark:border-gray-700",children:(0,a.jsx)("nav",{className:"flex items-center gap-1 py-2",children:h.map(e=>(0,a.jsx)(n(),{href:e.href,className:"flex-1",children:(0,a.jsxs)(i.$,{variant:e.active?"secondary":"ghost",size:"sm",className:"w-full gap-2",children:[(0,a.jsx)(c.Icon,{name:e.icon,className:"h-4 w-4"}),e.label]})},e.href))})})]})})}},97168:(e,t,r)=>{r.d(t,{$:()=>l});var a=r(95155),o=r(12115),s=r(99708),n=r(74466),i=r(53999);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=o.forwardRef((e,t)=>{let{className:r,variant:o,size:n,asChild:l=!1,...d}=e,h=l?s.DX:"button";return(0,a.jsx)(h,{className:(0,i.cn)(c({variant:o,size:n,className:r})),ref:t,...d})});l.displayName="Button"}}]);