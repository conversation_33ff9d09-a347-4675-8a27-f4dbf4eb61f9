import { createClient } from '@/lib/supabase/client'
import { withCache, CACHE_KEYS, CACHE_TTL, invalidateCache } from '@/lib/cache'
import { withClientCache, CLIENT_CACHE_KEYS, CLIENT_CACHE_TTL, invalidateClientCache } from '@/lib/client-cache'
import type {
  Prompt,
  PromptInsert,
  PromptUpdate,
  PromptWithDetails,
  SearchParams,
  PaginatedResponse,
  DatabaseError
} from '@/types/database'

const supabase = createClient()

/**
 * 获取提示词列表（支持搜索和分页）
 */
export async function getPrompts(params: SearchParams = {}): Promise<PaginatedResponse<PromptWithDetails>> {
  const {
    query,
    categoryId,
    tagIds = [],
    sortBy = 'updated_at',
    sortOrder = 'desc',
    limit = 12,
    offset = 0
  } = params

  // 只对非搜索查询进行缓存（搜索结果变化频繁）
  const shouldCache = !query && offset === 0 && limit <= 12

  if (shouldCache) {
    return withClientCache(
      CLIENT_CACHE_KEYS.PROMPTS,
      CLIENT_CACHE_TTL.MEDIUM,
      () => executeQuery(params),
      params
    )
  } else {
    return executeQuery(params)
  }
}

/**
 * 执行提示词查询的核心逻辑
 */
async function executeQuery(params: SearchParams): Promise<PaginatedResponse<PromptWithDetails>> {
  try {
    const {
      query,
      categoryId,
      tagIds = [],
      sortBy = 'updated_at',
      sortOrder = 'desc',
      limit = 12,
      offset = 0
    } = params

    let queryBuilder = supabase
      .from('prompts')
      .select(`
        *,
        category:categories(*),
        prompt_tags(
          tag:tags(*)
        )
      `)
      .is('deleted_at', null)

    // 分类筛选
    if (categoryId) {
      queryBuilder = queryBuilder.eq('category_id', categoryId)
    }

    // 标签筛选
    if (tagIds.length > 0) {
      queryBuilder = queryBuilder.in('id', 
        supabase
          .from('prompt_tags')
          .select('prompt_id')
          .in('tag_id', tagIds)
      )
    }

    // 文本搜索
    if (query) {
      queryBuilder = queryBuilder.or(`title.ilike.%${query}%,description.ilike.%${query}%,content.ilike.%${query}%`)
    }

    // 排序
    queryBuilder = queryBuilder.order(sortBy, { ascending: sortOrder === 'asc' })

    // 分页
    queryBuilder = queryBuilder.range(offset, offset + limit - 1)

    const { data, error, count } = await queryBuilder

    if (error) throw error

    // 处理数据，展平标签
    const processedData = data?.map(item => ({
      ...item,
      tags: item.prompt_tags?.map(pt => pt.tag).filter(Boolean) || []
    })) || []

    // 获取总数
    let totalQuery = supabase
      .from('prompts')
      .select('*', { count: 'exact', head: true })
      .is('deleted_at', null)

    if (categoryId) {
      totalQuery = totalQuery.eq('category_id', categoryId)
    }

    if (tagIds.length > 0) {
      totalQuery = totalQuery.in('id', 
        supabase
          .from('prompt_tags')
          .select('prompt_id')
          .in('tag_id', tagIds)
      )
    }

    if (query) {
      totalQuery = totalQuery.or(`title.ilike.%${query}%,description.ilike.%${query}%,content.ilike.%${query}%`)
    }

    const { count: total } = await totalQuery

    return {
      data: processedData,
      total: total || 0,
      page: Math.floor(offset / limit) + 1,
      pageSize: limit,
      hasMore: (offset + limit) < (total || 0)
    }
  } catch (error) {
    console.error('获取提示词列表失败:', error)
    throw new Error('获取提示词列表失败')
  }
}

/**
 * 根据ID获取单个提示词
 */
export async function getPromptById(id: string): Promise<PromptWithDetails | null> {
  try {
    // 如果是本地ID，不要查询远程数据库
    if (id.startsWith('local_')) {
      console.warn('尝试查询本地ID，跳过远程查询:', id)
      return null
    }

    const { data, error } = await supabase
      .from('prompts')
      .select(`
        *,
        category:categories(*),
        prompt_tags(
          tag:tags(*)
        )
      `)
      .eq('id', id)
      .is('deleted_at', null)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // 未找到记录
      throw error
    }

    // 处理标签数据
    const processedData = {
      ...data,
      tags: data.prompt_tags?.map(pt => pt.tag).filter(Boolean) || []
    }

    return processedData
  } catch (error) {
    console.error('获取提示词失败:', error)
    throw new Error('获取提示词失败')
  }
}

/**
 * 创建新提示词
 */
export async function createPrompt(prompt: Omit<PromptInsert, 'user_id'>, tagIds: string[] = []): Promise<PromptWithDetails> {
  try {
    // 获取当前用户
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    // 创建提示词
    const { data: promptData, error: promptError } = await supabase
      .from('prompts')
      .insert({
        ...prompt,
        user_id: user.id
      })
      .select()
      .single()

    if (promptError) throw promptError

    // 关联标签
    if (tagIds.length > 0) {
      const { error: tagError } = await supabase
        .from('prompt_tags')
        .insert(
          tagIds.map(tagId => ({
            prompt_id: promptData.id,
            tag_id: tagId
          }))
        )

      if (tagError) throw tagError
    }

    // 返回完整数据
    return await getPromptById(promptData.id) as PromptWithDetails
  } catch (error) {
    console.error('创建提示词失败:', error)
    throw new Error('创建提示词失败')
  }
}

/**
 * 更新提示词
 */
export async function updatePrompt(id: string, updates: PromptUpdate, tagIds?: string[]): Promise<PromptWithDetails> {
  try {
    // 更新提示词基本信息
    const { data: promptData, error: promptError } = await supabase
      .from('prompts')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (promptError) throw promptError

    // 更新标签关联（如果提供了标签ID）
    if (tagIds !== undefined) {
      // 删除现有关联
      await supabase
        .from('prompt_tags')
        .delete()
        .eq('prompt_id', id)

      // 添加新关联
      if (tagIds.length > 0) {
        const { error: tagError } = await supabase
          .from('prompt_tags')
          .insert(
            tagIds.map(tagId => ({
              prompt_id: id,
              tag_id: tagId
            }))
          )

        if (tagError) throw tagError
      }
    }

    // 返回完整数据
    return await getPromptById(id) as PromptWithDetails
  } catch (error) {
    console.error('更新提示词失败:', error)
    throw new Error('更新提示词失败')
  }
}

/**
 * 软删除提示词
 */
export async function deletePrompt(id: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('prompts')
      .update({ 
        deleted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)

    if (error) throw error
  } catch (error) {
    console.error('删除提示词失败:', error)
    throw new Error('删除提示词失败')
  }
}

/**
 * 增加提示词使用次数
 */
export async function incrementPromptUsage(id: string): Promise<void> {
  try {
    // 使用数据库函数来增加使用次数
    const { error } = await supabase.rpc('increment_prompt_usage', {
      prompt_id: id
    })

    if (error) throw error
  } catch (error) {
    console.error('更新使用次数失败:', error)
    // 这个错误不应该阻止复制操作，所以只记录日志
  }
}

/**
 * 批量导入提示词
 */
export async function importPrompts(prompts: Array<Omit<PromptInsert, 'user_id'>>): Promise<Prompt[]> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { data, error } = await supabase
      .from('prompts')
      .insert(
        prompts.map(prompt => ({
          ...prompt,
          user_id: user.id
        }))
      )
      .select()

    if (error) throw error

    return data
  } catch (error) {
    console.error('批量导入提示词失败:', error)
    throw new Error('批量导入提示词失败')
  }
}

/**
 * 获取热门提示词（按使用次数排序）
 */
export async function getPopularPrompts(limit: number = 10): Promise<PromptWithDetails[]> {
  try {
    const { data, error } = await supabase
      .from('prompts')
      .select(`
        *,
        category:categories(*),
        prompt_tags(
          tag:tags(*)
        )
      `)
      .is('deleted_at', null)
      .order('usage_count', { ascending: false })
      .limit(limit)

    if (error) throw error

    // 处理数据，展平标签
    const processedData = data?.map(item => ({
      ...item,
      tags: item.prompt_tags?.map(pt => pt.tag).filter(Boolean) || []
    })) || []

    return processedData
  } catch (error) {
    console.error('获取热门提示词失败:', error)
    throw new Error('获取热门提示词失败')
  }
}
