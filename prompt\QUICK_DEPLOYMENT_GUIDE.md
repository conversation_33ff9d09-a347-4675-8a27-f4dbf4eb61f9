# ⚡ 快速部署指南

无需手动检查！使用自动化脚本快速验证部署准备状态。

## 🚀 一键检查

### 方法一：快速检查（推荐）
```bash
npm run quick-check
```
检查最关键的部署准备项目，30秒内完成。

### 方法二：完整检查
```bash
npm run check-deployment
```
全面检查所有项目，包括代码质量和构建测试。

### 方法三：预部署检查
```bash
npm run pre-deploy
```
运行类型检查 + 代码检查 + 快速检查的组合。

## 📋 自动化检查项目

### ✅ 快速检查包含
- [x] 📦 package.json 配置
- [x] ⚙️ Next.js 配置文件
- [x] 🚀 Vercel 部署配置
- [x] 🔧 环境变量模板
- [x] 📚 项目文档
- [x] 🗄️ 数据库脚本
- [x] 📁 目录结构
- [x] 🔒 安全检查

### 🔍 完整检查额外包含
- [x] 🧪 TypeScript 编译检查
- [x] 📏 ESLint 代码质量检查
- [x] 🏗️ 项目构建测试
- [x] 📝 Git 状态检查
- [x] 📊 依赖关系验证

## 🎯 检查结果说明

### ✅ 全部通过
```
🎉 恭喜！项目已准备好部署到 Vercel！

下一步：
1. 创建 Supabase 项目
2. 配置环境变量
3. 执行数据库脚本
4. 部署到 Vercel
```

### ⚠️ 有警告
警告不会阻止部署，但建议修复：
- `.env.local` 文件存在（确保未提交到 Git）
- 某些可选配置缺失

### ❌ 有错误
必须修复后才能部署：
- 缺少必需文件
- 代码编译错误
- 配置文件错误

## 🚀 部署步骤

### 1. 运行检查
```bash
npm run quick-check
```

### 2. 如果检查通过，开始部署

#### 方法一：一键部署（推荐）
点击 README.md 中的部署按钮：
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/prompt-management-tool)

#### 方法二：手动部署
1. **Fork 项目到您的 GitHub**
2. **连接 Vercel**
   - 访问 [Vercel Dashboard](https://vercel.com/dashboard)
   - 点击 "New Project"
   - 选择您的 GitHub 仓库

3. **配置环境变量**
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-anon-key
   ```

4. **部署**
   - 点击 "Deploy" 按钮
   - 等待构建完成

### 3. 配置 Supabase

#### 创建项目
1. 访问 [Supabase Dashboard](https://app.supabase.com/)
2. 点击 "New Project"
3. 填写项目信息

#### 执行数据库脚本
在 Supabase SQL Editor 中依次执行：
1. `database/production/01-schema.sql`
2. `database/production/02-seed.sql`

#### 获取配置信息
- 项目 URL：`https://your-project.supabase.co`
- Anon Key：在 Settings > API 中找到

### 4. 验证部署
访问您的 Vercel 应用 URL，测试：
- [x] 用户注册/登录
- [x] 创建提示词
- [x] 搜索功能
- [x] 分类管理

## 🔧 常见问题

### Q: 检查脚本报错怎么办？
A: 根据错误信息修复问题，常见问题：
- 缺少依赖：运行 `npm install`
- TypeScript 错误：检查类型定义
- 文件缺失：确保所有必需文件存在

### Q: 构建失败怎么办？
A: 检查以下项目：
- 环境变量是否正确配置
- 依赖是否完整安装
- 代码是否有语法错误

### Q: Supabase 连接失败？
A: 确认：
- Supabase 项目状态正常
- URL 和 Key 配置正确
- 数据库脚本已执行

### Q: 部署后功能异常？
A: 检查：
- 环境变量在 Vercel 中正确配置
- 数据库 RLS 策略已启用
- 认证重定向 URL 已配置

## 📞 获取帮助

如果遇到问题：
1. 查看 `docs/deployment/vercel-deployment.md` 详细指南
2. 检查 `DEPLOYMENT_CHECKLIST.md` 完整清单
3. 提交 GitHub Issue 获取支持

---

## 🎊 总结

使用自动化检查脚本，您可以：
- ⚡ **30秒内**验证部署准备状态
- 🎯 **自动发现**潜在问题
- 🚀 **快速部署**到生产环境
- 📊 **可视化**检查结果

**不再需要手动逐项检查！** 让脚本为您完成繁琐的验证工作。
