#!/usr/bin/env node

/**
 * 快速部署检查脚本
 * 检查最关键的部署前准备项目
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

console.log(`${colors.bold}${colors.blue}⚡ 快速部署检查${colors.reset}\n`);

const checks = [
  {
    name: '📦 package.json',
    check: () => fs.existsSync('package.json'),
    required: true
  },
  {
    name: '⚙️  next.config.ts',
    check: () => fs.existsSync('next.config.ts'),
    required: true
  },
  {
    name: '🚀 vercel.json',
    check: () => fs.existsSync('vercel.json'),
    required: true
  },
  {
    name: '🔧 .env.example',
    check: () => fs.existsSync('.env.example'),
    required: true
  },
  {
    name: '📚 README.md',
    check: () => fs.existsSync('README.md'),
    required: true
  },
  {
    name: '🗄️  数据库架构脚本',
    check: () => fs.existsSync('database/production/01-schema.sql'),
    required: true
  },
  {
    name: '🌱 数据库种子脚本',
    check: () => fs.existsSync('database/production/02-seed.sql'),
    required: true
  },
  {
    name: '📁 app/ 目录',
    check: () => fs.existsSync('app') && fs.statSync('app').isDirectory(),
    required: true
  },
  {
    name: '🧩 components/ 目录',
    check: () => fs.existsSync('components') && fs.statSync('components').isDirectory(),
    required: true
  },
  {
    name: '📖 docs/ 目录',
    check: () => fs.existsSync('docs') && fs.statSync('docs').isDirectory(),
    required: true
  },
  {
    name: '🔒 .env.local 未提交',
    check: () => !fs.existsSync('.env.local'),
    required: false,
    warning: '.env.local 文件存在，请确保未提交到 Git'
  },
  {
    name: '📝 TypeScript 配置',
    check: () => fs.existsSync('tsconfig.json'),
    required: true
  }
];

let passed = 0;
let failed = 0;
let warnings = 0;

checks.forEach(({ name, check, required, warning }) => {
  const result = check();
  
  if (result) {
    console.log(`${colors.green}✅ ${name}${colors.reset}`);
    passed++;
  } else {
    if (required) {
      console.log(`${colors.red}❌ ${name}${colors.reset}`);
      failed++;
    } else {
      console.log(`${colors.yellow}⚠️  ${name}${colors.reset}`);
      if (warning) {
        console.log(`   ${colors.yellow}${warning}${colors.reset}`);
      }
      warnings++;
    }
  }
});

console.log('\n' + '='.repeat(50));
console.log(`${colors.bold}检查结果:${colors.reset}`);
console.log(`${colors.green}✅ 通过: ${passed}${colors.reset}`);
if (warnings > 0) {
  console.log(`${colors.yellow}⚠️  警告: ${warnings}${colors.reset}`);
}
if (failed > 0) {
  console.log(`${colors.red}❌ 失败: ${failed}${colors.reset}`);
}

if (failed === 0) {
  console.log(`\n${colors.green}${colors.bold}🎉 基础检查通过！项目可以部署！${colors.reset}`);
  console.log(`\n${colors.blue}下一步操作：${colors.reset}`);
  console.log('1. 创建 Supabase 项目');
  console.log('2. 执行数据库脚本');
  console.log('3. 配置环境变量');
  console.log('4. 部署到 Vercel');
  console.log(`\n${colors.blue}详细部署指南：${colors.reset} docs/deployment/vercel-deployment.md`);
} else {
  console.log(`\n${colors.red}${colors.bold}❌ 请先修复上述问题${colors.reset}`);
}
