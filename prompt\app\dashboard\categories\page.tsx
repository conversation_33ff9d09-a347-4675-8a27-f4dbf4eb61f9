"use client"

import { useState, useEffect } from 'react'
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { CategoryFormModal } from '@/components/category-form-modal'
import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog'
import { SortableCategoryItem } from '@/components/sortable-category-item'
import { DashboardHeader } from '@/components/dashboard-header'
import { useToast } from '@/hooks/use-toast'
import {
  getCategories,
  deleteCategory,
  updateCategoriesOrder,
  getCategoryPromptCount
} from '@/lib/database'
import { localFirstStore } from '@/lib/local-first-store'
import type { CategoryWithCount } from '@/types/database'

export default function CategoriesPage() {
  const [categories, setCategories] = useState<CategoryWithCount[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isFormModalOpen, setIsFormModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<CategoryWithCount | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deletingCategory, setDeletingCategory] = useState<CategoryWithCount | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const { toast } = useToast()

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      setIsLoading(true)
      console.log('🚀 使用本地优先存储加载分类')
      // 使用本地优先存储
      const data = await localFirstStore.getCategories()
      setCategories(data)
      console.log(`✅ 加载了 ${data.length} 个分类`)
    } catch (error) {
      console.error('加载分类失败:', error)
      toast({
        title: "加载失败",
        description: "无法加载分类列表",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateCategory = () => {
    setEditingCategory(null)
    setIsFormModalOpen(true)
  }

  const handleEditCategory = (category: CategoryWithCount) => {
    setEditingCategory(category)
    setIsFormModalOpen(true)
  }

  const handleDeleteCategory = async (category: CategoryWithCount) => {
    // 检查分类是否有提示词
    try {
      const promptCount = await getCategoryPromptCount(category.id)
      if (promptCount > 0) {
        toast({
          title: "无法删除",
          description: `该分类下还有 ${promptCount} 个提示词，请先移动或删除这些提示词`,
          variant: "destructive",
        })
        return
      }
    } catch (error) {
      console.error('检查分类提示词数量失败:', error)
    }

    setDeletingCategory(category)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!deletingCategory) return

    try {
      setIsDeleting(true)
      console.log('🚀 使用本地优先删除分类')

      // 使用本地优先删除
      const success = await localFirstStore.deleteCategory(deletingCategory.id)

      if (success) {
        // 立即从本地状态中移除
        setCategories(prev => prev.filter(c => c.id !== deletingCategory.id))

        toast({
          title: "删除成功",
          description: "分类已成功删除",
        })

        setIsDeleteDialogOpen(false)
        setDeletingCategory(null)
        console.log('✅ 分类已从本地状态中移除')

        // 输出同步状态用于调试
        const syncStatus = localFirstStore.getDetailedSyncStatus()
        console.log('🔍 删除后同步状态:', syncStatus)
      } else {
        throw new Error('删除失败')
      }
    } catch (error) {
      console.error('删除分类失败:', error)
      toast({
        title: "删除失败",
        description: "删除分类时出现错误",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleFormSuccess = () => {
    loadCategories()
  }

  const handleDragEnd = async (event: any) => {
    const { active, over } = event

    if (active.id !== over?.id) {
      const oldIndex = categories.findIndex(item => item.id === active.id)
      const newIndex = categories.findIndex(item => item.id === over.id)
      
      const newCategories = arrayMove(categories, oldIndex, newIndex)
      setCategories(newCategories)

      try {
        // 更新排序
        const categoryIds = newCategories.map(cat => cat.id)
        await updateCategoriesOrder(categoryIds)
        
        toast({
          title: "排序已更新",
          description: "分类排序已成功保存",
        })
      } catch (error) {
        console.error('更新分类排序失败:', error)
        // 恢复原始顺序
        setCategories(categories)
        toast({
          title: "排序失败",
          description: "更新分类排序时出现错误",
          variant: "destructive",
        })
      }
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Icon name="spinner" className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 顶部导航 */}
      <DashboardHeader />

      <div className="max-w-4xl mx-auto p-6">
        {/* 头部 */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
              分类管理
            </h1>
            <p className="text-muted-foreground mt-2">
              管理您的提示词分类，支持拖拽排序
            </p>
          </div>
          <Button onClick={handleCreateCategory} className="w-full sm:w-auto">
            <Icon name="plus" className="h-4 w-4 mr-2" />
            新建分类
          </Button>
        </div>

        {/* 分类列表 */}
        {categories.length > 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">
                  分类列表 ({categories.length})
                </h2>
                <div className="text-sm text-muted-foreground">
                  拖拽可调整排序
                </div>
              </div>

              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
                modifiers={[restrictToVerticalAxis]}
              >
                <SortableContext
                  items={categories.map(cat => cat.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <SortableCategoryItem
                        key={category.id}
                        category={category}
                        onEdit={handleEditCategory}
                        onDelete={handleDeleteCategory}
                      />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <Icon name="folder" className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              暂无分类
            </h3>
            <p className="text-muted-foreground mb-4">
              创建您的第一个分类来组织提示词
            </p>
            <Button onClick={handleCreateCategory}>
              <Icon name="plus" className="h-4 w-4 mr-2" />
              创建分类
            </Button>
          </div>
        )}

        {/* 使用说明 */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Icon name="info-circle" className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                使用说明
              </h3>
              <ul className="text-sm text-blue-700 dark:text-blue-200 space-y-1">
                <li>• 拖拽分类项可以调整显示顺序</li>
                <li>• 删除分类前需要先移动或删除该分类下的所有提示词</li>
                <li>• 分类颜色和图标会在侧边栏中显示</li>
                <li>• 分类名称在同一用户下必须唯一</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* 模态框 */}
      <CategoryFormModal
        category={editingCategory}
        isOpen={isFormModalOpen}
        onClose={() => {
          setIsFormModalOpen(false)
          setEditingCategory(null)
        }}
        onSuccess={handleFormSuccess}
      />

      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => {
          setIsDeleteDialogOpen(false)
          setDeletingCategory(null)
        }}
        onConfirm={handleDeleteConfirm}
        title="删除分类"
        description="此操作无法撤销，确定要删除这个分类吗？"
        itemName={deletingCategory?.name}
        isLoading={isDeleting}
      />
    </div>
  )
}
