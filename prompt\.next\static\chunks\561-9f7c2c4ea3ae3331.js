"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[561],{19178:(e,t,r)=>{r.d(t,{lg:()=>y,qW:()=>h,bL:()=>v});var n,s=r(12115),i=r(85185),l=r(63655),o=r(6101),a=r(39033),u=r(95155),c="dismissableLayer.update",d=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=s.forwardRef((e,t)=>{var r,h;let{disableOutsidePointerEvents:f=!1,onEscapeKeyDown:v,onPointerDownOutside:y,onFocusOutside:E,onInteractOutside:b,onDismiss:w,...g}=e,C=s.useContext(d),[k,x]=s.useState(null),A=null!=(h=null==k?void 0:k.ownerDocument)?h:null==(r=globalThis)?void 0:r.document,[,L]=s.useState({}),S=(0,o.s)(t,e=>x(e)),T=Array.from(C.layers),[R]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),O=T.indexOf(R),P=k?T.indexOf(k):-1,z=C.layersWithOutsidePointerEventsDisabled.size>0,D=P>=O,M=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,a.c)(e),i=s.useRef(!1),l=s.useRef(()=>{});return s.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){p("dismissableLayer.pointerDownOutside",n,s,{discrete:!0})},s={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",l.current),l.current=t,r.addEventListener("click",l.current,{once:!0})):t()}else r.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",l.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...C.branches].some(e=>e.contains(t));D&&!r&&(null==y||y(e),null==b||b(e),e.defaultPrevented||null==w||w())},A),N=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,a.c)(e),i=s.useRef(!1);return s.useEffect(()=>{let e=e=>{e.target&&!i.current&&p("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(null==E||E(e),null==b||b(e),e.defaultPrevented||null==w||w())},A);return!function(e,t=globalThis?.document){let r=(0,a.c)(e);s.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{P===C.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},A),s.useEffect(()=>{if(k)return f&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(n=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(k)),C.layers.add(k),m(),()=>{f&&1===C.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=n)}},[k,A,f,C]),s.useEffect(()=>()=>{k&&(C.layers.delete(k),C.layersWithOutsidePointerEventsDisabled.delete(k),m())},[k,C]),s.useEffect(()=>{let e=()=>L({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(l.sG.div,{...g,ref:S,style:{pointerEvents:z?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,M.onPointerDownCapture)})});h.displayName="DismissableLayer";var f=s.forwardRef((e,t)=>{let r=s.useContext(d),n=s.useRef(null),i=(0,o.s)(t,n);return s.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(l.sG.div,{...e,ref:i})});function m(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,r,n){let{discrete:s}=n,i=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),s?(0,l.hO)(i,o):i.dispatchEvent(o)}f.displayName="DismissableLayerBranch";var v=h,y=f},34378:(e,t,r)=>{r.d(t,{Z:()=>a});var n=r(12115),s=r(47650),i=r(63655),l=r(52712),o=r(95155),a=n.forwardRef((e,t)=>{var r,a;let{container:u,...c}=e,[d,h]=n.useState(!1);(0,l.N)(()=>h(!0),[]);let f=u||d&&(null==(a=globalThis)||null==(r=a.document)?void 0:r.body);return f?s.createPortal((0,o.jsx)(i.sG.div,{...c,ref:t}),f):null});a.displayName="Portal"},37328:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function s(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function i(e,t,r){var s=n(e,t,"set");if(s.set)s.set.call(e,r);else{if(!s.writable)throw TypeError("attempted to set read only private field");s.value=r}return r}r.d(t,{N:()=>h});var l,o=r(12115),a=r(46081),u=r(6101),c=r(99708),d=r(95155);function h(e){let t=e+"CollectionProvider",[r,n]=(0,a.A)(t),[s,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,n=o.useRef(null),i=o.useRef(new Map).current;return(0,d.jsx)(s,{scope:t,itemMap:i,collectionRef:n,children:r})};l.displayName=t;let h=e+"CollectionSlot",f=(0,c.TL)(h),m=o.forwardRef((e,t)=>{let{scope:r,children:n}=e,s=i(h,r),l=(0,u.s)(t,s.collectionRef);return(0,d.jsx)(f,{ref:l,children:n})});m.displayName=h;let p=e+"CollectionItemSlot",v="data-radix-collection-item",y=(0,c.TL)(p),E=o.forwardRef((e,t)=>{let{scope:r,children:n,...s}=e,l=o.useRef(null),a=(0,u.s)(t,l),c=i(p,r);return o.useEffect(()=>(c.itemMap.set(l,{ref:l,...s}),()=>void c.itemMap.delete(l))),(0,d.jsx)(y,{...{[v]:""},ref:a,children:n})});return E.displayName=p,[{Provider:l,Slot:m,ItemSlot:E},function(t){let r=i(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var f=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=p(t),s=n>=0?n:r+n;return s<0||s>=r?-1:s}(e,t);return -1===r?void 0:e[r]}function p(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap,class e extends Map{set(e,t){return f.get(this)&&(this.has(e)?s(this,l)[s(this,l).indexOf(e)]=e:s(this,l).push(e)),super.set(e,t),this}insert(e,t,r){let n,i=this.has(t),o=s(this,l).length,a=p(e),u=a>=0?a:o+a,c=u<0||u>=o?-1:u;if(c===this.size||i&&c===this.size-1||-1===c)return this.set(t,r),this;let d=this.size+ +!i;a<0&&u++;let h=[...s(this,l)],f=!1;for(let e=u;e<d;e++)if(u===e){let s=h[e];h[e]===t&&(s=h[e+1]),i&&this.delete(t),n=this.get(s),this.set(t,r)}else{f||h[e-1]!==t||(f=!0);let r=h[f?e:e-1],s=n;n=this.get(r),this.delete(r),this.set(r,s)}return this}with(t,r,n){let s=new e(this);return s.insert(t,r,n),s}before(e){let t=s(this,l).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,r){let n=s(this,l).indexOf(e);return -1===n?this:this.insert(n,t,r)}after(e){let t=s(this,l).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,r){let n=s(this,l).indexOf(e);return -1===n?this:this.insert(n+1,t,r)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return i(this,l,[]),super.clear()}delete(e){let t=super.delete(e);return t&&s(this,l).splice(s(this,l).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=m(s(this,l),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=m(s(this,l),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return s(this,l).indexOf(e)}keyAt(e){return m(s(this,l),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.at(n)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.keyAt(n)}find(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return n;r++}}findIndex(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return r;r++}return -1}filter(t,r){let n=[],s=0;for(let e of this)Reflect.apply(t,r,[e,s,this])&&n.push(e),s++;return new e(n)}map(t,r){let n=[],s=0;for(let e of this)n.push([e[0],Reflect.apply(t,r,[e,s,this])]),s++;return new e(n)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,s]=t,i=0,l=null!=s?s:this.at(0);for(let e of this)l=0===i&&1===t.length?e:Reflect.apply(n,this,[l,e,i,this]),i++;return l}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,s]=t,i=null!=s?s:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);i=e===this.size-1&&1===t.length?r:Reflect.apply(n,this,[i,r,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),n=this.get(r);t.set(r,n)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let s=[...this.entries()];return s.splice(...r),new e(s)}slice(t,r){let n=new e,s=this.size-1;if(void 0===t)return n;t<0&&(t+=this.size),void 0!==r&&r>0&&(s=r-1);for(let e=t;e<=s;e++){let t=this.keyAt(e),r=this.get(t);n.set(t,r)}return n}every(e,t){let r=0;for(let n of this){if(!Reflect.apply(e,t,[n,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return!0;r++}return!1}constructor(e){super(e),function(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}(this,l,{writable:!0,value:void 0}),i(this,l,[...super.keys()]),f.set(this,!0)}}},39033:(e,t,r)=>{r.d(t,{c:()=>s});var n=r(12115);function s(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},51362:(e,t,r)=>{r.d(t,{D:()=>u,ThemeProvider:()=>c});var n=r(12115),s=(e,t,r,n,s,i,l,o)=>{let a=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&i?s.map(e=>i[e]||e):s;r?(a.classList.remove(...n),a.classList.add(i&&i[t]?i[t]:t)):a.setAttribute(e,t)}),r=t,o&&u.includes(r)&&(a.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},i=["light","dark"],l="(prefers-color-scheme: dark)",o=n.createContext(void 0),a={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(o))?e:a},c=e=>n.useContext(o)?n.createElement(n.Fragment,null,e.children):n.createElement(h,{...e}),d=["light","dark"],h=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:s=!0,enableColorScheme:a=!0,storageKey:u="theme",themes:c=d,defaultTheme:h=s?"system":"light",attribute:y="data-theme",value:E,children:b,nonce:w,scriptProps:g}=e,[C,k]=n.useState(()=>m(u,h)),[x,A]=n.useState(()=>"system"===C?v():C),L=E?Object.values(E):c,S=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&s&&(t=v());let n=E?E[t]:t,l=r?p(w):null,o=document.documentElement,u=e=>{"class"===e?(o.classList.remove(...L),n&&o.classList.add(n)):e.startsWith("data-")&&(n?o.setAttribute(e,n):o.removeAttribute(e))};if(Array.isArray(y)?y.forEach(u):u(y),a){let e=i.includes(h)?h:null,r=i.includes(t)?t:e;o.style.colorScheme=r}null==l||l()},[w]),T=n.useCallback(e=>{let t="function"==typeof e?e(C):e;k(t);try{localStorage.setItem(u,t)}catch(e){}},[C]),R=n.useCallback(e=>{A(v(e)),"system"===C&&s&&!t&&S("system")},[C,t]);n.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(R),R(e),()=>e.removeListener(R)},[R]),n.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?k(e.newValue):T(h))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),n.useEffect(()=>{S(null!=t?t:C)},[t,C]);let O=n.useMemo(()=>({theme:C,setTheme:T,forcedTheme:t,resolvedTheme:"system"===C?x:C,themes:s?[...c,"system"]:c,systemTheme:s?x:void 0}),[C,T,t,x,s,c]);return n.createElement(o.Provider,{value:O},n.createElement(f,{forcedTheme:t,storageKey:u,attribute:y,enableSystem:s,enableColorScheme:a,defaultTheme:h,value:E,themes:c,nonce:w,scriptProps:g}),b)},f=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:i,enableSystem:l,enableColorScheme:o,defaultTheme:a,value:u,themes:c,nonce:d,scriptProps:h}=e,f=JSON.stringify([i,r,a,t,c,u,l,o]).slice(1,-1);return n.createElement("script",{...h,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(s.toString(),")(").concat(f,")")}})}),m=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},p=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")}}]);