import { createClient } from '@/lib/supabase/client'
import { withCache, CACHE_KEYS, CACHE_TTL, invalidateCache } from '@/lib/cache'
import { withClientCache, CLIENT_CACHE_KEYS, CLIENT_CACHE_TTL, invalidateClientCache } from '@/lib/client-cache'
import type {
  Category,
  CategoryInsert,
  CategoryUpdate,
  CategoryWithCount,
  DatabaseError
} from '@/types/database'

const supabase = createClient()

/**
 * 获取用户的所有分类（包含提示词数量）- 优化版本
 */
export async function getCategories(): Promise<CategoryWithCount[]> {
  return withClientCache(
    CLIENT_CACHE_KEYS.CATEGORIES,
    CLIENT_CACHE_TTL.LONG, // 10分钟客户端缓存
    async () => {
      try {
        // 获取当前用户
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) throw new Error('用户未登录')

        // 获取所有分类
        const { data: categories, error: categoriesError } = await supabase
          .from('categories')
          .select('*')
          .eq('user_id', user.id)
          .is('deleted_at', null)
          .order('sort_order', { ascending: true })

        if (categoriesError) throw categoriesError

        // 使用单个查询获取所有分类的提示词数量
        const { data: promptCounts, error: countError } = await supabase
          .from('prompts')
          .select('category_id')
          .eq('user_id', user.id)
          .is('deleted_at', null)

        if (countError) throw countError

        // 统计每个分类的提示词数量
        const countMap = new Map<string, number>()
        promptCounts?.forEach(prompt => {
          const categoryId = prompt.category_id
          if (categoryId) {
            countMap.set(categoryId, (countMap.get(categoryId) || 0) + 1)
          }
        })

        // 合并分类和数量信息
        const categoriesWithCount = categories.map(category => ({
          ...category,
          prompt_count: countMap.get(category.id) || 0
        })) as CategoryWithCount[]

        return categoriesWithCount
      } catch (error) {
        console.error('获取分类失败:', error)
        throw new Error('获取分类失败')
      }
    }
  )
}

/**
 * 根据ID获取单个分类
 */
export async function getCategoryById(id: string): Promise<Category | null> {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .is('deleted_at', null)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // 未找到记录
      throw error
    }

    return data
  } catch (error) {
    console.error('获取分类失败:', error)
    throw new Error('获取分类失败')
  }
}

/**
 * 创建新分类
 */
export async function createCategory(category: Omit<CategoryInsert, 'user_id'>): Promise<Category> {
  try {
    // 获取当前用户
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    // 获取最大排序值
    const { data: maxSortData } = await supabase
      .from('categories')
      .select('sort_order')
      .eq('user_id', user.id)
      .is('deleted_at', null)
      .order('sort_order', { ascending: false })
      .limit(1)

    const nextSortOrder = (maxSortData?.[0]?.sort_order || 0) + 1

    const { data, error } = await supabase
      .from('categories')
      .insert({
        ...category,
        user_id: user.id,
        sort_order: category.sort_order || nextSortOrder
      })
      .select()
      .single()

    if (error) throw error

    // 清除相关缓存
    invalidateCache([CACHE_KEYS.CATEGORIES, 'prompts:'])

    return data
  } catch (error) {
    console.error('创建分类失败:', error)
    throw new Error('创建分类失败')
  }
}

/**
 * 更新分类
 */
export async function updateCategory(id: string, updates: CategoryUpdate): Promise<Category> {
  try {
    const { data, error } = await supabase
      .from('categories')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    return data
  } catch (error) {
    console.error('更新分类失败:', error)
    throw new Error('更新分类失败')
  }
}

/**
 * 软删除分类
 */
export async function deleteCategory(id: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('categories')
      .update({ 
        deleted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)

    if (error) throw error
  } catch (error) {
    console.error('删除分类失败:', error)
    throw new Error('删除分类失败')
  }
}

/**
 * 更新分类排序
 */
export async function updateCategoriesOrder(categoryIds: string[]): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    // 使用数据库函数进行批量更新
    const { error } = await supabase.rpc('update_categories_order', {
      category_ids: categoryIds,
      user_uuid: user.id
    })

    if (error) throw error

    // 清除相关缓存
    invalidateCache([CACHE_KEYS.CATEGORIES])
    invalidateClientCache([CLIENT_CACHE_KEYS.CATEGORIES])
  } catch (error) {
    console.error('更新分类排序失败:', error)
    throw new Error('更新分类排序失败')
  }
}

/**
 * 检查分类名称是否已存在
 */
export async function checkCategoryNameExists(name: string, excludeId?: string): Promise<boolean> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    let query = supabase
      .from('categories')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', name)
      .is('deleted_at', null)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) throw error

    return data.length > 0
  } catch (error) {
    console.error('检查分类名称失败:', error)
    return false
  }
}

/**
 * 获取分类的提示词数量
 */
export async function getCategoryPromptCount(categoryId: string): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('prompts')
      .select('*', { count: 'exact', head: true })
      .eq('category_id', categoryId)
      .is('deleted_at', null)

    if (error) throw error

    return count || 0
  } catch (error) {
    console.error('获取分类提示词数量失败:', error)
    return 0
  }
}
