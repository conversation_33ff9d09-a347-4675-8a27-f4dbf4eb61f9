"use strict";exports.id=796,exports.ids=[796],exports.modules={15616:(a,b,c)=>{c.d(b,{T:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},22392:(a,b,c)=>{c.d(b,{J:()=>g});var d=c(47828);let e={PROMPTS:"local_prompts",CATEGORIES:"local_categories",TAGS:"local_tags"};class f{constructor(){this.supabase=(0,d.U)(),this.syncInProgress=!1,this.syncQueue=[]}generateLocalId(){return`local_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}async getCurrentUserId(){let{data:{user:a}}=await this.supabase.auth.getUser();return a?a.id:null}getLocalData(a){try{return[]}catch(a){return console.error("获取本地数据失败:",a),[]}}setLocalData(a,b){try{return}catch(a){console.error("保存本地数据失败:",a)}}addToSyncQueue(a){let b={...a,id:this.generateLocalId(),timestamp:Date.now(),retryCount:0};this.syncQueue.push(b),this.saveSyncQueue()}saveSyncQueue(){}loadSyncQueue(){try{this.syncQueue=[];return}catch(a){console.error("加载同步队列失败:",a),this.syncQueue=[]}}async getPrompts(){console.log("\uD83D\uDCF1 从本地获取提示词数据");let a=this.getLocalData(e.PROMPTS);if(a.length>0)return console.log(`✅ 本地找到 ${a.length} 个提示词`),a;console.log("\uD83D\uDCF1 本地无数据，尝试从远程获取...");try{let{data:a,error:b}=await this.supabase.from("prompts").select("*, category:categories(*), prompt_tags(tag:tags(*))").order("updated_at",{ascending:!1});if(b)return console.warn("远程获取失败，返回空数组:",b),[];let c=(a||[]).map(a=>({...a,_localId:a.id,_isLocal:!1,_needsSync:!1,_lastModified:new Date(a.updated_at).getTime()}));return this.setLocalData(e.PROMPTS,c),console.log(`✅ 从远程获取并缓存了 ${c.length} 个提示词`),c}catch(a){return console.warn("获取远程提示词失败，返回空数组:",a),[]}}async getCategories(){console.log("\uD83D\uDCF1 从本地获取分类数据");let a=this.getLocalData(e.CATEGORIES);if(a.length>0)return console.log(`✅ 本地找到 ${a.length} 个分类`),a;console.log("\uD83D\uDCF1 本地无数据，尝试从远程获取...");try{let{data:a,error:b}=await this.supabase.from("categories").select("*").order("sort_order",{ascending:!0});if(b)return console.warn("远程获取失败，返回空数组:",b),[];let c=(a||[]).map(a=>({...a,_localId:a.id,_isLocal:!1,_needsSync:!1,_lastModified:new Date(a.updated_at).getTime()}));return this.setLocalData(e.CATEGORIES,c),console.log(`✅ 从远程获取并缓存了 ${c.length} 个分类`),c}catch(a){return console.warn("获取远程分类失败，返回空数组:",a),[]}}async createPrompt(a){let b=await this.getCurrentUserId();if(!b)throw Error("用户未登录");let c=this.generateLocalId(),d=Date.now(),f={id:c,...a,user_id:b,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),usage_count:0,_localId:c,_isLocal:!0,_needsSync:!0,_lastModified:d},g=this.getLocalData(e.PROMPTS);return g.unshift(f),this.setLocalData(e.PROMPTS,g),this.addToSyncQueue({type:"CREATE",table:"prompts",data:a,localId:c}),console.log("✅ 提示词已保存到本地，等待同步"),f}async createCategory(a){let b=await this.getCurrentUserId();if(!b)throw Error("用户未登录");let c=this.generateLocalId(),d=Date.now(),f={id:c,...a,user_id:b,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),_localId:c,_isLocal:!0,_needsSync:!0,_lastModified:d},g=this.getLocalData(e.CATEGORIES);return g.push(f),this.setLocalData(e.CATEGORIES,g),this.addToSyncQueue({type:"CREATE",table:"categories",data:a,localId:c}),console.log("✅ 分类已保存到本地，等待同步"),f}async updatePrompt(a,b){let c=this.getLocalData(e.PROMPTS),d=c.findIndex(b=>b.id===a||b._localId===a);if(-1===d)return console.error("提示词不存在:",a),null;let f=Date.now(),g={...c[d],...b,updated_at:new Date().toISOString(),_needsSync:!0,_lastModified:f};return c[d]=g,this.setLocalData(e.PROMPTS,c),this.addToSyncQueue({type:"UPDATE",table:"prompts",data:{id:c[d].id,...b},localId:c[d]._localId}),console.log("✅ 提示词已在本地更新，等待同步"),g}async deletePrompt(a){let b=this.getLocalData(e.PROMPTS),c=b.findIndex(b=>b.id===a||b._localId===a);if(-1===c)return console.error("提示词不存在:",a),!1;let d=b[c];return b.splice(c,1),this.setLocalData(e.PROMPTS,b),d._isLocal||this.addToSyncQueue({type:"DELETE",table:"prompts",data:{id:d.id},localId:d._localId}),console.log("✅ 提示词已在本地删除，等待同步"),!0}async updateCategory(a,b){let c=this.getLocalData(e.CATEGORIES),d=c.findIndex(b=>b.id===a||b._localId===a);if(-1===d)return console.error("分类不存在:",a),null;let f=Date.now(),g={...c[d],...b,updated_at:new Date().toISOString(),_needsSync:!0,_lastModified:f};return c[d]=g,this.setLocalData(e.CATEGORIES,c),this.addToSyncQueue({type:"UPDATE",table:"categories",data:{id:c[d].id,...b},localId:c[d]._localId}),console.log("✅ 分类已在本地更新，等待同步"),g}async deleteCategory(a){let b=this.getLocalData(e.CATEGORIES),c=b.findIndex(b=>b.id===a||b._localId===a);if(-1===c)return console.error("分类不存在:",a),!1;let d=b[c];return b.splice(c,1),this.setLocalData(e.CATEGORIES,b),d._isLocal||this.addToSyncQueue({type:"DELETE",table:"categories",data:{id:d.id},localId:d._localId}),console.log("✅ 分类已在本地删除，等待同步"),!0}async performSync(){if(this.syncInProgress||0===this.syncQueue.length)return;this.syncInProgress=!0,console.log(`🔄 开始同步 ${this.syncQueue.length} 个操作`);let a=[];for(let b of this.syncQueue)try{await this.syncSingleOperation(b),a.push(b.id),console.log(`✅ 同步成功: ${b.type} ${b.table}`)}catch(c){console.error(`❌ 同步失败: ${b.type} ${b.table}`,c),b.retryCount++,b.retryCount>=3&&(console.error(`🚫 放弃同步: ${b.id}`),a.push(b.id))}this.syncQueue=this.syncQueue.filter(b=>!a.includes(b.id)),this.saveSyncQueue(),this.syncInProgress=!1,console.log(`🎉 同步完成，剩余 ${this.syncQueue.length} 个操作`)}async syncSingleOperation(a){let{type:b,table:c,data:d,localId:e}=a;try{if("CREATE"===b){let{data:a,error:b}=await this.supabase.from(c).insert(d).select().single();if(b)throw b;this.updateLocalDataAfterSync(c,e,a)}else if("UPDATE"===b){let{data:a,error:b}=await this.supabase.from(c).update(d).eq("id",d.id).select().single();if(b)throw b;this.updateLocalDataAfterSync(c,e,a)}else if("DELETE"===b){console.log(`🔄 开始远程删除: ${c} ${d.id}`);let{error:a}=await this.supabase.from(c).delete().eq("id",d.id);if(a)throw console.error(`❌ 远程删除失败: ${c} ${d.id}`,a),a;console.log(`✅ 远程删除成功: ${c} ${d.id}`)}}catch(a){throw console.error(`❌ 同步操作失败: ${b} ${c}`,a),a}}updateLocalDataAfterSync(a,b,c){let d="prompts"===a?e.PROMPTS:"categories"===a?e.CATEGORIES:e.TAGS,f=this.getLocalData(d),g=f.findIndex(a=>a._localId===b);-1!==g&&(f[g]={...c,_localId:c.id,_isLocal:!1,_needsSync:!1,_lastModified:new Date(c.updated_at).getTime()},this.setLocalData(d,f))}startBackgroundSync(){setTimeout(()=>this.performSync(),1e3),setInterval(()=>this.performSync(),3e4)}getLocalDataOnly(){let a=this.getLocalData(e.PROMPTS),b=this.getLocalData(e.CATEGORIES);return console.log(`📱 立即获取本地数据: ${a.length} 个提示词, ${b.length} 个分类`),{prompts:a,categories:b}}async preloadAllData(){console.log("\uD83D\uDE80 开始预加载所有数据到本地");try{let[a,b]=await Promise.all([this.getPrompts(),this.getCategories()]);console.log(`✅ 预加载完成: ${a.length} 个提示词, ${b.length} 个分类`)}catch(a){console.error("❌ 预加载失败:",a)}}async refreshFromRemote(){console.log("\uD83D\uDD04 从远程刷新数据");try{this.setLocalData(e.PROMPTS,[]),this.setLocalData(e.CATEGORIES,[]),await this.preloadAllData(),console.log("✅ 远程数据刷新完成")}catch(a){console.error("❌ 远程刷新失败:",a)}}async manualSync(){await this.performSync()}getSyncStatus(){return{pending:this.syncQueue.length,lastSync:null}}getDetailedSyncStatus(){let a=this.getLocalData(e.PROMPTS),b=this.getLocalData(e.CATEGORIES),c=this.getLocalData(e.TAGS);return{pending:this.syncQueue.length,lastSync:null,queue:[...this.syncQueue],localCounts:{prompts:a.length,categories:b.length,tags:c.length}}}clearLocalData(){this.syncQueue=[],console.log("\uD83D\uDDD1️ 已清除所有本地数据")}}let g=new f},37826:(a,b,c)=>{c.d(b,{Cf:()=>l,L3:()=>n,c7:()=>m,lG:()=>i,rr:()=>o});var d=c(60687),e=c(43210),f=c(26134),g=c(11860),h=c(96241);let i=f.bL;f.l9;let j=f.ZL;f.bm;let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hJ,{ref:c,className:(0,h.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...b}));k.displayName=f.hJ.displayName;let l=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(j,{children:[(0,d.jsx)(k,{}),(0,d.jsxs)(f.UC,{ref:e,className:(0,h.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[b,(0,d.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"关闭"})]})]})]}));l.displayName=f.UC.displayName;let m=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...b});m.displayName="DialogHeader";let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hE,{ref:c,className:(0,h.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));n.displayName=f.hE.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.VY,{ref:c,className:(0,h.cn)("text-sm text-muted-foreground",a),...b}));o.displayName=f.VY.displayName},39390:(a,b,c)=>{c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},68988:(a,b,c)=>{c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:e,...c}));g.displayName="Input"},89904:(a,b,c)=>{c.d(b,{o:()=>h});var d=c(60687),e=c(37826),f=c(24934),g=c(32418);function h({isOpen:a,onClose:b,onConfirm:c,title:h,description:i,itemName:j,isLoading:k=!1}){return(0,d.jsx)(e.lG,{open:a,onOpenChange:b,children:(0,d.jsxs)(e.Cf,{className:"max-w-md",children:[(0,d.jsx)(e.c7,{children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-red-100 rounded-full",children:(0,d.jsx)(g.Icon,{name:"exclamation-triangle",className:"h-5 w-5 text-red-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)(e.L3,{className:"text-lg font-semibold",children:h}),(0,d.jsx)(e.rr,{className:"mt-1",children:i})]})]})}),j&&(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-3 my-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"即将删除："}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:j})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-4",children:[(0,d.jsx)(f.$,{variant:"outline",onClick:b,disabled:k,children:"取消"}),(0,d.jsx)(f.$,{variant:"destructive",onClick:c,disabled:k,children:k?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),"删除中..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.Icon,{name:"trash",className:"h-4 w-4 mr-2"}),"确认删除"]})})]})]})})}}};