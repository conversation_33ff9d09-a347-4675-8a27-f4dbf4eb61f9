"use strict";exports.id=121,exports.ids=[121],exports.modules={363:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},1359:(a,b,c)=>{c.d(b,{Oh:()=>f});var d=c(43210),e=0;function f(){d.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??g()),document.body.insertAdjacentElement("beforeend",a[1]??g()),e++,()=>{1===e&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),e--}},[])}function g(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}},10436:(a,b,c)=>{c.d(b,{H_:()=>cV,UC:()=>cR,YJ:()=>cS,q7:()=>cU,VF:()=>cY,JU:()=>cT,ZL:()=>cQ,z6:()=>cW,hN:()=>cX,bL:()=>cO,wv:()=>cZ,Pb:()=>c$,G5:()=>c0,ZP:()=>c_,l9:()=>cP});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(65551),i=c(14163),j=c(9510),k=c(60687),l=d.createContext(void 0);function m(a){let b=d.useContext(l);return a||b||"ltr"}var n=c(31355),o=c(1359),p=c(32547),q=c(96963);let r=["top","right","bottom","left"],s=Math.min,t=Math.max,u=Math.round,v=Math.floor,w=a=>({x:a,y:a}),x={left:"right",right:"left",bottom:"top",top:"bottom"},y={start:"end",end:"start"};function z(a,b){return"function"==typeof a?a(b):a}function A(a){return a.split("-")[0]}function B(a){return a.split("-")[1]}function C(a){return"x"===a?"y":"x"}function D(a){return"y"===a?"height":"width"}let E=new Set(["top","bottom"]);function F(a){return E.has(A(a))?"y":"x"}function G(a){return a.replace(/start|end/g,a=>y[a])}let H=["left","right"],I=["right","left"],J=["top","bottom"],K=["bottom","top"];function L(a){return a.replace(/left|right|bottom|top/g,a=>x[a])}function M(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function N(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function O(a,b,c){let d,{reference:e,floating:f}=a,g=F(b),h=C(F(b)),i=D(h),j=A(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(B(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let P=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=O(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=O(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function Q(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=z(b,a),o=M(n),p=h[m?"floating"===l?"reference":"floating":l],q=N(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=N(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function R(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function S(a){return r.some(b=>a[b]>=0)}let T=new Set(["left","top"]);async function U(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=A(c),h=B(c),i="y"===F(c),j=T.has(g)?-1:1,k=f&&i?-1:1,l=z(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function V(){return"undefined"!=typeof window}function W(a){return Z(a)?(a.nodeName||"").toLowerCase():"#document"}function X(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function Y(a){var b;return null==(b=(Z(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function Z(a){return!!V()&&(a instanceof Node||a instanceof X(a).Node)}function $(a){return!!V()&&(a instanceof Element||a instanceof X(a).Element)}function _(a){return!!V()&&(a instanceof HTMLElement||a instanceof X(a).HTMLElement)}function aa(a){return!!V()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof X(a).ShadowRoot)}let ab=new Set(["inline","contents"]);function ac(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=an(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!ab.has(e)}let ad=new Set(["table","td","th"]),ae=[":popover-open",":modal"];function af(a){return ae.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let ag=["transform","translate","scale","rotate","perspective"],ah=["transform","translate","scale","rotate","perspective","filter"],ai=["paint","layout","strict","content"];function aj(a){let b=ak(),c=$(a)?an(a):a;return ag.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||ah.some(a=>(c.willChange||"").includes(a))||ai.some(a=>(c.contain||"").includes(a))}function ak(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let al=new Set(["html","body","#document"]);function am(a){return al.has(W(a))}function an(a){return X(a).getComputedStyle(a)}function ao(a){return $(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function ap(a){if("html"===W(a))return a;let b=a.assignedSlot||a.parentNode||aa(a)&&a.host||Y(a);return aa(b)?b.host:b}function aq(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=ap(b);return am(c)?b.ownerDocument?b.ownerDocument.body:b.body:_(c)&&ac(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=X(e);if(f){let a=ar(g);return b.concat(g,g.visualViewport||[],ac(e)?e:[],a&&c?aq(a):[])}return b.concat(e,aq(e,[],c))}function ar(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function as(a){let b=an(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=_(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=u(c)!==f||u(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function at(a){return $(a)?a:a.contextElement}function au(a){let b=at(a);if(!_(b))return w(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=as(b),g=(f?u(c.width):c.width)/d,h=(f?u(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let av=w(0);function aw(a){let b=X(a);return ak()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:av}function ax(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=at(a),h=w(1);b&&(d?$(d)&&(h=au(d)):h=au(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===X(g))&&e)?aw(g):w(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=X(g),b=d&&$(d)?X(d):d,c=a,e=ar(c);for(;e&&d&&b!==c;){let a=au(e),b=e.getBoundingClientRect(),d=an(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=ar(c=X(e))}}return N({width:l,height:m,x:j,y:k})}function ay(a,b){let c=ao(a).scrollLeft;return b?b.left+c:ax(Y(a)).left+c}function az(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:ay(a,d)),y:d.top+b.scrollTop}}let aA=new Set(["absolute","fixed"]);function aB(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=X(a),d=Y(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=ak();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=Y(a),c=ao(a),d=a.ownerDocument.body,e=t(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=t(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+ay(a),h=-c.scrollTop;return"rtl"===an(d).direction&&(g+=t(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(Y(a));else if($(b))d=function(a,b){let c=ax(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=_(a)?au(a):w(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=aw(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return N(d)}function aC(a){return"static"===an(a).position}function aD(a,b){if(!_(a)||"fixed"===an(a).position)return null;if(b)return b(a);let c=a.offsetParent;return Y(a)===c&&(c=c.ownerDocument.body),c}function aE(a,b){var c;let d=X(a);if(af(a))return d;if(!_(a)){let b=ap(a);for(;b&&!am(b);){if($(b)&&!aC(b))return b;b=ap(b)}return d}let e=aD(a,b);for(;e&&(c=e,ad.has(W(c)))&&aC(e);)e=aD(e,b);return e&&am(e)&&aC(e)&&!aj(e)?d:e||function(a){let b=ap(a);for(;_(b)&&!am(b);){if(aj(b))return b;if(af(b))break;b=ap(b)}return null}(a)||d}let aF=async function(a){let b=this.getOffsetParent||aE,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=_(b),e=Y(b),f="fixed"===c,g=ax(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=w(0);if(d||!d&&!f)if(("body"!==W(b)||ac(e))&&(h=ao(b)),d){let a=ax(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=ay(e));f&&!d&&e&&(i.x=ay(e));let j=!e||d||f?w(0):az(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},aG={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=Y(d),h=!!b&&af(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=w(1),k=w(0),l=_(d);if((l||!l&&!f)&&(("body"!==W(d)||ac(g))&&(i=ao(d)),_(d))){let a=ax(d);j=au(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?w(0):az(g,i,!0);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:Y,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?af(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=aq(a,[],!1).filter(a=>$(a)&&"body"!==W(a)),e=null,f="fixed"===an(a).position,g=f?ap(a):a;for(;$(g)&&!am(g);){let b=an(g),c=aj(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&aA.has(e.position)||ac(g)&&!c&&function a(b,c){let d=ap(b);return!(d===c||!$(d)||am(d))&&("fixed"===an(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=ap(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=aB(b,c,e);return a.top=t(d.top,a.top),a.right=s(d.right,a.right),a.bottom=s(d.bottom,a.bottom),a.left=t(d.left,a.left),a},aB(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:aE,getElementRects:aF,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=as(a);return{width:b,height:c}},getScale:au,isElement:$,isRTL:function(a){return"rtl"===an(a).direction}};function aH(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let aI=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=z(a,b)||{};if(null==j)return{};let l=M(k),m={x:c,y:d},n=C(F(e)),o=D(n),p=await g.getDimensions(j),q="y"===n,r=q?"clientHeight":"clientWidth",u=f.reference[o]+f.reference[n]-m[n]-f.floating[o],v=m[n]-f.reference[n],w=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),x=w?w[r]:0;x&&await (null==g.isElement?void 0:g.isElement(w))||(x=h.floating[r]||f.floating[o]);let y=x/2-p[o]/2-1,A=s(l[q?"top":"left"],y),E=s(l[q?"bottom":"right"],y),G=x-p[o]-E,H=x/2-p[o]/2+(u/2-v/2),I=t(A,s(H,G)),J=!i.arrow&&null!=B(e)&&H!==I&&f.reference[o]/2-(H<A?A:E)-p[o]/2<0,K=J?H<A?H-A:H-G:0;return{[n]:m[n]+K,data:{[n]:I,centerOffset:H-I-K,...J&&{alignmentOffset:K}},reset:J}}});var aJ=c(51215),aK="undefined"!=typeof document?d.useLayoutEffect:function(){};function aL(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!aL(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!aL(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function aM(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function aN(a,b){let c=aM(a);return Math.round(b*c)/c}function aO(a){let b=d.useRef(a);return aK(()=>{b.current=a}),b}var aP=d.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,k.jsx)(i.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,k.jsx)("polygon",{points:"0,0 30,0 15,10"})})});aP.displayName="Arrow";var aQ=c(13495),aR=c(66156),aS=c(18853),aT="Popper",[aU,aV]=(0,g.A)(aT),[aW,aX]=aU(aT),aY=a=>{let{__scopePopper:b,children:c}=a,[e,f]=d.useState(null);return(0,k.jsx)(aW,{scope:b,anchor:e,onAnchorChange:f,children:c})};aY.displayName=aT;var aZ="PopperAnchor",a$=d.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:e,...g}=a,h=aX(aZ,c),j=d.useRef(null),l=(0,f.s)(b,j);return d.useEffect(()=>{h.onAnchorChange(e?.current||j.current)}),e?null:(0,k.jsx)(i.sG.div,{...g,ref:l})});a$.displayName=aZ;var a_="PopperContent",[a0,a1]=aU(a_),a2=d.forwardRef((a,b)=>{let{__scopePopper:c,side:e="bottom",sideOffset:g=0,align:h="center",alignOffset:j=0,arrowPadding:l=0,avoidCollisions:m=!0,collisionBoundary:n=[],collisionPadding:o=0,sticky:p="partial",hideWhenDetached:q=!1,updatePositionStrategy:r="optimized",onPlaced:u,...w}=a,x=aX(a_,c),[y,E]=d.useState(null),M=(0,f.s)(b,a=>E(a)),[N,O]=d.useState(null),V=(0,aS.X)(N),W=V?.width??0,X=V?.height??0,Z="number"==typeof o?o:{top:0,right:0,bottom:0,left:0,...o},$=Array.isArray(n)?n:[n],_=$.length>0,aa={padding:Z,boundary:$.filter(a6),altBoundary:_},{refs:ab,floatingStyles:ac,placement:ad,isPositioned:ae,middlewareData:af}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:e=[],platform:f,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=d.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=d.useState(e);aL(n,e)||o(e);let[p,q]=d.useState(null),[r,s]=d.useState(null),t=d.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=d.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=d.useRef(null),y=d.useRef(null),z=d.useRef(l),A=null!=j,B=aO(j),C=aO(f),D=aO(k),E=d.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};C.current&&(a.platform=C.current),((a,b,c)=>{let d=new Map,e={platform:aG,...c},f={...e.platform,_c:d};return P(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==D.current};F.current&&!aL(z.current,b)&&(z.current=b,aJ.flushSync(()=>{m(b)}))})},[n,b,c,C,D]);aK(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let F=d.useRef(!1);aK(()=>(F.current=!0,()=>{F.current=!1}),[]),aK(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,E);E()}},[v,w,E,B,A]);let G=d.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),H=d.useMemo(()=>({reference:v,floating:w}),[v,w]),I=d.useMemo(()=>{let a={position:c,left:0,top:0};if(!H.floating)return a;let b=aN(H.floating,l.x),d=aN(H.floating,l.y);return i?{...a,transform:"translate("+b+"px, "+d+"px)",...aM(H.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,i,H.floating,l.x,l.y]);return d.useMemo(()=>({...l,update:E,refs:G,elements:H,floatingStyles:I}),[l,E,G,H,I])}({strategy:"fixed",placement:e+("center"!==h?"-"+h:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=at(a),l=f||g?[...k?aq(k):[],...aq(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=Y(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=v(l),p=v(e.clientWidth-(k+m)),q={rootMargin:-o+"px "+-p+"px "+-v(e.clientHeight-(l+n))+"px "+-v(k)+"px",threshold:t(0,s(1,i))||1},r=!0;function u(b){let d=b[0].intersectionRatio;if(d!==i){if(!r)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||aH(j,a.getBoundingClientRect())||g(),r=!1}try{d=new IntersectionObserver(u,{...q,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(u,q)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?ax(a):null;return j&&function b(){let d=ax(a);p&&!aH(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===r}),elements:{reference:x.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await U(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:g+X,alignmentAxis:j}),m&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=z(a,b),j={x:c,y:d},k=await Q(b,i),l=F(A(e)),m=C(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=t(c,s(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=t(c,s(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=z(a,b),k={x:c,y:d},l=F(e),m=C(l),n=k[m],o=k[l],p=z(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=T.has(A(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,...aa}),m&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=z(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=A(h),v=F(k),w=A(k)===k,x=await (null==l.isRTL?void 0:l.isRTL(m.floating)),y=p||(w||!s?[L(k)]:function(a){let b=L(a);return[G(a),b,G(b)]}(k)),E="none"!==r;!p&&E&&y.push(...function(a,b,c,d){let e=B(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?I:H;return b?H:I;case"left":case"right":return b?J:K;default:return[]}}(A(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(G)))),f}(k,s,r,x));let M=[k,...y],N=await Q(b,t),O=[],P=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&O.push(N[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=B(a),e=C(F(a)),f=D(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=L(g)),[g,L(g)]}(h,j,x);O.push(N[a[0]],N[a[1]])}if(P=[...P,{placement:h,overflows:O}],!O.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=M[a];if(b&&("alignment"!==o||v===F(b)||P.every(a=>a.overflows[0]>0&&F(a.placement)===v)))return{data:{index:a,overflows:P},reset:{placement:b}};let c=null==(f=P.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=P.filter(a=>{if(E){let b=F(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...aa}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=z(a,b),m=await Q(b,l),n=A(g),o=B(g),p="y"===F(g),{width:q,height:r}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let u=r-m.top-m.bottom,v=q-m.left-m.right,w=s(r-m[e],u),x=s(q-m[f],v),y=!b.middlewareData.shift,C=w,D=x;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(D=v),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(C=u),y&&!o){let a=t(m.left,0),b=t(m.right,0),c=t(m.top,0),d=t(m.bottom,0);p?D=q-2*(0!==a||0!==b?a+b:t(m.left,m.right)):C=r-2*(0!==c||0!==d?c+d:t(m.top,m.bottom))}await k({...b,availableWidth:D,availableHeight:C});let E=await i.getDimensions(j.floating);return q!==E.width||r!==E.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...aa,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),N&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?aI({element:c.current,padding:d}).fn(b):{}:c?aI({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:N,padding:l}),a7({arrowWidth:W,arrowHeight:X}),q&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=z(a,b);switch(d){case"referenceHidden":{let a=R(await Q(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:S(a)}}}case"escaped":{let a=R(await Q(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:S(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...aa})]}),[ag,ah]=a8(ad),ai=(0,aQ.c)(u);(0,aR.N)(()=>{ae&&ai?.()},[ae,ai]);let aj=af.arrow?.x,ak=af.arrow?.y,al=af.arrow?.centerOffset!==0,[am,an]=d.useState();return(0,aR.N)(()=>{y&&an(window.getComputedStyle(y).zIndex)},[y]),(0,k.jsx)("div",{ref:ab.setFloating,"data-radix-popper-content-wrapper":"",style:{...ac,transform:ae?ac.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:am,"--radix-popper-transform-origin":[af.transformOrigin?.x,af.transformOrigin?.y].join(" "),...af.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,k.jsx)(a0,{scope:c,placedSide:ag,onArrowChange:O,arrowX:aj,arrowY:ak,shouldHideArrow:al,children:(0,k.jsx)(i.sG.div,{"data-side":ag,"data-align":ah,...w,ref:M,style:{...w.style,animation:ae?void 0:"none"}})})})});a2.displayName=a_;var a3="PopperArrow",a4={top:"bottom",right:"left",bottom:"top",left:"right"},a5=d.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=a1(a3,c),f=a4[e.placedSide];return(0,k.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,k.jsx)(aP,{...d,ref:b,style:{...d.style,display:"block"}})})});function a6(a){return null!==a}a5.displayName=a3;var a7=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=a8(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function a8(a){let[b,c="center"]=a.split("-");return[b,c]}var a9=c(25028),ba=c(46059),bb="rovingFocusGroup.onEntryFocus",bc={bubbles:!1,cancelable:!0},bd="RovingFocusGroup",[be,bf,bg]=(0,j.N)(bd),[bh,bi]=(0,g.A)(bd,[bg]),[bj,bk]=bh(bd),bl=d.forwardRef((a,b)=>(0,k.jsx)(be.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,k.jsx)(be.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,k.jsx)(bm,{...a,ref:b})})}));bl.displayName=bd;var bm=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:g,loop:j=!1,dir:l,currentTabStopId:n,defaultCurrentTabStopId:o,onCurrentTabStopIdChange:p,onEntryFocus:q,preventScrollOnEntryFocus:r=!1,...s}=a,t=d.useRef(null),u=(0,f.s)(b,t),v=m(l),[w,x]=(0,h.i)({prop:n,defaultProp:o??null,onChange:p,caller:bd}),[y,z]=d.useState(!1),A=(0,aQ.c)(q),B=bf(c),C=d.useRef(!1),[D,E]=d.useState(0);return d.useEffect(()=>{let a=t.current;if(a)return a.addEventListener(bb,A),()=>a.removeEventListener(bb,A)},[A]),(0,k.jsx)(bj,{scope:c,orientation:g,dir:v,loop:j,currentTabStopId:w,onItemFocus:d.useCallback(a=>x(a),[x]),onItemShiftTab:d.useCallback(()=>z(!0),[]),onFocusableItemAdd:d.useCallback(()=>E(a=>a+1),[]),onFocusableItemRemove:d.useCallback(()=>E(a=>a-1),[]),children:(0,k.jsx)(i.sG.div,{tabIndex:y||0===D?-1:0,"data-orientation":g,...s,ref:u,style:{outline:"none",...a.style},onMouseDown:(0,e.m)(a.onMouseDown,()=>{C.current=!0}),onFocus:(0,e.m)(a.onFocus,a=>{let b=!C.current;if(a.target===a.currentTarget&&b&&!y){let b=new CustomEvent(bb,bc);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=B().filter(a=>a.focusable);bq([a.find(a=>a.active),a.find(a=>a.id===w),...a].filter(Boolean).map(a=>a.ref.current),r)}}C.current=!1}),onBlur:(0,e.m)(a.onBlur,()=>z(!1))})})}),bn="RovingFocusGroupItem",bo=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:f=!0,active:g=!1,tabStopId:h,children:j,...l}=a,m=(0,q.B)(),n=h||m,o=bk(bn,c),p=o.currentTabStopId===n,r=bf(c),{onFocusableItemAdd:s,onFocusableItemRemove:t,currentTabStopId:u}=o;return d.useEffect(()=>{if(f)return s(),()=>t()},[f,s,t]),(0,k.jsx)(be.ItemSlot,{scope:c,id:n,focusable:f,active:g,children:(0,k.jsx)(i.sG.span,{tabIndex:p?0:-1,"data-orientation":o.orientation,...l,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f?o.onItemFocus(n):a.preventDefault()}),onFocus:(0,e.m)(a.onFocus,()=>o.onItemFocus(n)),onKeyDown:(0,e.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void o.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return bp[e]}(a,o.orientation,o.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=r().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=o.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>bq(c))}}),children:"function"==typeof j?j({isCurrentTabStop:p,hasTabStop:null!=u}):j})})});bo.displayName=bn;var bp={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function bq(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var br=c(8730),bs=c(63376),bt=c(42247),bu=["Enter"," "],bv=["ArrowUp","PageDown","End"],bw=["ArrowDown","PageUp","Home",...bv],bx={ltr:[...bu,"ArrowRight"],rtl:[...bu,"ArrowLeft"]},by={ltr:["ArrowLeft"],rtl:["ArrowRight"]},bz="Menu",[bA,bB,bC]=(0,j.N)(bz),[bD,bE]=(0,g.A)(bz,[bC,aV,bi]),bF=aV(),bG=bi(),[bH,bI]=bD(bz),[bJ,bK]=bD(bz),bL=a=>{let{__scopeMenu:b,open:c=!1,children:e,dir:f,onOpenChange:g,modal:h=!0}=a,i=bF(b),[j,l]=d.useState(null),n=d.useRef(!1),o=(0,aQ.c)(g),p=m(f);return d.useEffect(()=>{let a=()=>{n.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>n.current=!1;return document.addEventListener("keydown",a,{capture:!0}),()=>{document.removeEventListener("keydown",a,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),(0,k.jsx)(aY,{...i,children:(0,k.jsx)(bH,{scope:b,open:c,onOpenChange:o,content:j,onContentChange:l,children:(0,k.jsx)(bJ,{scope:b,onClose:d.useCallback(()=>o(!1),[o]),isUsingKeyboardRef:n,dir:p,modal:h,children:e})})})};bL.displayName=bz;var bM=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=bF(c);return(0,k.jsx)(a$,{...e,...d,ref:b})});bM.displayName="MenuAnchor";var bN="MenuPortal",[bO,bP]=bD(bN,{forceMount:void 0}),bQ=a=>{let{__scopeMenu:b,forceMount:c,children:d,container:e}=a,f=bI(bN,b);return(0,k.jsx)(bO,{scope:b,forceMount:c,children:(0,k.jsx)(ba.C,{present:c||f.open,children:(0,k.jsx)(a9.Z,{asChild:!0,container:e,children:d})})})};bQ.displayName=bN;var bR="MenuContent",[bS,bT]=bD(bR),bU=d.forwardRef((a,b)=>{let c=bP(bR,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=bI(bR,a.__scopeMenu),g=bK(bR,a.__scopeMenu);return(0,k.jsx)(bA.Provider,{scope:a.__scopeMenu,children:(0,k.jsx)(ba.C,{present:d||f.open,children:(0,k.jsx)(bA.Slot,{scope:a.__scopeMenu,children:g.modal?(0,k.jsx)(bV,{...e,ref:b}):(0,k.jsx)(bW,{...e,ref:b})})})})}),bV=d.forwardRef((a,b)=>{let c=bI(bR,a.__scopeMenu),g=d.useRef(null),h=(0,f.s)(b,g);return d.useEffect(()=>{let a=g.current;if(a)return(0,bs.Eq)(a)},[]),(0,k.jsx)(bY,{...a,ref:h,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:(0,e.m)(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),bW=d.forwardRef((a,b)=>{let c=bI(bR,a.__scopeMenu);return(0,k.jsx)(bY,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),bX=(0,br.TL)("MenuContent.ScrollLock"),bY=d.forwardRef((a,b)=>{let{__scopeMenu:c,loop:g=!1,trapFocus:h,onOpenAutoFocus:i,onCloseAutoFocus:j,disableOutsidePointerEvents:l,onEntryFocus:m,onEscapeKeyDown:q,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:t,onDismiss:u,disableOutsideScroll:v,...w}=a,x=bI(bR,c),y=bK(bR,c),z=bF(c),A=bG(c),B=bB(c),[C,D]=d.useState(null),E=d.useRef(null),F=(0,f.s)(b,E,x.onContentChange),G=d.useRef(0),H=d.useRef(""),I=d.useRef(0),J=d.useRef(null),K=d.useRef("right"),L=d.useRef(0),M=v?bt.A:d.Fragment;d.useEffect(()=>()=>window.clearTimeout(G.current),[]),(0,o.Oh)();let N=d.useCallback(a=>K.current===J.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,J.current?.area),[]);return(0,k.jsx)(bS,{scope:c,searchRef:H,onItemEnter:d.useCallback(a=>{N(a)&&a.preventDefault()},[N]),onItemLeave:d.useCallback(a=>{N(a)||(E.current?.focus(),D(null))},[N]),onTriggerLeave:d.useCallback(a=>{N(a)&&a.preventDefault()},[N]),pointerGraceTimerRef:I,onPointerGraceIntentChange:d.useCallback(a=>{J.current=a},[]),children:(0,k.jsx)(M,{...v?{as:bX,allowPinchZoom:!0}:void 0,children:(0,k.jsx)(p.n,{asChild:!0,trapped:h,onMountAutoFocus:(0,e.m)(i,a=>{a.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:j,children:(0,k.jsx)(n.qW,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:q,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:t,onDismiss:u,children:(0,k.jsx)(bl,{asChild:!0,...A,dir:y.dir,orientation:"vertical",loop:g,currentTabStopId:C,onCurrentTabStopIdChange:D,onEntryFocus:(0,e.m)(m,a=>{y.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,k.jsx)(a2,{role:"menu","aria-orientation":"vertical","data-state":co(x.open),"data-radix-menu-content":"",dir:y.dir,...z,...w,ref:F,style:{outline:"none",...w.style},onKeyDown:(0,e.m)(w.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=H.current+a,c=B().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){H.current=b,window.clearTimeout(G.current),""!==b&&(G.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=E.current;if(a.target!==e||!bw.includes(a.key))return;a.preventDefault();let f=B().filter(a=>!a.disabled).map(a=>a.ref.current);bv.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:(0,e.m)(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(G.current),H.current="")}),onPointerMove:(0,e.m)(a.onPointerMove,cr(a=>{let b=a.target,c=L.current!==a.clientX;a.currentTarget.contains(b)&&c&&(K.current=a.clientX>L.current?"right":"left",L.current=a.clientX)}))})})})})})})});bU.displayName=bR;var bZ=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,k.jsx)(i.sG.div,{role:"group",...d,ref:b})});bZ.displayName="MenuGroup";var b$=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,k.jsx)(i.sG.div,{...d,ref:b})});b$.displayName="MenuLabel";var b_="MenuItem",b0="menu.itemSelect",b1=d.forwardRef((a,b)=>{let{disabled:c=!1,onSelect:g,...h}=a,j=d.useRef(null),l=bK(b_,a.__scopeMenu),m=bT(b_,a.__scopeMenu),n=(0,f.s)(b,j),o=d.useRef(!1);return(0,k.jsx)(b2,{...h,ref:n,disabled:c,onClick:(0,e.m)(a.onClick,()=>{let a=j.current;if(!c&&a){let b=new CustomEvent(b0,{bubbles:!0,cancelable:!0});a.addEventListener(b0,a=>g?.(a),{once:!0}),(0,i.hO)(a,b),b.defaultPrevented?o.current=!1:l.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),o.current=!0},onPointerUp:(0,e.m)(a.onPointerUp,a=>{o.current||a.currentTarget?.click()}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{let b=""!==m.searchRef.current;c||b&&" "===a.key||bu.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});b1.displayName=b_;var b2=d.forwardRef((a,b)=>{let{__scopeMenu:c,disabled:g=!1,textValue:h,...j}=a,l=bT(b_,c),m=bG(c),n=d.useRef(null),o=(0,f.s)(b,n),[p,q]=d.useState(!1),[r,s]=d.useState("");return d.useEffect(()=>{let a=n.current;a&&s((a.textContent??"").trim())},[j.children]),(0,k.jsx)(bA.ItemSlot,{scope:c,disabled:g,textValue:h??r,children:(0,k.jsx)(bo,{asChild:!0,...m,focusable:!g,children:(0,k.jsx)(i.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":g||void 0,"data-disabled":g?"":void 0,...j,ref:o,onPointerMove:(0,e.m)(a.onPointerMove,cr(a=>{g?l.onItemLeave(a):(l.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,e.m)(a.onPointerLeave,cr(a=>l.onItemLeave(a))),onFocus:(0,e.m)(a.onFocus,()=>q(!0)),onBlur:(0,e.m)(a.onBlur,()=>q(!1))})})})}),b3=d.forwardRef((a,b)=>{let{checked:c=!1,onCheckedChange:d,...f}=a;return(0,k.jsx)(cb,{scope:a.__scopeMenu,checked:c,children:(0,k.jsx)(b1,{role:"menuitemcheckbox","aria-checked":cp(c)?"mixed":c,...f,ref:b,"data-state":cq(c),onSelect:(0,e.m)(f.onSelect,()=>d?.(!!cp(c)||!c),{checkForDefaultPrevented:!1})})})});b3.displayName="MenuCheckboxItem";var b4="MenuRadioGroup",[b5,b6]=bD(b4,{value:void 0,onValueChange:()=>{}}),b7=d.forwardRef((a,b)=>{let{value:c,onValueChange:d,...e}=a,f=(0,aQ.c)(d);return(0,k.jsx)(b5,{scope:a.__scopeMenu,value:c,onValueChange:f,children:(0,k.jsx)(bZ,{...e,ref:b})})});b7.displayName=b4;var b8="MenuRadioItem",b9=d.forwardRef((a,b)=>{let{value:c,...d}=a,f=b6(b8,a.__scopeMenu),g=c===f.value;return(0,k.jsx)(cb,{scope:a.__scopeMenu,checked:g,children:(0,k.jsx)(b1,{role:"menuitemradio","aria-checked":g,...d,ref:b,"data-state":cq(g),onSelect:(0,e.m)(d.onSelect,()=>f.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});b9.displayName=b8;var ca="MenuItemIndicator",[cb,cc]=bD(ca,{checked:!1}),cd=d.forwardRef((a,b)=>{let{__scopeMenu:c,forceMount:d,...e}=a,f=cc(ca,c);return(0,k.jsx)(ba.C,{present:d||cp(f.checked)||!0===f.checked,children:(0,k.jsx)(i.sG.span,{...e,ref:b,"data-state":cq(f.checked)})})});cd.displayName=ca;var ce=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,k.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...d,ref:b})});ce.displayName="MenuSeparator";var cf=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=bF(c);return(0,k.jsx)(a5,{...e,...d,ref:b})});cf.displayName="MenuArrow";var cg="MenuSub",[ch,ci]=bD(cg),cj=a=>{let{__scopeMenu:b,children:c,open:e=!1,onOpenChange:f}=a,g=bI(cg,b),h=bF(b),[i,j]=d.useState(null),[l,m]=d.useState(null),n=(0,aQ.c)(f);return d.useEffect(()=>(!1===g.open&&n(!1),()=>n(!1)),[g.open,n]),(0,k.jsx)(aY,{...h,children:(0,k.jsx)(bH,{scope:b,open:e,onOpenChange:n,content:l,onContentChange:m,children:(0,k.jsx)(ch,{scope:b,contentId:(0,q.B)(),triggerId:(0,q.B)(),trigger:i,onTriggerChange:j,children:c})})})};cj.displayName=cg;var ck="MenuSubTrigger",cl=d.forwardRef((a,b)=>{let c=bI(ck,a.__scopeMenu),g=bK(ck,a.__scopeMenu),h=ci(ck,a.__scopeMenu),i=bT(ck,a.__scopeMenu),j=d.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:m}=i,n={__scopeMenu:a.__scopeMenu},o=d.useCallback(()=>{j.current&&window.clearTimeout(j.current),j.current=null},[]);return d.useEffect(()=>o,[o]),d.useEffect(()=>{let a=l.current;return()=>{window.clearTimeout(a),m(null)}},[l,m]),(0,k.jsx)(bM,{asChild:!0,...n,children:(0,k.jsx)(b2,{id:h.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":h.contentId,"data-state":co(c.open),...a,ref:(0,f.t)(b,h.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:(0,e.m)(a.onPointerMove,cr(b=>{i.onItemEnter(b),!b.defaultPrevented&&(a.disabled||c.open||j.current||(i.onPointerGraceIntentChange(null),j.current=window.setTimeout(()=>{c.onOpenChange(!0),o()},100)))})),onPointerLeave:(0,e.m)(a.onPointerLeave,cr(a=>{o();let b=c.content?.getBoundingClientRect();if(b){let d=c.content?.dataset.side,e="right"===d,f=b[e?"left":"right"],g=b[e?"right":"left"];i.onPointerGraceIntentChange({area:[{x:a.clientX+(e?-5:5),y:a.clientY},{x:f,y:b.top},{x:g,y:b.top},{x:g,y:b.bottom},{x:f,y:b.bottom}],side:d}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(a),a.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,e.m)(a.onKeyDown,b=>{let d=""!==i.searchRef.current;a.disabled||d&&" "===b.key||bx[g.dir].includes(b.key)&&(c.onOpenChange(!0),c.content?.focus(),b.preventDefault())})})})});cl.displayName=ck;var cm="MenuSubContent",cn=d.forwardRef((a,b)=>{let c=bP(bR,a.__scopeMenu),{forceMount:g=c.forceMount,...h}=a,i=bI(bR,a.__scopeMenu),j=bK(bR,a.__scopeMenu),l=ci(cm,a.__scopeMenu),m=d.useRef(null),n=(0,f.s)(b,m);return(0,k.jsx)(bA.Provider,{scope:a.__scopeMenu,children:(0,k.jsx)(ba.C,{present:g||i.open,children:(0,k.jsx)(bA.Slot,{scope:a.__scopeMenu,children:(0,k.jsx)(bY,{id:l.contentId,"aria-labelledby":l.triggerId,...h,ref:n,align:"start",side:"rtl"===j.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{j.isUsingKeyboardRef.current&&m.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:(0,e.m)(a.onFocusOutside,a=>{a.target!==l.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,e.m)(a.onEscapeKeyDown,a=>{j.onClose(),a.preventDefault()}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=by[j.dir].includes(a.key);b&&c&&(i.onOpenChange(!1),l.trigger?.focus(),a.preventDefault())})})})})})});function co(a){return a?"open":"closed"}function cp(a){return"indeterminate"===a}function cq(a){return cp(a)?"indeterminate":a?"checked":"unchecked"}function cr(a){return b=>"mouse"===b.pointerType?a(b):void 0}cn.displayName=cm;var cs="DropdownMenu",[ct,cu]=(0,g.A)(cs,[bE]),cv=bE(),[cw,cx]=ct(cs),cy=a=>{let{__scopeDropdownMenu:b,children:c,dir:e,open:f,defaultOpen:g,onOpenChange:i,modal:j=!0}=a,l=cv(b),m=d.useRef(null),[n,o]=(0,h.i)({prop:f,defaultProp:g??!1,onChange:i,caller:cs});return(0,k.jsx)(cw,{scope:b,triggerId:(0,q.B)(),triggerRef:m,contentId:(0,q.B)(),open:n,onOpenChange:o,onOpenToggle:d.useCallback(()=>o(a=>!a),[o]),modal:j,children:(0,k.jsx)(bL,{...l,open:n,onOpenChange:o,dir:e,modal:j,children:c})})};cy.displayName=cs;var cz="DropdownMenuTrigger",cA=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,disabled:d=!1,...g}=a,h=cx(cz,c),j=cv(c);return(0,k.jsx)(bM,{asChild:!0,...j,children:(0,k.jsx)(i.sG.button,{type:"button",id:h.triggerId,"aria-haspopup":"menu","aria-expanded":h.open,"aria-controls":h.open?h.contentId:void 0,"data-state":h.open?"open":"closed","data-disabled":d?"":void 0,disabled:d,...g,ref:(0,f.t)(b,h.triggerRef),onPointerDown:(0,e.m)(a.onPointerDown,a=>{!d&&0===a.button&&!1===a.ctrlKey&&(h.onOpenToggle(),h.open||a.preventDefault())}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{!d&&(["Enter"," "].includes(a.key)&&h.onOpenToggle(),"ArrowDown"===a.key&&h.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});cA.displayName=cz;var cB=a=>{let{__scopeDropdownMenu:b,...c}=a,d=cv(b);return(0,k.jsx)(bQ,{...d,...c})};cB.displayName="DropdownMenuPortal";var cC="DropdownMenuContent",cD=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...f}=a,g=cx(cC,c),h=cv(c),i=d.useRef(!1);return(0,k.jsx)(bU,{id:g.contentId,"aria-labelledby":g.triggerId,...h,...f,ref:b,onCloseAutoFocus:(0,e.m)(a.onCloseAutoFocus,a=>{i.current||g.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:(0,e.m)(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!g.modal||d)&&(i.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});cD.displayName=cC;var cE=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(bZ,{...e,...d,ref:b})});cE.displayName="DropdownMenuGroup";var cF=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(b$,{...e,...d,ref:b})});cF.displayName="DropdownMenuLabel";var cG=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(b1,{...e,...d,ref:b})});cG.displayName="DropdownMenuItem";var cH=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(b3,{...e,...d,ref:b})});cH.displayName="DropdownMenuCheckboxItem";var cI=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(b7,{...e,...d,ref:b})});cI.displayName="DropdownMenuRadioGroup";var cJ=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(b9,{...e,...d,ref:b})});cJ.displayName="DropdownMenuRadioItem";var cK=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(cd,{...e,...d,ref:b})});cK.displayName="DropdownMenuItemIndicator";var cL=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(ce,{...e,...d,ref:b})});cL.displayName="DropdownMenuSeparator",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(cf,{...e,...d,ref:b})}).displayName="DropdownMenuArrow";var cM=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(cl,{...e,...d,ref:b})});cM.displayName="DropdownMenuSubTrigger";var cN=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cv(c);return(0,k.jsx)(cn,{...e,...d,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});cN.displayName="DropdownMenuSubContent";var cO=cy,cP=cA,cQ=cB,cR=cD,cS=cE,cT=cF,cU=cG,cV=cH,cW=cI,cX=cJ,cY=cK,cZ=cL,c$=a=>{let{__scopeDropdownMenu:b,children:c,open:d,onOpenChange:e,defaultOpen:f}=a,g=cv(b),[i,j]=(0,h.i)({prop:d,defaultProp:f??!1,onChange:e,caller:"DropdownMenuSub"});return(0,k.jsx)(cj,{...g,open:i,onOpenChange:j,children:c})},c_=cM,c0=cN},13964:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18853:(a,b,c)=>{c.d(b,{X:()=>f});var d=c(43210),e=c(66156);function f(a){let[b,c]=d.useState(void 0);return(0,e.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}},21134:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},32547:(a,b,c)=>{c.d(b,{n:()=>l});var d=c(43210),e=c(98599),f=c(14163),g=c(13495),h=c(60687),i="focusScope.autoFocusOnMount",j="focusScope.autoFocusOnUnmount",k={bubbles:!1,cancelable:!0},l=d.forwardRef((a,b)=>{let{loop:c=!1,trapped:l=!1,onMountAutoFocus:q,onUnmountAutoFocus:r,...s}=a,[t,u]=d.useState(null),v=(0,g.c)(q),w=(0,g.c)(r),x=d.useRef(null),y=(0,e.s)(b,a=>u(a)),z=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(l){let a=function(a){if(z.paused||!t)return;let b=a.target;t.contains(b)?x.current=b:o(x.current,{select:!0})},b=function(a){if(z.paused||!t)return;let b=a.relatedTarget;null!==b&&(t.contains(b)||o(x.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&o(t)});return t&&c.observe(t,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[l,t,z.paused]),d.useEffect(()=>{if(t){p.add(z);let a=document.activeElement;if(!t.contains(a)){let b=new CustomEvent(i,k);t.addEventListener(i,v),t.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(o(d,{select:b}),document.activeElement!==c)return}(m(t).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&o(t))}return()=>{t.removeEventListener(i,v),setTimeout(()=>{let b=new CustomEvent(j,k);t.addEventListener(j,w),t.dispatchEvent(b),b.defaultPrevented||o(a??document.body,{select:!0}),t.removeEventListener(j,w),p.remove(z)},0)}}},[t,v,w,z]);let A=d.useCallback(a=>{if(!c&&!l||z.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,d=document.activeElement;if(b&&d){let b=a.currentTarget,[e,f]=function(a){let b=m(a);return[n(b,a),n(b.reverse(),a)]}(b);e&&f?a.shiftKey||d!==f?a.shiftKey&&d===e&&(a.preventDefault(),c&&o(f,{select:!0})):(a.preventDefault(),c&&o(e,{select:!0})):d===b&&a.preventDefault()}},[c,l,z.paused]);return(0,h.jsx)(f.sG.div,{tabIndex:-1,...s,ref:y,onKeyDown:A})});function m(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function n(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function o(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}l.displayName="FocusScope";var p=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=q(a,b)).unshift(b)},remove(b){a=q(a,b),a[0]?.resume()}}}();function q(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}},34410:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]])},42247:(a,b,c)=>{c.d(b,{A:()=>U});var d,e,f=function(){return(f=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function g(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var h=("function"==typeof SuppressedError&&SuppressedError,c(43210)),i="right-scroll-bar-position",j="width-before-scroll-bar";function k(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var l="undefined"!=typeof window?h.useLayoutEffect:h.useEffect,m=new WeakMap;function n(a){return a}var o=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=n),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=f({async:!0,ssr:!1},a),e}(),p=function(){},q=h.forwardRef(function(a,b){var c,d,e,i,j=h.useRef(null),n=h.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),q=n[0],r=n[1],s=a.forwardProps,t=a.children,u=a.className,v=a.removeScrollBar,w=a.enabled,x=a.shards,y=a.sideCar,z=a.noRelative,A=a.noIsolation,B=a.inert,C=a.allowPinchZoom,D=a.as,E=a.gapMode,F=g(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=(c=[j,b],d=function(a){return c.forEach(function(b){return k(b,a)})},(e=(0,h.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,i=e.facade,l(function(){var a=m.get(i);if(a){var b=new Set(a),d=new Set(c),e=i.current;b.forEach(function(a){d.has(a)||k(a,null)}),d.forEach(function(a){b.has(a)||k(a,e)})}m.set(i,c)},[c]),i),H=f(f({},F),q);return h.createElement(h.Fragment,null,w&&h.createElement(y,{sideCar:o,removeScrollBar:v,shards:x,noRelative:z,noIsolation:A,inert:B,setCallbacks:r,allowPinchZoom:!!C,lockRef:j,gapMode:E}),s?h.cloneElement(h.Children.only(t),f(f({},H),{ref:G})):h.createElement(void 0===D?"div":D,f({},H,{className:u,ref:G}),t))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:j,zeroRight:i};var r=function(a){var b=a.sideCar,c=g(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return h.createElement(d,f({},c))};r.isSideCarExport=!0;var s=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=e||c.nc;return b&&a.setAttribute("nonce",b),a}())){var f,g;(f=b).styleSheet?f.styleSheet.cssText=d:f.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},t=function(){var a=s();return function(b,c){h.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},u=function(){var a=t();return function(b){return a(b.styles,b.dynamic),null}},v={left:0,top:0,right:0,gap:0},w=function(a){return parseInt(a||"",10)||0},x=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[w(c),w(d),w(e)]},y=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return v;var b=x(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},z=u(),A="data-scroll-locked",B=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(i," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(j," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(j," .").concat(j," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},C=function(){var a=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(a)?a:0},D=function(){h.useEffect(function(){return document.body.setAttribute(A,(C()+1).toString()),function(){var a=C()-1;a<=0?document.body.removeAttribute(A):document.body.setAttribute(A,a.toString())}},[])},E=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;D();var f=h.useMemo(function(){return y(e)},[e]);return h.createElement(z,{styles:B(f,!b,e,c?"":"!important")})},F=!1;if("undefined"!=typeof window)try{var G=Object.defineProperty({},"passive",{get:function(){return F=!0,!0}});window.addEventListener("test",G,G),window.removeEventListener("test",G,G)}catch(a){F=!1}var H=!!F&&{passive:!1},I=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},J=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),K(a,d)){var e=L(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},K=function(a,b){return"v"===a?I(b,"overflowY"):I(b,"overflowX")},L=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},M=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=L(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&K(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},N=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},O=function(a){return[a.deltaX,a.deltaY]},P=function(a){return a&&"current"in a?a.current:a},Q=0,R=[];let S=(d=function(a){var b=h.useRef([]),c=h.useRef([0,0]),d=h.useRef(),e=h.useState(Q++)[0],f=h.useState(u)[0],g=h.useRef(a);h.useEffect(function(){g.current=a},[a]),h.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(P),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=h.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=N(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=J(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=J(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return M(n,b,a,"h"===n?i:j,!0)},[]),j=h.useCallback(function(a){if(R.length&&R[R.length-1]===f){var c="deltaY"in a?O(a):N(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(P).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=h.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=h.useCallback(function(a){c.current=N(a),d.current=void 0},[]),m=h.useCallback(function(b){k(b.type,O(b),b.target,i(b,a.lockRef.current))},[]),n=h.useCallback(function(b){k(b.type,N(b),b.target,i(b,a.lockRef.current))},[]);h.useEffect(function(){return R.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,H),document.addEventListener("touchmove",j,H),document.addEventListener("touchstart",l,H),function(){R=R.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,H),document.removeEventListener("touchmove",j,H),document.removeEventListener("touchstart",l,H)}},[]);var o=a.removeScrollBar,p=a.inert;return h.createElement(h.Fragment,null,p?h.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?h.createElement(E,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},o.useMedium(d),r);var T=h.forwardRef(function(a,b){return h.createElement(q,f({},a,{ref:b,sideCar:S}))});T.classNames=q.classNames;let U=T},63376:(a,b,c)=>{c.d(b,{Eq:()=>j});var d=new WeakMap,e=new WeakMap,f={},g=0,h=function(a){return a&&(a.host||h(a.parentNode))},i=function(a,b,c,i){var j=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=h(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});f[c]||(f[c]=new WeakMap);var k=f[c],l=[],m=new Set,n=new Set(j),o=function(a){!a||m.has(a)||(m.add(a),o(a.parentNode))};j.forEach(o);var p=function(a){!a||n.has(a)||Array.prototype.forEach.call(a.children,function(a){if(m.has(a))p(a);else try{var b=a.getAttribute(i),f=null!==b&&"false"!==b,g=(d.get(a)||0)+1,h=(k.get(a)||0)+1;d.set(a,g),k.set(a,h),l.push(a),1===g&&f&&e.set(a,!0),1===h&&a.setAttribute(c,"true"),f||a.setAttribute(i,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return p(b),m.clear(),g++,function(){l.forEach(function(a){var b=d.get(a)-1,f=k.get(a)-1;d.set(a,b),k.set(a,f),b||(e.has(a)||a.removeAttribute(i),e.delete(a)),f||a.removeAttribute(c)}),--g||(d=new WeakMap,d=new WeakMap,e=new WeakMap,f={})}},j=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),i(d,e,c,"aria-hidden")):function(){return null}}},65822:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},96963:(a,b,c)=>{c.d(b,{B:()=>i});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useId ".trim().toString()]||(()=>void 0),h=0;function i(a){let[b,c]=e.useState(g());return(0,f.N)(()=>{a||c(a=>a??String(h++))},[a]),a||(b?`radix-${b}`:"")}}};