"use client"

import { useState } from 'react'
import { <PERSON><PERSON> as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON>er } from 'react-syntax-highlighter'
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'

interface CodeBlockProps {
  children: string
  className?: string
  inline?: boolean
}

export function CodeBlock({ children, className, inline }: CodeBlockProps) {
  const [copied, setCopied] = useState(false)
  const { toast } = useToast()
  
  const match = /language-(\w+)/.exec(className || '')
  const language = match ? match[1] : ''
  
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(children)
      setCopied(true)
      toast({
        title: "复制成功",
        description: "代码已复制到剪贴板",
      })
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  if (inline) {
    return (
      <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold">
        {children}
      </code>
    )
  }

  return (
    <div className="relative group">
      <div className="absolute right-2 top-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCopy}
          className={cn(
            "h-8 px-2 bg-background/80 backdrop-blur-sm border-border/50",
            copied && "bg-green-100 border-green-300 text-green-700"
          )}
        >
          <Icon 
            name={copied ? "check" : "copy"} 
            className="h-3 w-3 mr-1" 
          />
          {copied ? "已复制" : "复制"}
        </Button>
      </div>
      
      <SyntaxHighlighter
        style={tomorrow}
        language={language}
        PreTag="div"
        className="rounded-md !mt-0 !mb-0"
        customStyle={{
          margin: 0,
          borderRadius: '0.375rem',
          fontSize: '0.875rem',
          lineHeight: '1.25rem',
        }}
      >
        {children}
      </SyntaxHighlighter>
    </div>
  )
}
