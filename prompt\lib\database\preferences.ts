import { createClient } from '@/lib/supabase/client'
import type { 
  UserPreferences, 
  UserPreferencesInsert, 
  UserPreferencesUpdate,
  DatabaseError 
} from '@/types/database'

const supabase = createClient()

/**
 * 获取用户偏好设置
 */
export async function getUserPreferences(): Promise<UserPreferences | null> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // 如果没有偏好设置，创建默认设置
        return await createDefaultUserPreferences()
      }
      throw error
    }

    return data
  } catch (error) {
    console.error('获取用户偏好设置失败:', error)
    throw new Error('获取用户偏好设置失败')
  }
}

/**
 * 创建默认用户偏好设置
 */
export async function createDefaultUserPreferences(): Promise<UserPreferences> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const defaultPreferences: UserPreferencesInsert = {
      user_id: user.id,
      theme: 'system',
      items_per_page: 12,
      show_usage_count: true,
      auto_copy_feedback: true
    }

    const { data, error } = await supabase
      .from('user_preferences')
      .insert(defaultPreferences)
      .select()
      .single()

    if (error) throw error

    return data
  } catch (error) {
    console.error('创建默认用户偏好设置失败:', error)
    throw new Error('创建默认用户偏好设置失败')
  }
}

/**
 * 更新用户偏好设置
 */
export async function updateUserPreferences(updates: UserPreferencesUpdate): Promise<UserPreferences> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { data, error } = await supabase
      .from('user_preferences')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) throw error

    return data
  } catch (error) {
    console.error('更新用户偏好设置失败:', error)
    throw new Error('更新用户偏好设置失败')
  }
}

/**
 * 更新主题设置
 */
export async function updateTheme(theme: string): Promise<void> {
  try {
    await updateUserPreferences({ theme })
  } catch (error) {
    console.error('更新主题设置失败:', error)
    throw new Error('更新主题设置失败')
  }
}

/**
 * 更新默认分类
 */
export async function updateDefaultCategory(categoryId: string | null): Promise<void> {
  try {
    await updateUserPreferences({ default_category_id: categoryId })
  } catch (error) {
    console.error('更新默认分类失败:', error)
    throw new Error('更新默认分类失败')
  }
}

/**
 * 更新每页显示数量
 */
export async function updateItemsPerPage(itemsPerPage: number): Promise<void> {
  try {
    if (itemsPerPage < 1 || itemsPerPage > 100) {
      throw new Error('每页显示数量必须在1-100之间')
    }

    await updateUserPreferences({ items_per_page: itemsPerPage })
  } catch (error) {
    console.error('更新每页显示数量失败:', error)
    throw new Error('更新每页显示数量失败')
  }
}

/**
 * 更新使用次数显示设置
 */
export async function updateShowUsageCount(showUsageCount: boolean): Promise<void> {
  try {
    await updateUserPreferences({ show_usage_count: showUsageCount })
  } catch (error) {
    console.error('更新使用次数显示设置失败:', error)
    throw new Error('更新使用次数显示设置失败')
  }
}

/**
 * 更新自动复制反馈设置
 */
export async function updateAutoCopyFeedback(autoCopyFeedback: boolean): Promise<void> {
  try {
    await updateUserPreferences({ auto_copy_feedback: autoCopyFeedback })
  } catch (error) {
    console.error('更新自动复制反馈设置失败:', error)
    throw new Error('更新自动复制反馈设置失败')
  }
}

/**
 * 重置用户偏好设置为默认值
 */
export async function resetUserPreferences(): Promise<UserPreferences> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const defaultPreferences: UserPreferencesUpdate = {
      theme: 'system',
      default_category_id: null,
      items_per_page: 12,
      show_usage_count: true,
      auto_copy_feedback: true,
      updated_at: new Date().toISOString()
    }

    const { data, error } = await supabase
      .from('user_preferences')
      .update(defaultPreferences)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) throw error

    return data
  } catch (error) {
    console.error('重置用户偏好设置失败:', error)
    throw new Error('重置用户偏好设置失败')
  }
}

/**
 * 删除用户偏好设置
 */
export async function deleteUserPreferences(): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { error } = await supabase
      .from('user_preferences')
      .delete()
      .eq('user_id', user.id)

    if (error) throw error
  } catch (error) {
    console.error('删除用户偏好设置失败:', error)
    throw new Error('删除用户偏好设置失败')
  }
}

/**
 * 初始化用户数据（首次登录时调用）
 */
export async function initializeUserData(): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    // 检查是否已有偏好设置
    const { data: existingPrefs } = await supabase
      .from('user_preferences')
      .select('user_id')
      .eq('user_id', user.id)
      .single()

    if (!existingPrefs) {
      // 创建默认偏好设置
      await createDefaultUserPreferences()

      // 调用创建默认分类的函数（在seed.sql中定义）
      const { error } = await supabase.rpc('create_default_categories_for_user')
      if (error) {
        console.error('创建默认分类失败:', error)
        // 不抛出错误，因为这不应该阻止用户使用应用
      }
    }
  } catch (error) {
    console.error('初始化用户数据失败:', error)
    // 不抛出错误，因为这不应该阻止用户使用应用
  }
}
