"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@/components/ui/icon'
import { PromptCard } from '@/components/prompt-card'
import { AdvancedSearchModal } from '@/components/advanced-search-modal'
import type { PromptWithDetails } from '@/types/database'

interface SearchResultsProps {
  results: PromptWithDetails[]
  searchQuery: string
  total: number
  isLoading: boolean
  hasMore: boolean
  onLoadMore: () => void
  onPromptView: (promptId: string) => void
  onPromptEdit: (promptId: string) => void
  onPromptDelete: (promptId: string) => void
  onPromptCopy: (content: string, promptId: string) => void
  onAdvancedSearch: (params: any) => void
  onClearSearch: () => void
}

export function SearchResults({
  results,
  searchQuery,
  total,
  isLoading,
  hasMore,
  onLoadMore,
  onPromptView,
  onPromptEdit,
  onPromptDelete,
  onPromptCopy,
  onAdvancedSearch,
  onClearSearch
}: SearchResultsProps) {
  const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false)

  const handleAdvancedSearch = (params: any) => {
    onAdvancedSearch(params)
    setIsAdvancedSearchOpen(false)
  }

  return (
    <div className="space-y-6">
      {/* 搜索结果头部 */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            搜索结果
          </h2>
          <p className="text-sm text-muted-foreground mt-1">
            {searchQuery ? (
              <>
                找到 <span className="font-medium">{total}</span> 个包含 "
                <span className="font-medium text-blue-600">{searchQuery}</span>" 的提示词
              </>
            ) : (
              <>共 <span className="font-medium">{total}</span> 个提示词</>
            )}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAdvancedSearchOpen(true)}
          >
            <Icon name="filter" className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">高级搜索</span>
          </Button>

          {searchQuery && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearSearch}
            >
              <Icon name="times" className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">清除搜索</span>
            </Button>
          )}
        </div>
      </div>

      {/* 搜索建议 */}
      {searchQuery && results.length === 0 && !isLoading && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Icon name="lightbulb" className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                搜索建议
              </h3>
              <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• 检查拼写是否正确</li>
                <li>• 尝试使用更通用的关键词</li>
                <li>• 使用高级搜索进行更精确的筛选</li>
                <li>• 检查是否选择了正确的分类</li>
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* 搜索结果列表 */}
      {results.length > 0 ? (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {results.map((prompt) => (
              <PromptCard
                key={prompt.id}
                id={prompt.id}
                title={prompt.title}
                description={prompt.description || undefined}
                content={prompt.content}
                category={prompt.category}
                tags={prompt.tags}
                usageCount={prompt.usage_count}
                createdAt={prompt.created_at}
                updatedAt={prompt.updated_at}
                onView={onPromptView}
                onEdit={onPromptEdit}
                onDelete={onPromptDelete}
                onCopy={onPromptCopy}
                className="search-result-card"
              />
            ))}
          </div>

          {/* 加载更多 */}
          {hasMore && (
            <div className="text-center">
              <Button
                variant="outline"
                onClick={onLoadMore}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Icon name="spinner" className="h-4 w-4 mr-2 animate-spin" />
                    加载中...
                  </>
                ) : (
                  <>
                    <Icon name="refresh" className="h-4 w-4 mr-2" />
                    加载更多
                  </>
                )}
              </Button>
            </div>
          )}
        </>
      ) : !isLoading ? (
        <div className="text-center py-12">
          <Icon name="search" className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {searchQuery ? '未找到匹配的提示词' : '暂无提示词'}
          </h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery 
              ? '尝试调整搜索关键词或使用高级搜索'
              : '开始创建您的第一个提示词吧'
            }
          </p>
          <div className="flex items-center justify-center gap-2">
            {searchQuery ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => setIsAdvancedSearchOpen(true)}
                >
                  <Icon name="filter" className="h-4 w-4 mr-2" />
                  高级搜索
                </Button>
                <Button onClick={onClearSearch}>
                  <Icon name="times" className="h-4 w-4 mr-2" />
                  清除搜索
                </Button>
              </>
            ) : (
              <Button onClick={() => {/* TODO: 新建提示词 */}}>
                <Icon name="plus" className="h-4 w-4 mr-2" />
                新建提示词
              </Button>
            )}
          </div>
        </div>
      ) : null}

      {/* 加载状态 */}
      {isLoading && results.length === 0 && (
        <div className="text-center py-12">
          <Icon name="spinner" className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">搜索中...</p>
        </div>
      )}

      {/* 高级搜索模态框 */}
      <AdvancedSearchModal
        isOpen={isAdvancedSearchOpen}
        onClose={() => setIsAdvancedSearchOpen(false)}
        onSearch={handleAdvancedSearch}
      />
    </div>
  )
}
