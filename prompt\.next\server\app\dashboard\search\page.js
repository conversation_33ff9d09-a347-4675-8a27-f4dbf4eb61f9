(()=>{var a={};a.id=382,a.ids=[382],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1708:a=>{"use strict";a.exports=require("node:process")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},18326:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,53469)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\search\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,38836)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]}.children,H=["D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\search\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/search/page",pathname:"/dashboard/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/search/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},18541:(a,b,c)=>{Promise.resolve().then(c.bind(c,38407))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},37101:(a,b,c)=>{Promise.resolve().then(c.bind(c,53469))},38407:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>x});var d=c(60687),e=c(43210),f=c(16189),g=c(32945),h=c(32675),i=c(24934),j=c(32418),k=c(130),l=c(37826),m=c(68988),n=c(39390),o=c(59821),p=c(71702),q=c(34257);function r({isOpen:a,onClose:b,onSearch:c,initialParams:f={}}){let[g,h]=(0,e.useState)(""),[k,q]=(0,e.useState)(""),[r,s]=(0,e.useState)(""),[t,u]=(0,e.useState)(""),[v,w]=(0,e.useState)([]),[x,y]=(0,e.useState)(""),[z,A]=(0,e.useState)(""),[B,C]=(0,e.useState)(""),[D,E]=(0,e.useState)(""),[F,G]=(0,e.useState)("updated_at"),[H,I]=(0,e.useState)("desc"),[J,K]=(0,e.useState)([]),[L,M]=(0,e.useState)([]),[N,O]=(0,e.useState)(!1),{toast:P}=(0,p.dj)();(0,e.useEffect)(()=>{f&&(h(f.query||""),q(f.title||""),s(f.content||""),u(f.categoryId||""),w(f.tagIds||[]),y(f.dateFrom||""),A(f.dateTo||""),C(f.usageCountMin?.toString()||""),E(f.usageCountMax?.toString()||""),G(f.sortBy||"updated_at"),I(f.sortOrder||"desc"))},[JSON.stringify(f)]);let Q=a=>{w(b=>b.includes(a)?b.filter(b=>b!==a):[...b,a])},R=L.filter(a=>v.includes(a.id));return(0,d.jsx)(l.lG,{open:a,onOpenChange:b,children:(0,d.jsxs)(l.Cf,{className:"max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col",children:[(0,d.jsxs)(l.c7,{children:[(0,d.jsx)(l.L3,{children:"高级搜索"}),(0,d.jsx)(l.rr,{children:"使用多个条件精确搜索提示词"})]}),N?(0,d.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,d.jsx)(j.Icon,{name:"spinner",className:"h-6 w-6 animate-spin mr-2"}),(0,d.jsx)("span",{children:"加载中..."})]}):(0,d.jsxs)("form",{onSubmit:a=>{a.preventDefault();let d={query:g.trim()||void 0,title:k.trim()||void 0,content:r.trim()||void 0,categoryId:t||void 0,tagIds:v.length>0?v:void 0,dateFrom:x||void 0,dateTo:z||void 0,usageCountMin:B?parseInt(B):void 0,usageCountMax:D?parseInt(D):void 0,sortBy:F,sortOrder:H};c(d),b()},className:"flex-1 overflow-hidden flex flex-col",children:[(0,d.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"query",children:"关键词搜索"}),(0,d.jsx)(m.p,{id:"query",value:g,onChange:a=>h(a.target.value),placeholder:"在标题、描述、内容中搜索"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"title",children:"标题"}),(0,d.jsx)(m.p,{id:"title",value:k,onChange:a=>q(a.target.value),placeholder:"搜索标题"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"content",children:"内容"}),(0,d.jsx)(m.p,{id:"content",value:r,onChange:a=>s(a.target.value),placeholder:"搜索内容"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"category",children:"分类"}),(0,d.jsxs)("select",{id:"category",value:t,onChange:a=>u(a.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,d.jsx)("option",{value:"",children:"所有分类"}),J.map(a=>(0,d.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{children:"标签"}),R.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:R.map(a=>(0,d.jsxs)(o.E,{variant:"secondary",className:"cursor-pointer",style:{backgroundColor:`${a.color}20`,color:a.color},onClick:()=>Q(a.id),children:[a.name,(0,d.jsx)(j.Icon,{name:"times",className:"h-3 w-3 ml-1"})]},a.id))}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 max-h-32 overflow-y-auto",children:L.filter(a=>!v.includes(a.id)).map(a=>(0,d.jsxs)(o.E,{variant:"outline",className:"cursor-pointer hover:bg-gray-100",onClick:()=>Q(a.id),children:[(0,d.jsx)(j.Icon,{name:"plus",className:"h-3 w-3 mr-1"}),a.name]},a.id))})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"dateFrom",children:"创建日期从"}),(0,d.jsx)(m.p,{id:"dateFrom",type:"date",value:x,onChange:a=>y(a.target.value)})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"dateTo",children:"创建日期到"}),(0,d.jsx)(m.p,{id:"dateTo",type:"date",value:z,onChange:a=>A(a.target.value)})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"usageCountMin",children:"最少使用次数"}),(0,d.jsx)(m.p,{id:"usageCountMin",type:"number",min:"0",value:B,onChange:a=>C(a.target.value),placeholder:"0"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"usageCountMax",children:"最多使用次数"}),(0,d.jsx)(m.p,{id:"usageCountMax",type:"number",min:"0",value:D,onChange:a=>E(a.target.value),placeholder:"无限制"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"sortBy",children:"排序字段"}),(0,d.jsxs)("select",{id:"sortBy",value:F,onChange:a=>G(a.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,d.jsx)("option",{value:"updated_at",children:"更新时间"}),(0,d.jsx)("option",{value:"created_at",children:"创建时间"}),(0,d.jsx)("option",{value:"usage_count",children:"使用次数"}),(0,d.jsx)("option",{value:"title",children:"标题"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n.J,{htmlFor:"sortOrder",children:"排序方向"}),(0,d.jsxs)("select",{id:"sortOrder",value:H,onChange:a=>I(a.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,d.jsx)("option",{value:"desc",children:"降序"}),(0,d.jsx)("option",{value:"asc",children:"升序"})]})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,d.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{h(""),q(""),s(""),u(""),w([]),y(""),A(""),C(""),E(""),G("updated_at"),I("desc")},children:[(0,d.jsx)(j.Icon,{name:"refresh",className:"h-4 w-4 mr-2"}),"重置"]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(i.$,{type:"button",variant:"outline",onClick:b,children:"取消"}),(0,d.jsxs)(i.$,{type:"submit",children:[(0,d.jsx)(j.Icon,{name:"search",className:"h-4 w-4 mr-2"}),"搜索"]})]})]})]})]})})}function s({results:a,searchQuery:b,total:c,isLoading:f,hasMore:g,onLoadMore:h,onPromptView:l,onPromptEdit:m,onPromptDelete:n,onPromptCopy:o,onAdvancedSearch:p,onClearSearch:q}){let[s,t]=(0,e.useState)(!1);return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"搜索结果"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:b?(0,d.jsxs)(d.Fragment,{children:["找到 ",(0,d.jsx)("span",{className:"font-medium",children:c}),' 个包含 "',(0,d.jsx)("span",{className:"font-medium text-blue-600",children:b}),'" 的提示词']}):(0,d.jsxs)(d.Fragment,{children:["共 ",(0,d.jsx)("span",{className:"font-medium",children:c})," 个提示词"]})})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>t(!0),children:[(0,d.jsx)(j.Icon,{name:"filter",className:"h-4 w-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"高级搜索"})]}),b&&(0,d.jsxs)(i.$,{variant:"outline",size:"sm",onClick:q,children:[(0,d.jsx)(j.Icon,{name:"times",className:"h-4 w-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"清除搜索"})]})]})]}),b&&0===a.length&&!f&&(0,d.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)(j.Icon,{name:"lightbulb",className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-yellow-800 dark:text-yellow-200 mb-1",children:"搜索建议"}),(0,d.jsxs)("ul",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:[(0,d.jsx)("li",{children:"• 检查拼写是否正确"}),(0,d.jsx)("li",{children:"• 尝试使用更通用的关键词"}),(0,d.jsx)("li",{children:"• 使用高级搜索进行更精确的筛选"}),(0,d.jsx)("li",{children:"• 检查是否选择了正确的分类"})]})]})]})}),a.length>0?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6",children:a.map(a=>(0,d.jsx)(k.z,{id:a.id,title:a.title,description:a.description||void 0,content:a.content,category:a.category,tags:a.tags,usageCount:a.usage_count,createdAt:a.created_at,updatedAt:a.updated_at,onView:l,onEdit:m,onDelete:n,onCopy:o,className:"search-result-card"},a.id))}),g&&(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)(i.$,{variant:"outline",onClick:h,disabled:f,children:f?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(j.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),"加载中..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(j.Icon,{name:"refresh",className:"h-4 w-4 mr-2"}),"加载更多"]})})})]}):f?null:(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(j.Icon,{name:"search",className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:b?"未找到匹配的提示词":"暂无提示词"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:b?"尝试调整搜索关键词或使用高级搜索":"开始创建您的第一个提示词吧"}),(0,d.jsx)("div",{className:"flex items-center justify-center gap-2",children:b?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(i.$,{variant:"outline",onClick:()=>t(!0),children:[(0,d.jsx)(j.Icon,{name:"filter",className:"h-4 w-4 mr-2"}),"高级搜索"]}),(0,d.jsxs)(i.$,{onClick:q,children:[(0,d.jsx)(j.Icon,{name:"times",className:"h-4 w-4 mr-2"}),"清除搜索"]})]}):(0,d.jsxs)(i.$,{onClick:()=>{},children:[(0,d.jsx)(j.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),"新建提示词"]})})]}),f&&0===a.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(j.Icon,{name:"spinner",className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"搜索中..."})]}),(0,d.jsx)(r,{isOpen:s,onClose:()=>t(!1),onSearch:a=>{p(a),t(!1)}})]})}var t=c(8522),u=c(90890),v=c(89904);function w(){let a=(0,f.useSearchParams)().get("q")||"",[b,c]=(0,e.useState)(a),[i,j]=(0,e.useState)([]),[k,l]=(0,e.useState)([]),[m,n]=(0,e.useState)(!1),[o,r]=(0,e.useState)(0),[w,x]=(0,e.useState)(1),[y,z]=(0,e.useState)(!1),[A,B]=(0,e.useState)({}),[C,D]=(0,e.useState)(null),[E,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(!1),[I,J]=(0,e.useState)(null),[K,L]=(0,e.useState)(!1),[M,N]=(0,e.useState)(null),[O,P]=(0,e.useState)(!1),{toast:Q}=(0,p.dj)(),R=(0,e.useCallback)(async()=>{try{let a=await (0,q.FA)(10);l(a)}catch(a){console.error("加载搜索历史失败:",a)}},[]),S=(0,e.useCallback)(async(a,b={})=>{try{n(!0),x(1);let c={query:a.trim()||void 0,...b,limit:12,offset:0};B(c);let d=await (0,q.jG)(c);j(d),r(d.length),z(12===d.length),a.trim()&&(await (0,q.eQ)(a.trim()),await R())}catch(a){console.error("搜索失败:",a)}finally{n(!1)}},[]),T=async a=>{await S("",a)},U=async()=>{if(y&&!m)try{n(!0);let a=w+1,b={...A,offset:(a-1)*12},c=await (0,q.jG)(b);j(a=>[...a,...c]),x(a),z(12===c.length)}catch(a){console.error("加载更多失败:",a),Q({title:"加载失败",description:"加载更多结果时出现错误",variant:"destructive"})}finally{n(!1)}},V=async a=>{try{let b=await (0,q.Fw)(a);b&&(D(b),F(!0))}catch(a){console.error("获取提示词详情失败:",a),Q({title:"加载失败",description:"无法获取提示词详情",variant:"destructive"})}},W=async a=>{try{let b=await (0,q.Fw)(a);b&&(J(b),H(!0))}catch(a){console.error("获取提示词详情失败:",a),Q({title:"加载失败",description:"无法获取提示词详情",variant:"destructive"})}},X=async a=>{let b=i.find(b=>b.id===a);b&&(N(b),L(!0))},Y=async(a,b)=>{try{await (0,q.pB)(b),j(a=>a.map(a=>a.id===b?{...a,usage_count:a.usage_count+1}:a))}catch(a){console.error("更新使用次数失败:",a)}},Z=async()=>{if(M)try{P(!0),await (0,q.Ir)(M.id),Q({title:"删除成功",description:"提示词已成功删除"}),L(!1),N(null),j(a=>a.filter(a=>a.id!==M.id)),r(a=>a-1)}catch(a){console.error("删除提示词失败:",a),Q({title:"删除失败",description:"删除提示词时出现错误",variant:"destructive"})}finally{P(!1)}},$=async()=>{try{await (0,q.Wf)(),l([]),Q({title:"清除成功",description:"搜索历史已清除"})}catch(a){console.error("清除搜索历史失败:",a),Q({title:"清除失败",description:"清除搜索历史时出现错误",variant:"destructive"})}};return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,d.jsx)(g.a,{}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto p-6",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)("div",{className:"max-w-2xl",children:(0,d.jsx)(h.I,{value:b,onChange:c,onSearch:S,searchHistory:k,onClearHistory:$,placeholder:"搜索提示词..."})})}),(0,d.jsx)(s,{results:i,searchQuery:b,total:o,isLoading:m,hasMore:y,onLoadMore:U,onPromptView:V,onPromptEdit:W,onPromptDelete:X,onPromptCopy:Y,onAdvancedSearch:T,onClearSearch:()=>{c(""),j([]),r(0),x(1),z(!1),B({})}})]}),(0,d.jsx)(t.n,{prompt:C,isOpen:E,onClose:()=>{F(!1),D(null)},onEdit:W,onDelete:X,onCopy:Y}),(0,d.jsx)(u.G,{prompt:I,isOpen:G,onClose:()=>{H(!1),J(null)},onSuccess:()=>{(A.query||Object.keys(A).length>1)&&S(b,A)}}),(0,d.jsx)(v.o,{isOpen:K,onClose:()=>{L(!1),N(null)},onConfirm:Z,title:"删除提示词",description:"此操作无法撤销，确定要删除这个提示词吗？",itemName:M?.title,isLoading:O})]})}function x(){return(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:"加载中..."}),children:(0,d.jsx)(w,{})})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},53469:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Cursor Project\\\\prompy augment\\\\prompt\\\\app\\\\dashboard\\\\search\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\app\\dashboard\\search\\page.tsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:a=>{"use strict";a.exports=require("node:url")},74075:a=>{"use strict";a.exports=require("zlib")},76760:a=>{"use strict";a.exports=require("node:path")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,519,522,814,884,121,438,780,568,796,924],()=>b(b.s=18326));module.exports=c})();