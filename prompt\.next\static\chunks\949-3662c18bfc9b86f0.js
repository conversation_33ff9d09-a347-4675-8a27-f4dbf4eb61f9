"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[949],{5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9428:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},11275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(12115),o=n(52712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},25519:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(12115),o=n(6101),i=n(63655),a=n(39033),l=n(95155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,a.c)(v),E=(0,a.c)(g),R=r.useRef(null),C=(0,o.s)(t,e=>x(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(A.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:h(R.current,{select:!0})},t=function(e){if(A.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(R.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,A.paused]),r.useEffect(()=>{if(w){m.add(A);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),m.remove(A)},0)}}},[w,b,E,A]);let S=r.useCallback(e=>{if(!n&&!d||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,A.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:S})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null==(n=(e=v(e,t))[0])||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},29020:(e,t,n)=>{n.d(t,{H_:()=>nU,UC:()=>nG,YJ:()=>nz,q7:()=>nX,VF:()=>nZ,JU:()=>nV,ZL:()=>nH,z6:()=>nY,hN:()=>nq,bL:()=>nK,wv:()=>n$,Pb:()=>nJ,G5:()=>n0,ZP:()=>nQ,l9:()=>nW});var r=n(12115),o=n(85185),i=n(6101),a=n(46081),l=n(5845),u=n(63655),c=n(37328),s=n(95155),d=r.createContext(void 0);function f(e){let t=r.useContext(d);return e||t||"ltr"}var p=n(19178),h=n(92293),m=n(25519),v=n(61285);let g=["top","right","bottom","left"],y=Math.min,w=Math.max,x=Math.round,b=Math.floor,E=e=>({x:e,y:e}),R={left:"right",right:"left",bottom:"top",top:"bottom"},C={start:"end",end:"start"};function A(e,t){return"function"==typeof e?e(t):e}function S(e){return e.split("-")[0]}function M(e){return e.split("-")[1]}function k(e){return"x"===e?"y":"x"}function T(e){return"y"===e?"height":"width"}let P=new Set(["top","bottom"]);function j(e){return P.has(S(e))?"y":"x"}function D(e){return e.replace(/start|end/g,e=>C[e])}let L=["left","right"],O=["right","left"],N=["top","bottom"],I=["bottom","top"];function F(e){return e.replace(/left|right|bottom|top/g,e=>R[e])}function _(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function B(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function K(e,t,n){let r,{reference:o,floating:i}=e,a=j(t),l=k(j(t)),u=T(l),c=S(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(M(t)){case"start":r[l]-=p*(n&&s?-1:1);break;case"end":r[l]+=p*(n&&s?-1:1)}return r}let W=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=K(c,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=K(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function H(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=A(t,e),h=_(p),m=l[f?"floating"===d?"reference":"floating":d],v=B(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=B(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function G(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function z(e){return g.some(t=>e[t]>=0)}let V=new Set(["left","top"]);async function X(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=S(n),l=M(n),u="y"===j(n),c=V.has(a)?-1:1,s=i&&u?-1:1,d=A(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function U(){return"undefined"!=typeof window}function Y(e){return $(e)?(e.nodeName||"").toLowerCase():"#document"}function q(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Z(e){var t;return null==(t=($(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function $(e){return!!U()&&(e instanceof Node||e instanceof q(e).Node)}function J(e){return!!U()&&(e instanceof Element||e instanceof q(e).Element)}function Q(e){return!!U()&&(e instanceof HTMLElement||e instanceof q(e).HTMLElement)}function ee(e){return!!U()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof q(e).ShadowRoot)}let et=new Set(["inline","contents"]);function en(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ep(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!et.has(o)}let er=new Set(["table","td","th"]),eo=[":popover-open",":modal"];function ei(e){return eo.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let ea=["transform","translate","scale","rotate","perspective"],el=["transform","translate","scale","rotate","perspective","filter"],eu=["paint","layout","strict","content"];function ec(e){let t=es(),n=J(e)?ep(e):e;return ea.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||el.some(e=>(n.willChange||"").includes(e))||eu.some(e=>(n.contain||"").includes(e))}function es(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ed=new Set(["html","body","#document"]);function ef(e){return ed.has(Y(e))}function ep(e){return q(e).getComputedStyle(e)}function eh(e){return J(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function em(e){if("html"===Y(e))return e;let t=e.assignedSlot||e.parentNode||ee(e)&&e.host||Z(e);return ee(t)?t.host:t}function ev(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=em(t);return ef(n)?t.ownerDocument?t.ownerDocument.body:t.body:Q(n)&&en(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=q(o);if(i){let e=eg(a);return t.concat(a,a.visualViewport||[],en(o)?o:[],e&&n?ev(e):[])}return t.concat(o,ev(o,[],n))}function eg(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ey(e){let t=ep(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=Q(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=x(n)!==i||x(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function ew(e){return J(e)?e:e.contextElement}function ex(e){let t=ew(e);if(!Q(t))return E(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ey(t),a=(i?x(n.width):n.width)/r,l=(i?x(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let eb=E(0);function eE(e){let t=q(e);return es()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eb}function eR(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ew(e),l=E(1);t&&(r?J(r)&&(l=ex(r)):l=ex(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===q(a))&&o)?eE(a):E(0),c=(i.left+u.x)/l.x,s=(i.top+u.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=q(a),t=r&&J(r)?q(r):r,n=e,o=eg(n);for(;o&&r&&t!==n;){let e=ex(o),t=o.getBoundingClientRect(),r=ep(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=a,o=eg(n=q(o))}}return B({width:d,height:f,x:c,y:s})}function eC(e,t){let n=eh(e).scrollLeft;return t?t.left+n:eR(Z(e)).left+n}function eA(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eC(e,r)),y:r.top+t.scrollTop}}let eS=new Set(["absolute","fixed"]);function eM(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=q(e),r=Z(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=es();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=Z(e),n=eh(e),r=e.ownerDocument.body,o=w(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=w(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+eC(e),l=-n.scrollTop;return"rtl"===ep(r).direction&&(a+=w(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(Z(e));else if(J(t))r=function(e,t){let n=eR(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Q(e)?ex(e):E(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=eE(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return B(r)}function ek(e){return"static"===ep(e).position}function eT(e,t){if(!Q(e)||"fixed"===ep(e).position)return null;if(t)return t(e);let n=e.offsetParent;return Z(e)===n&&(n=n.ownerDocument.body),n}function eP(e,t){var n;let r=q(e);if(ei(e))return r;if(!Q(e)){let t=em(e);for(;t&&!ef(t);){if(J(t)&&!ek(t))return t;t=em(t)}return r}let o=eT(e,t);for(;o&&(n=o,er.has(Y(n)))&&ek(o);)o=eT(o,t);return o&&ef(o)&&ek(o)&&!ec(o)?r:o||function(e){let t=em(e);for(;Q(t)&&!ef(t);){if(ec(t))return t;if(ei(t))break;t=em(t)}return null}(e)||r}let ej=async function(e){let t=this.getOffsetParent||eP,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=Q(t),o=Z(t),i="fixed"===n,a=eR(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=E(0);if(r||!r&&!i)if(("body"!==Y(t)||en(o))&&(l=eh(t)),r){let e=eR(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eC(o));i&&!r&&o&&(u.x=eC(o));let c=!o||r||i?E(0):eA(o,l);return{x:a.left+l.scrollLeft-u.x-c.x,y:a.top+l.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eD={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=Z(r),l=!!t&&ei(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},c=E(1),s=E(0),d=Q(r);if((d||!d&&!i)&&(("body"!==Y(r)||en(a))&&(u=eh(r)),Q(r))){let e=eR(r);c=ex(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!a||d||i?E(0):eA(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:Z,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ei(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=ev(e,[],!1).filter(e=>J(e)&&"body"!==Y(e)),o=null,i="fixed"===ep(e).position,a=i?em(e):e;for(;J(a)&&!ef(a);){let t=ep(a),n=ec(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eS.has(o.position)||en(a)&&!n&&function e(t,n){let r=em(t);return!(r===n||!J(r)||ef(r))&&("fixed"===ep(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=em(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eM(t,n,o);return e.top=w(r.top,e.top),e.right=y(r.right,e.right),e.bottom=y(r.bottom,e.bottom),e.left=w(r.left,e.left),e},eM(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eP,getElementRects:ej,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ey(e);return{width:t,height:n}},getScale:ex,isElement:J,isRTL:function(e){return"rtl"===ep(e).direction}};function eL(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eO=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:u}=t,{element:c,padding:s=0}=A(e,t)||{};if(null==c)return{};let d=_(s),f={x:n,y:r},p=k(j(o)),h=T(p),m=await a.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",x=i.reference[h]+i.reference[p]-f[p]-i.floating[h],b=f[p]-i.reference[p],E=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),R=E?E[g]:0;R&&await (null==a.isElement?void 0:a.isElement(E))||(R=l.floating[g]||i.floating[h]);let C=R/2-m[h]/2-1,S=y(d[v?"top":"left"],C),P=y(d[v?"bottom":"right"],C),D=R-m[h]-P,L=R/2-m[h]/2+(x/2-b/2),O=w(S,y(L,D)),N=!u.arrow&&null!=M(o)&&L!==O&&i.reference[h]/2-(L<S?S:P)-m[h]/2<0,I=N?L<S?L-S:L-D:0;return{[p]:f[p]+I,data:{[p]:O,centerOffset:L-O-I,...N&&{alignmentOffset:I}},reset:N}}});var eN=n(47650),eI="undefined"!=typeof document?r.useLayoutEffect:function(){};function eF(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eF(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eF(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e_(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eB(e,t){let n=e_(e);return Math.round(t*n)/n}function eK(e){let t=r.useRef(e);return eI(()=>{t.current=e}),t}var eW=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,s.jsx)(u.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,s.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eW.displayName="Arrow";var eH=n(39033),eG=n(52712),ez=n(11275),eV="Popper",[eX,eU]=(0,a.A)(eV),[eY,eq]=eX(eV),eZ=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,s.jsx)(eY,{scope:t,anchor:o,onAnchorChange:i,children:n})};eZ.displayName=eV;var e$="PopperAnchor",eJ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...a}=e,l=eq(e$,n),c=r.useRef(null),d=(0,i.s)(t,c);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||c.current)}),o?null:(0,s.jsx)(u.sG.div,{...a,ref:d})});eJ.displayName=e$;var eQ="PopperContent",[e0,e1]=eX(eQ),e2=r.forwardRef((e,t)=>{var n,o,a,l,c,d,f,p;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:x=0,arrowPadding:E=0,avoidCollisions:R=!0,collisionBoundary:C=[],collisionPadding:P=0,sticky:_="partial",hideWhenDetached:B=!1,updatePositionStrategy:K="optimized",onPlaced:U,...Y}=e,q=eq(eQ,h),[$,J]=r.useState(null),Q=(0,i.s)(t,e=>J(e)),[ee,et]=r.useState(null),en=(0,ez.X)(ee),er=null!=(f=null==en?void 0:en.width)?f:0,eo=null!=(p=null==en?void 0:en.height)?p:0,ei="number"==typeof P?P:{top:0,right:0,bottom:0,left:0,...P},ea=Array.isArray(C)?C:[C],el=ea.length>0,eu={padding:ei,boundary:ea.filter(e4),altBoundary:el},{refs:ec,floatingStyles:es,placement:ed,isPositioned:ef,middlewareData:ep}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);eF(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),x=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),b=a||m,E=l||g,R=r.useRef(null),C=r.useRef(null),A=r.useRef(d),S=null!=c,M=eK(c),k=eK(i),T=eK(s),P=r.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};k.current&&(e.platform=k.current),((e,t,n)=>{let r=new Map,o={platform:eD,...n},i={...o.platform,_c:r};return W(e,t,{...o,platform:i})})(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};j.current&&!eF(A.current,t)&&(A.current=t,eN.flushSync(()=>{f(t)}))})},[p,t,n,k,T]);eI(()=>{!1===s&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let j=r.useRef(!1);eI(()=>(j.current=!0,()=>{j.current=!1}),[]),eI(()=>{if(b&&(R.current=b),E&&(C.current=E),b&&E){if(M.current)return M.current(b,E,P);P()}},[b,E,P,M,S]);let D=r.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:x}),[w,x]),L=r.useMemo(()=>({reference:b,floating:E}),[b,E]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!L.floating)return e;let t=eB(L.floating,d.x),r=eB(L.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...e_(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,L.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:D,elements:L,floatingStyles:O}),[d,P,D,L,O])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=ew(e),d=i||a?[...s?ev(s):[],...ev(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=Z(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,u){void 0===l&&(l=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(l||t(),!f||!p)return;let h=b(d),m=b(o.clientWidth-(s+f)),v={rootMargin:-h+"px "+-m+"px "+-b(o.clientHeight-(d+p))+"px "+-b(s)+"px",threshold:w(0,y(1,u))||1},g=!0;function x(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eL(c,e.getBoundingClientRect())||a(),g=!1}try{r=new IntersectionObserver(x,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,v)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?eR(e):null;return c&&function t(){let r=eR(e);m&&!eL(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===K})},elements:{reference:q.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await X(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}))({mainAxis:v+eo,alignmentAxis:x}),R&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=A(e,t),c={x:n,y:r},s=await H(t,u),d=j(S(o)),f=k(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=w(n,y(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=w(n,y(h,r))}let m=l.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===_?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=A(e,t),s={x:n,y:r},d=j(o),f=k(d),p=s[f],h=s[d],m=A(l,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=V.has(S(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}))():void 0,...eu}),R&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=A(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=S(l),b=j(s),E=S(s)===s,R=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=m||(E||!y?[F(s)]:function(e){let t=F(e);return[D(e),t,D(t)]}(s)),P="none"!==g;!m&&P&&C.push(...function(e,t,n,r){let o=M(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?O:L;return t?L:O;case"left":case"right":return t?N:I;default:return[]}}(S(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(D)))),i}(s,y,g,R));let _=[s,...C],B=await H(t,w),K=[],W=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&K.push(B[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=M(e),o=k(j(e)),i=T(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=F(a)),[a,F(a)]}(l,c,R);K.push(B[e[0]],B[e[1]])}if(W=[...W,{placement:l,overflows:K}],!K.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=_[e];if(t&&("alignment"!==h||b===j(t)||W.every(e=>e.overflows[0]>0&&j(e.placement)===b)))return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=W.filter(e=>{if(P){let t=j(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...eu}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:a,rects:l,platform:u,elements:c}=t,{apply:s=()=>{},...d}=A(e,t),f=await H(t,d),p=S(a),h=M(a),m="y"===j(a),{width:v,height:g}=l.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let x=g-f.top-f.bottom,b=v-f.left-f.right,E=y(g-f[o],x),R=y(v-f[i],b),C=!t.middlewareData.shift,k=E,T=R;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(T=b),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=x),C&&!h){let e=w(f.left,0),t=w(f.right,0),n=w(f.top,0),r=w(f.bottom,0);m?T=v-2*(0!==e||0!==t?e+t:w(f.left,f.right)):k=g-2*(0!==n||0!==r?n+r:w(f.top,f.bottom))}await s({...t,availableWidth:T,availableHeight:k});let P=await u.getDimensions(c.floating);return v!==P.width||g!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...eu,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),ee&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eO({element:n.current,padding:r}).fn(t):{}:n?eO({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:ee,padding:E}),e3({arrowWidth:er,arrowHeight:eo}),B&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=A(e,t);switch(r){case"referenceHidden":{let e=G(await H(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:z(e)}}}case"escaped":{let e=G(await H(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:z(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...eu})]}),[eh,em]=e8(ed),eg=(0,eH.c)(U);(0,eG.N)(()=>{ef&&(null==eg||eg())},[ef,eg]);let ey=null==(n=ep.arrow)?void 0:n.x,ex=null==(o=ep.arrow)?void 0:o.y,eb=(null==(a=ep.arrow)?void 0:a.centerOffset)!==0,[eE,eC]=r.useState();return(0,eG.N)(()=>{$&&eC(window.getComputedStyle($).zIndex)},[$]),(0,s.jsx)("div",{ref:ec.setFloating,"data-radix-popper-content-wrapper":"",style:{...es,transform:ef?es.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eE,"--radix-popper-transform-origin":[null==(l=ep.transformOrigin)?void 0:l.x,null==(c=ep.transformOrigin)?void 0:c.y].join(" "),...(null==(d=ep.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,s.jsx)(e0,{scope:h,placedSide:eh,onArrowChange:et,arrowX:ey,arrowY:ex,shouldHideArrow:eb,children:(0,s.jsx)(u.sG.div,{"data-side":eh,"data-align":em,...Y,ref:Q,style:{...Y.style,animation:ef?void 0:"none"}})})})});e2.displayName=eQ;var e9="PopperArrow",e5={top:"bottom",right:"left",bottom:"top",left:"right"},e6=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e1(e9,n),i=e5[o.placedSide];return(0,s.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,s.jsx)(eW,{...r,ref:t,style:{...r.style,display:"block"}})})});function e4(e){return null!==e}e6.displayName=e9;var e3=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=e8(l),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(a=null==(o=c.arrow)?void 0:o.y)?a:0)+f/2,y="",w="";return"bottom"===p?(y=s?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(y=s?m:"".concat(v,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function e8(e){let[t,n="center"]=e.split("-");return[t,n]}var e7=n(34378),te=n(28905),tt="rovingFocusGroup.onEntryFocus",tn={bubbles:!1,cancelable:!0},tr="RovingFocusGroup",[to,ti,ta]=(0,c.N)(tr),[tl,tu]=(0,a.A)(tr,[ta]),[tc,ts]=tl(tr),td=r.forwardRef((e,t)=>(0,s.jsx)(to.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(to.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(tf,{...e,ref:t})})}));td.displayName=tr;var tf=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:c=!1,dir:d,currentTabStopId:p,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:m,onEntryFocus:v,preventScrollOnEntryFocus:g=!1,...y}=e,w=r.useRef(null),x=(0,i.s)(t,w),b=f(d),[E,R]=(0,l.i)({prop:p,defaultProp:null!=h?h:null,onChange:m,caller:tr}),[C,A]=r.useState(!1),S=(0,eH.c)(v),M=ti(n),k=r.useRef(!1),[T,P]=r.useState(0);return r.useEffect(()=>{let e=w.current;if(e)return e.addEventListener(tt,S),()=>e.removeEventListener(tt,S)},[S]),(0,s.jsx)(tc,{scope:n,orientation:a,dir:b,loop:c,currentTabStopId:E,onItemFocus:r.useCallback(e=>R(e),[R]),onItemShiftTab:r.useCallback(()=>A(!0),[]),onFocusableItemAdd:r.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>P(e=>e-1),[]),children:(0,s.jsx)(u.sG.div,{tabIndex:C||0===T?-1:0,"data-orientation":a,...y,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!C){let t=new CustomEvent(tt,tn);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=M().filter(e=>e.focusable);tv([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),g)}}k.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>A(!1))})})}),tp="RovingFocusGroupItem",th=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,children:c,...d}=e,f=(0,v.B)(),p=l||f,h=ts(tp,n),m=h.currentTabStopId===p,g=ti(n),{onFocusableItemAdd:y,onFocusableItemRemove:w,currentTabStopId:x}=h;return r.useEffect(()=>{if(i)return y(),()=>w()},[i,y,w]),(0,s.jsx)(to.ItemSlot,{scope:n,id:p,focusable:i,active:a,children:(0,s.jsx)(u.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?h.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tm[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>tv(n))}}),children:"function"==typeof c?c({isCurrentTabStop:m,hasTabStop:null!=x}):c})})});th.displayName=tp;var tm={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tv(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var tg=n(99708),ty=n(38168),tw=n(93795),tx=["Enter"," "],tb=["ArrowUp","PageDown","End"],tE=["ArrowDown","PageUp","Home",...tb],tR={ltr:[...tx,"ArrowRight"],rtl:[...tx,"ArrowLeft"]},tC={ltr:["ArrowLeft"],rtl:["ArrowRight"]},tA="Menu",[tS,tM,tk]=(0,c.N)(tA),[tT,tP]=(0,a.A)(tA,[tk,eU,tu]),tj=eU(),tD=tu(),[tL,tO]=tT(tA),[tN,tI]=tT(tA),tF=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=tj(t),[c,d]=r.useState(null),p=r.useRef(!1),h=(0,eH.c)(a),m=f(i);return r.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,s.jsx)(eZ,{...u,children:(0,s.jsx)(tL,{scope:t,open:n,onOpenChange:h,content:c,onContentChange:d,children:(0,s.jsx)(tN,{scope:t,onClose:r.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:p,dir:m,modal:l,children:o})})})};tF.displayName=tA;var t_=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=tj(n);return(0,s.jsx)(eJ,{...o,...r,ref:t})});t_.displayName="MenuAnchor";var tB="MenuPortal",[tK,tW]=tT(tB,{forceMount:void 0}),tH=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=tO(tB,t);return(0,s.jsx)(tK,{scope:t,forceMount:n,children:(0,s.jsx)(te.C,{present:n||i.open,children:(0,s.jsx)(e7.Z,{asChild:!0,container:o,children:r})})})};tH.displayName=tB;var tG="MenuContent",[tz,tV]=tT(tG),tX=r.forwardRef((e,t)=>{let n=tW(tG,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=tO(tG,e.__scopeMenu),a=tI(tG,e.__scopeMenu);return(0,s.jsx)(tS.Provider,{scope:e.__scopeMenu,children:(0,s.jsx)(te.C,{present:r||i.open,children:(0,s.jsx)(tS.Slot,{scope:e.__scopeMenu,children:a.modal?(0,s.jsx)(tU,{...o,ref:t}):(0,s.jsx)(tY,{...o,ref:t})})})})}),tU=r.forwardRef((e,t)=>{let n=tO(tG,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,ty.Eq)(e)},[]),(0,s.jsx)(tZ,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),tY=r.forwardRef((e,t)=>{let n=tO(tG,e.__scopeMenu);return(0,s.jsx)(tZ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),tq=(0,tg.TL)("MenuContent.ScrollLock"),tZ=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:d,onEntryFocus:f,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,disableOutsideScroll:b,...E}=e,R=tO(tG,n),C=tI(tG,n),A=tj(n),S=tD(n),M=tM(n),[k,T]=r.useState(null),P=r.useRef(null),j=(0,i.s)(t,P,R.onContentChange),D=r.useRef(0),L=r.useRef(""),O=r.useRef(0),N=r.useRef(null),I=r.useRef("right"),F=r.useRef(0),_=b?tw.A:r.Fragment;r.useEffect(()=>()=>window.clearTimeout(D.current),[]),(0,h.Oh)();let B=r.useCallback(e=>{var t,n;return I.current===(null==(t=N.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=N.current)?void 0:n.area)},[]);return(0,s.jsx)(tz,{scope:n,searchRef:L,onItemEnter:r.useCallback(e=>{B(e)&&e.preventDefault()},[B]),onItemLeave:r.useCallback(e=>{var t;B(e)||(null==(t=P.current)||t.focus(),T(null))},[B]),onTriggerLeave:r.useCallback(e=>{B(e)&&e.preventDefault()},[B]),pointerGraceTimerRef:O,onPointerGraceIntentChange:r.useCallback(e=>{N.current=e},[]),children:(0,s.jsx)(_,{...b?{as:tq,allowPinchZoom:!0}:void 0,children:(0,s.jsx)(m.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{var t;e.preventDefault(),null==(t=P.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,s.jsx)(p.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,children:(0,s.jsx)(td,{asChild:!0,...S,dir:C.dir,orientation:"vertical",loop:a,currentTabStopId:k,onCurrentTabStopIdChange:T,onEntryFocus:(0,o.m)(f,e=>{C.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,s.jsx)(e2,{role:"menu","aria-orientation":"vertical","data-state":nh(R.open),"data-radix-menu-content":"",dir:C.dir,...A,...E,ref:j,style:{outline:"none",...E.style},onKeyDown:(0,o.m)(E.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&(e=>{var t,n;let r=L.current+e,o=M().filter(e=>!e.disabled),i=document.activeElement,a=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,a),u=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){L.current=t,window.clearTimeout(D.current),""!==t&&(D.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())})(e.key));let o=P.current;if(e.target!==o||!tE.includes(e.key))return;e.preventDefault();let i=M().filter(e=>!e.disabled).map(e=>e.ref.current);tb.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(D.current),L.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,ng(e=>{let t=e.target,n=F.current!==e.clientX;e.currentTarget.contains(t)&&n&&(I.current=e.clientX>F.current?"right":"left",F.current=e.clientX)}))})})})})})})});tX.displayName=tG;var t$=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,s.jsx)(u.sG.div,{role:"group",...r,ref:t})});t$.displayName="MenuGroup";var tJ=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,s.jsx)(u.sG.div,{...r,ref:t})});tJ.displayName="MenuLabel";var tQ="MenuItem",t0="menu.itemSelect",t1=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,c=r.useRef(null),d=tI(tQ,e.__scopeMenu),f=tV(tQ,e.__scopeMenu),p=(0,i.s)(t,c),h=r.useRef(!1);return(0,s.jsx)(t2,{...l,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(t0,{bubbles:!0,cancelable:!0});e.addEventListener(t0,e=>null==a?void 0:a(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?h.current=!1:d.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),h.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;h.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;n||t&&" "===e.key||tx.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});t1.displayName=tQ;var t2=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...c}=e,d=tV(tQ,n),f=tD(n),p=r.useRef(null),h=(0,i.s)(t,p),[m,v]=r.useState(!1),[g,y]=r.useState("");return r.useEffect(()=>{let e=p.current;if(e){var t;y((null!=(t=e.textContent)?t:"").trim())}},[c.children]),(0,s.jsx)(tS.ItemSlot,{scope:n,disabled:a,textValue:null!=l?l:g,children:(0,s.jsx)(th,{asChild:!0,...f,focusable:!a,children:(0,s.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:h,onPointerMove:(0,o.m)(e.onPointerMove,ng(e=>{a?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ng(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),t9=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,s.jsx)(nt,{scope:e.__scopeMenu,checked:n,children:(0,s.jsx)(t1,{role:"menuitemcheckbox","aria-checked":nm(n)?"mixed":n,...i,ref:t,"data-state":nv(n),onSelect:(0,o.m)(i.onSelect,()=>null==r?void 0:r(!!nm(n)||!n),{checkForDefaultPrevented:!1})})})});t9.displayName="MenuCheckboxItem";var t5="MenuRadioGroup",[t6,t4]=tT(t5,{value:void 0,onValueChange:()=>{}}),t3=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,eH.c)(r);return(0,s.jsx)(t6,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,s.jsx)(t$,{...o,ref:t})})});t3.displayName=t5;var t8="MenuRadioItem",t7=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=t4(t8,e.__scopeMenu),a=n===i.value;return(0,s.jsx)(nt,{scope:e.__scopeMenu,checked:a,children:(0,s.jsx)(t1,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":nv(a),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null==(e=i.onValueChange)?void 0:e.call(i,n)},{checkForDefaultPrevented:!1})})})});t7.displayName=t8;var ne="MenuItemIndicator",[nt,nn]=tT(ne,{checked:!1}),nr=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=nn(ne,n);return(0,s.jsx)(te.C,{present:r||nm(i.checked)||!0===i.checked,children:(0,s.jsx)(u.sG.span,{...o,ref:t,"data-state":nv(i.checked)})})});nr.displayName=ne;var no=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,s.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});no.displayName="MenuSeparator";var ni=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=tj(n);return(0,s.jsx)(e6,{...o,...r,ref:t})});ni.displayName="MenuArrow";var na="MenuSub",[nl,nu]=tT(na),nc=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:i}=e,a=tO(na,t),l=tj(t),[u,c]=r.useState(null),[d,f]=r.useState(null),p=(0,eH.c)(i);return r.useEffect(()=>(!1===a.open&&p(!1),()=>p(!1)),[a.open,p]),(0,s.jsx)(eZ,{...l,children:(0,s.jsx)(tL,{scope:t,open:o,onOpenChange:p,content:d,onContentChange:f,children:(0,s.jsx)(nl,{scope:t,contentId:(0,v.B)(),triggerId:(0,v.B)(),trigger:u,onTriggerChange:c,children:n})})})};nc.displayName=na;var ns="MenuSubTrigger",nd=r.forwardRef((e,t)=>{let n=tO(ns,e.__scopeMenu),a=tI(ns,e.__scopeMenu),l=nu(ns,e.__scopeMenu),u=tV(ns,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:f}=u,p={__scopeMenu:e.__scopeMenu},h=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>h,[h]),r.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),f(null)}},[d,f]),(0,s.jsx)(t_,{asChild:!0,...p,children:(0,s.jsx)(t2,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":nh(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,ng(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),h()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ng(e=>{var t,r;h();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,i="right"===t,a=o[i?"left":"right"],l=o[i?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:a,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&tR[a.dir].includes(t.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),t.preventDefault()}})})})});nd.displayName=ns;var nf="MenuSubContent",np=r.forwardRef((e,t)=>{let n=tW(tG,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=tO(tG,e.__scopeMenu),c=tI(tG,e.__scopeMenu),d=nu(nf,e.__scopeMenu),f=r.useRef(null),p=(0,i.s)(t,f);return(0,s.jsx)(tS.Provider,{scope:e.__scopeMenu,children:(0,s.jsx)(te.C,{present:a||u.open,children:(0,s.jsx)(tS.Slot,{scope:e.__scopeMenu,children:(0,s.jsx)(tZ,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:p,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null==(t=f.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=tC[c.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null==(r=d.trigger)||r.focus(),e.preventDefault()}})})})})})});function nh(e){return e?"open":"closed"}function nm(e){return"indeterminate"===e}function nv(e){return nm(e)?"indeterminate":e?"checked":"unchecked"}function ng(e){return t=>"mouse"===t.pointerType?e(t):void 0}np.displayName=nf;var ny="DropdownMenu",[nw,nx]=(0,a.A)(ny,[tP]),nb=tP(),[nE,nR]=nw(ny),nC=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,d=nb(t),f=r.useRef(null),[p,h]=(0,l.i)({prop:i,defaultProp:null!=a&&a,onChange:u,caller:ny});return(0,s.jsx)(nE,{scope:t,triggerId:(0,v.B)(),triggerRef:f,contentId:(0,v.B)(),open:p,onOpenChange:h,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),modal:c,children:(0,s.jsx)(tF,{...d,open:p,onOpenChange:h,dir:o,modal:c,children:n})})};nC.displayName=ny;var nA="DropdownMenuTrigger",nS=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=nR(nA,n),c=nb(n);return(0,s.jsx)(t_,{asChild:!0,...c,children:(0,s.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});nS.displayName=nA;var nM=e=>{let{__scopeDropdownMenu:t,...n}=e,r=nb(t);return(0,s.jsx)(tH,{...r,...n})};nM.displayName="DropdownMenuPortal";var nk="DropdownMenuContent",nT=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=nR(nk,n),l=nb(n),u=r.useRef(!1);return(0,s.jsx)(tX,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=a.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nT.displayName=nk;var nP=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(t$,{...o,...r,ref:t})});nP.displayName="DropdownMenuGroup";var nj=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(tJ,{...o,...r,ref:t})});nj.displayName="DropdownMenuLabel";var nD=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(t1,{...o,...r,ref:t})});nD.displayName="DropdownMenuItem";var nL=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(t9,{...o,...r,ref:t})});nL.displayName="DropdownMenuCheckboxItem";var nO=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(t3,{...o,...r,ref:t})});nO.displayName="DropdownMenuRadioGroup";var nN=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(t7,{...o,...r,ref:t})});nN.displayName="DropdownMenuRadioItem";var nI=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(nr,{...o,...r,ref:t})});nI.displayName="DropdownMenuItemIndicator";var nF=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(no,{...o,...r,ref:t})});nF.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(ni,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var n_=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(nd,{...o,...r,ref:t})});n_.displayName="DropdownMenuSubTrigger";var nB=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nb(n);return(0,s.jsx)(np,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nB.displayName="DropdownMenuSubContent";var nK=nC,nW=nS,nH=nM,nG=nT,nz=nP,nV=nj,nX=nD,nU=nL,nY=nO,nq=nN,nZ=nI,n$=nF,nJ=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,a=nb(t),[u,c]=(0,l.i)({prop:r,defaultProp:null!=i&&i,onChange:o,caller:"DropdownMenuSub"});return(0,s.jsx)(nc,{...a,open:u,onOpenChange:c,children:n})},nQ=n_,n0=nB},35695:(e,t,n)=>{var r=n(18999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},38168:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=new WeakMap,o=new WeakMap,i={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,n,u){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(u),i=null!==t&&"false"!==t,a=(r.get(e)||0)+1,l=(s.get(e)||0)+1;r.set(e,a),s.set(e,l),d.push(e),1===a&&i&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),i||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),a++,function(){d.forEach(function(e){var t=r.get(e)-1,i=s.get(e)-1;r.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),i||e.removeAttribute(n)}),--a||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),u(r,o,n,"aria-hidden")):function(){return null}}},42148:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]])},61285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(12115),i=n(52712),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},62098:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},92293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(12115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:a()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93509:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},93795:(e,t,n)=>{n.d(t,{A:()=>X});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(12115)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=p),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return o.options=i({async:!0,ssr:!1},e),o}(),m=function(){},v=l.forwardRef(function(e,t){var n,r,o,u,c=l.useRef(null),p=l.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,E=e.enabled,R=e.shards,C=e.sideCar,A=e.noRelative,S=e.noIsolation,M=e.inert,k=e.allowPinchZoom,T=e.as,P=e.gapMode,j=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),L=i(i({},j),v);return l.createElement(l.Fragment,null,E&&l.createElement(C,{sideCar:h,removeScrollBar:b,shards:R,noRelative:A,noIsolation:S,inert:M,setCallbacks:g,allowPinchZoom:!!k,lockRef:c,gapMode:P}),y?l.cloneElement(l.Children.only(w),i(i({},L),{ref:D})):l.createElement(void 0===T?"div":T,i({},L,{className:x,ref:D}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},x=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},R=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=R(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=x(),S="data-scroll-locked",M=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},k=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},T=function(){l.useEffect(function(){return document.body.setAttribute(S,(k()+1).toString()),function(){var e=k()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=l.useMemo(function(){return C(o)},[o]);return l.createElement(A,{styles:M(i,!t,o,n?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){j=!1}var L=!!j&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},N=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var h=F(e,u),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&I(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},K=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},H=0,G=[];let z=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(H++)[0],i=l.useState(x)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=B(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=N(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=N(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(G.length&&G[G.length-1]===i){var n="deltaY"in e?K(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=B(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,K(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return G.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,L),document.addEventListener("touchmove",c,L),document.addEventListener("touchstart",d,L),function(){G=G.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,L),document.removeEventListener("touchmove",c,L),document.removeEventListener("touchstart",d,L)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var V=l.forwardRef(function(e,t){return l.createElement(v,i({},e,{ref:t,sideCar:z}))});V.classNames=v.classNames;let X=V}}]);