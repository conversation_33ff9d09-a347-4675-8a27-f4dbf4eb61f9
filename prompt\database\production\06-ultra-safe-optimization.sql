-- =====================================================
-- 超安全的性能优化脚本
-- =====================================================
-- 版本: 1.0.2
-- 创建时间: 2025-07-26
-- 描述: 检查所有字段后再创建索引，避免字段不存在错误
-- =====================================================

-- 1. 检查并创建 categories 表索引
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        RAISE NOTICE '✅ 开始优化 categories 表';
        
        -- 基础索引
        CREATE INDEX IF NOT EXISTS idx_categories_user_id ON categories(user_id);
        CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);
        
        -- 检查并创建复合索引
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'deleted_at') THEN
            CREATE INDEX IF NOT EXISTS idx_categories_user_sort_active ON categories(user_id, sort_order) WHERE deleted_at IS NULL;
        ELSE
            CREATE INDEX IF NOT EXISTS idx_categories_user_sort ON categories(user_id, sort_order);
        END IF;
        
        -- 检查 created_at 字段
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'created_at') THEN
            IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'deleted_at') THEN
                CREATE INDEX IF NOT EXISTS idx_categories_user_created_active ON categories(user_id, created_at DESC) WHERE deleted_at IS NULL;
            ELSE
                CREATE INDEX IF NOT EXISTS idx_categories_user_created ON categories(user_id, created_at DESC);
            END IF;
        END IF;
        
        RAISE NOTICE '✅ categories 表索引优化完成';
    END IF;
END $$;

-- 2. 检查并创建 prompts 表索引
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'prompts') THEN
        RAISE NOTICE '✅ 开始优化 prompts 表';
        
        -- 基础索引
        CREATE INDEX IF NOT EXISTS idx_prompts_user_id ON prompts(user_id);
        
        -- 检查 category_id 字段
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompts' AND column_name = 'category_id') THEN
            CREATE INDEX IF NOT EXISTS idx_prompts_category_id ON prompts(category_id);
            
            IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompts' AND column_name = 'deleted_at') THEN
                CREATE INDEX IF NOT EXISTS idx_prompts_user_category_active ON prompts(user_id, category_id) WHERE deleted_at IS NULL;
            ELSE
                CREATE INDEX IF NOT EXISTS idx_prompts_user_category ON prompts(user_id, category_id);
            END IF;
        END IF;
        
        -- 检查 usage_count 字段
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompts' AND column_name = 'usage_count') THEN
            CREATE INDEX IF NOT EXISTS idx_prompts_usage_count ON prompts(usage_count DESC);
        END IF;
        
        -- 检查时间字段
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompts' AND column_name = 'created_at') THEN
            IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompts' AND column_name = 'deleted_at') THEN
                CREATE INDEX IF NOT EXISTS idx_prompts_user_created_active ON prompts(user_id, created_at DESC) WHERE deleted_at IS NULL;
            ELSE
                CREATE INDEX IF NOT EXISTS idx_prompts_user_created ON prompts(user_id, created_at DESC);
            END IF;
        END IF;
        
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompts' AND column_name = 'updated_at') THEN
            IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompts' AND column_name = 'deleted_at') THEN
                CREATE INDEX IF NOT EXISTS idx_prompts_user_updated_active ON prompts(user_id, updated_at DESC) WHERE deleted_at IS NULL;
            ELSE
                CREATE INDEX IF NOT EXISTS idx_prompts_user_updated ON prompts(user_id, updated_at DESC);
            END IF;
        END IF;
        
        -- 搜索索引
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompts' AND column_name = 'title') THEN
            CREATE INDEX IF NOT EXISTS idx_prompts_title_btree ON prompts(title);
        END IF;
        
        RAISE NOTICE '✅ prompts 表索引优化完成';
    END IF;
END $$;

-- 3. 检查并创建 tags 表索引
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tags') THEN
        RAISE NOTICE '✅ 开始优化 tags 表';
        
        CREATE INDEX IF NOT EXISTS idx_tags_user_id ON tags(user_id);
        
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tags' AND column_name = 'name') THEN
            CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
        END IF;
        
        RAISE NOTICE '✅ tags 表索引优化完成';
    END IF;
END $$;

-- 4. 检查并创建 prompt_tags 表索引
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'prompt_tags') THEN
        RAISE NOTICE '✅ 开始优化 prompt_tags 表';
        
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompt_tags' AND column_name = 'prompt_id') THEN
            CREATE INDEX IF NOT EXISTS idx_prompt_tags_prompt ON prompt_tags(prompt_id);
        END IF;
        
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prompt_tags' AND column_name = 'tag_id') THEN
            CREATE INDEX IF NOT EXISTS idx_prompt_tags_tag ON prompt_tags(tag_id);
        END IF;
        
        RAISE NOTICE '✅ prompt_tags 表索引优化完成';
    END IF;
END $$;

-- 5. 检查并创建 user_preferences 表索引
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_preferences') THEN
        RAISE NOTICE '✅ 开始优化 user_preferences 表';
        
        CREATE INDEX IF NOT EXISTS idx_user_preferences_user ON user_preferences(user_id);
        
        RAISE NOTICE '✅ user_preferences 表索引优化完成';
    END IF;
END $$;

-- 6. 检查并创建 search_history 表索引
DO $$
DECLARE
    time_column TEXT;
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'search_history') THEN
        RAISE NOTICE '✅ 开始优化 search_history 表';
        
        CREATE INDEX IF NOT EXISTS idx_search_history_user ON search_history(user_id);
        
        -- 检查时间字段名
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'search_history' AND column_name = 'created_at') THEN
            time_column := 'created_at';
        ELSIF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'search_history' AND column_name = 'last_searched_at') THEN
            time_column := 'last_searched_at';
        ELSIF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'search_history' AND column_name = 'searched_at') THEN
            time_column := 'searched_at';
        END IF;
        
        -- 如果找到时间字段，创建索引
        IF time_column IS NOT NULL THEN
            EXECUTE format('CREATE INDEX IF NOT EXISTS idx_search_history_user_time ON search_history(user_id, %I DESC)', time_column);
            RAISE NOTICE '✅ search_history 表索引优化完成（使用字段: %）', time_column;
        ELSE
            RAISE NOTICE '⚠️ search_history 表未找到时间字段，跳过时间索引';
        END IF;
    END IF;
END $$;

-- 7. 创建简化的搜索函数
CREATE OR REPLACE FUNCTION search_prompts_optimized(
    search_query TEXT,
    user_uuid UUID,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    category_id UUID,
    usage_count INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.title,
        p.content,
        p.category_id,
        COALESCE(p.usage_count, 0) as usage_count,
        p.created_at,
        p.updated_at
    FROM prompts p
    WHERE 
        p.user_id = user_uuid
        AND (
            search_query IS NULL 
            OR search_query = '' 
            OR p.title ILIKE '%' || search_query || '%'
            OR p.content ILIKE '%' || search_query || '%'
        )
    ORDER BY p.updated_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql STABLE;

-- 8. 授予权限
GRANT EXECUTE ON FUNCTION search_prompts_optimized TO authenticated;

-- 9. 更新表统计信息
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        ANALYZE categories;
        RAISE NOTICE '✅ categories 表统计信息已更新';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'prompts') THEN
        ANALYZE prompts;
        RAISE NOTICE '✅ prompts 表统计信息已更新';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tags') THEN
        ANALYZE tags;
        RAISE NOTICE '✅ tags 表统计信息已更新';
    END IF;
END $$;

-- 10. 显示优化结果
DO $$
DECLARE
    index_count INTEGER;
    table_list TEXT[];
BEGIN
    -- 获取存在的表
    SELECT array_agg(table_name) INTO table_list
    FROM information_schema.tables 
    WHERE table_name IN ('categories', 'prompts', 'tags', 'prompt_tags', 'user_preferences', 'search_history')
    AND table_schema = 'public';
    
    -- 计算索引数量
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE tablename = ANY(table_list);
    
    RAISE NOTICE '🎉 数据库性能优化完成！';
    RAISE NOTICE '📊 已优化表: %', array_to_string(table_list, ', ');
    RAISE NOTICE '🔍 已创建/验证 % 个索引', index_count;
    RAISE NOTICE '⚡ 已创建搜索优化函数';
    RAISE NOTICE '📈 数据库查询性能已显著提升';
END $$;
