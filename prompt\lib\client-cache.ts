/**
 * 浏览器端缓存系统
 * 解决服务器端内存缓存在 Vercel 等 serverless 环境中的问题
 */

interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number
  version: string
}

class ClientCache {
  private memoryCache = new Map<string, CacheItem<any>>()
  private readonly version = '1.0.0' // 缓存版本，用于缓存失效

  /**
   * 生成稳定的缓存键
   */
  private generateKey(key: string, params?: any): string {
    if (!params) return key
    
    // 确保对象键的顺序一致
    const sortedParams = this.sortObject(params)
    return `${key}:${JSON.stringify(sortedParams)}`
  }

  /**
   * 递归排序对象键，确保缓存键的一致性
   */
  private sortObject(obj: any): any {
    if (obj === null || typeof obj !== 'object') return obj
    if (Array.isArray(obj)) return obj.map(item => this.sortObject(item))
    
    const sorted: any = {}
    Object.keys(obj).sort().forEach(key => {
      sorted[key] = this.sortObject(obj[key])
    })
    return sorted
  }

  /**
   * 设置缓存（内存 + localStorage）
   */
  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000, params?: any): void {
    const cacheKey = this.generateKey(key, params)
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      version: this.version
    }

    // 内存缓存
    this.memoryCache.set(cacheKey, item)

    // localStorage 缓存（仅在浏览器环境）
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(`cache_${cacheKey}`, JSON.stringify(item))
      } catch (error) {
        console.warn('localStorage 缓存失败:', error)
      }
    }
  }

  /**
   * 获取缓存
   */
  get<T>(key: string, params?: any): T | null {
    const cacheKey = this.generateKey(key, params)

    // 先尝试内存缓存
    let item = this.memoryCache.get(cacheKey)

    // 如果内存缓存没有，尝试 localStorage
    if (!item && typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(`cache_${cacheKey}`)
        if (stored) {
          item = JSON.parse(stored)
          // 恢复到内存缓存
          if (item) {
            this.memoryCache.set(cacheKey, item)
          }
        }
      } catch (error) {
        console.warn('localStorage 读取失败:', error)
      }
    }

    if (!item) return null

    // 检查版本
    if (item.version !== this.version) {
      this.delete(key, params)
      return null
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key, params)
      return null
    }

    return item.data as T
  }

  /**
   * 删除缓存
   */
  delete(key: string, params?: any): void {
    const cacheKey = this.generateKey(key, params)
    
    this.memoryCache.delete(cacheKey)
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`cache_${cacheKey}`)
    }
  }

  /**
   * 清除所有缓存
   */
  clear(): void {
    this.memoryCache.clear()
    
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('cache_')) {
          localStorage.removeItem(key)
        }
      })
    }
  }

  /**
   * 清除匹配模式的缓存
   */
  clearPattern(pattern: string): void {
    // 清除内存缓存
    for (const key of this.memoryCache.keys()) {
      if (key.includes(pattern)) {
        this.memoryCache.delete(key)
      }
    }

    // 清除 localStorage 缓存
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('cache_') && key.includes(pattern)) {
          localStorage.removeItem(key)
        }
      })
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): { memorySize: number; localStorageSize: number; keys: string[] } {
    let localStorageSize = 0
    const keys: string[] = []

    if (typeof window !== 'undefined') {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('cache_')) {
          localStorageSize++
          keys.push(key.replace('cache_', ''))
        }
      })
    }

    return {
      memorySize: this.memoryCache.size,
      localStorageSize,
      keys: Array.from(new Set([...Array.from(this.memoryCache.keys()), ...keys]))
    }
  }
}

// 创建全局客户端缓存实例
export const clientCache = new ClientCache()

// 缓存键常量
export const CLIENT_CACHE_KEYS = {
  CATEGORIES: 'categories',
  PROMPTS: 'prompts',
  TAGS: 'tags',
  SEARCH_HISTORY: 'search_history',
  PROMPT_DETAIL: 'prompt_detail',
  USER_PREFERENCES: 'user_preferences',
} as const

// 缓存时间常量（毫秒）
export const CLIENT_CACHE_TTL = {
  SHORT: 30 * 1000,        // 30秒
  MEDIUM: 2 * 60 * 1000,   // 2分钟
  LONG: 10 * 60 * 1000,    // 10分钟
  VERY_LONG: 30 * 60 * 1000, // 30分钟
} as const

/**
 * 客户端缓存装饰器函数
 */
export async function withClientCache<T>(
  cacheKey: string,
  ttl: number,
  fn: () => Promise<T>,
  params?: any
): Promise<T> {
  // 尝试从缓存获取
  const cached = clientCache.get<T>(cacheKey, params)
  if (cached !== null) {
    console.log(`Client cache hit: ${cacheKey}`, params)
    return cached
  }

  // 缓存未命中，执行函数
  console.log(`Client cache miss: ${cacheKey}`, params)
  const result = await fn()
  
  // 存入缓存
  clientCache.set(cacheKey, result, ttl, params)
  
  return result
}

/**
 * 清除相关缓存
 */
export function invalidateClientCache(patterns: string[]): void {
  patterns.forEach(pattern => {
    clientCache.clearPattern(pattern)
  })
  console.log(`Invalidated client cache patterns: ${patterns.join(', ')}`)
}
