// 数据库操作统一入口
// 导出所有数据库操作函数

// 分类相关
export {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  updateCategoriesOrder,
  checkCategoryNameExists,
  getCategoryPromptCount
} from './categories'

// 提示词相关
export {
  getPrompts,
  getPromptById,
  createPrompt,
  updatePrompt,
  deletePrompt,
  incrementPromptUsage,
  importPrompts,
  getPopularPrompts
} from './prompts'

// 标签相关
export {
  getTags,
  getTagById,
  createTag,
  updateTag,
  deleteTag,
  checkTagNameExists,
  getTagUsageCount,
  getPopularTags,
  createTags,
  searchTags,
  getPromptTags,
  addTagToPrompt,
  removeTagFromPrompt
} from './tags'

// 搜索相关
export {
  getSearchHistory,
  addSearchHistory,
  clearSearchHistory,
  deleteSearchHistoryItem,
  getPopularSearchTerms,
  getSearchSuggestions,
  searchPrompts,
  advancedSearch
} from './search'

// 用户偏好相关
export {
  getUserPreferences,
  createDefaultUserPreferences,
  updateUserPreferences,
  updateTheme,
  updateDefaultCategory,
  updateItemsPerPage,
  updateShowUsageCount,
  updateAutoCopyFeedback,
  resetUserPreferences,
  deleteUserPreferences,
  initializeUserData
} from './preferences'

// 导出类型
export type {
  Database,
  Category,
  CategoryInsert,
  CategoryUpdate,
  CategoryWithCount,
  Tag,
  TagInsert,
  TagUpdate,
  Prompt,
  PromptInsert,
  PromptUpdate,
  PromptWithDetails,
  PromptTag,
  PromptTagInsert,
  PromptTagUpdate,
  SearchHistory,
  SearchHistoryInsert,
  SearchHistoryUpdate,
  UserPreferences,
  UserPreferencesInsert,
  UserPreferencesUpdate,
  SearchParams,
  PaginatedResponse,
  DatabaseError
} from '@/types/database'
