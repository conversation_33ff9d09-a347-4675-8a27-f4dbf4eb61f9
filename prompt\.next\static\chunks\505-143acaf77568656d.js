(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[505],{4181:(t,e,n)=>{"use strict";function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=Array(e);n<e;n++)a[n]=t[n];return a}n.d(e,{Kk:()=>ne,Yv:()=>e8,qg:()=>nt});function r(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=d(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var a=0,r=function(){};return{s:r,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:r}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return i=t.done,t},e:function(t){s=!0,o=t},f:function(){try{i||null==n.return||n.return()}finally{if(s)throw o}}}}function o(t,e,n){return(e=c(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function i(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,a)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?i(Object(n),!0).forEach(function(e){o(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var a,r,o,i,s=[],l=!0,f=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=o.call(n)).done)&&(s.push(a.value),s.length!==e);l=!0);}catch(t){f=!0,r=t}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(f)throw r}}return s}}(t,e)||d(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t){return function(t){if(Array.isArray(t))return a(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||d(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d(t,e){if(t){if("string"==typeof t)return a(t,e);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(t,e):void 0}}var m,p,h,g=function(){},b={},y={},v=null,x={mark:g,measure:g};try{"undefined"!=typeof window&&(b=window),"undefined"!=typeof document&&(y=document),"undefined"!=typeof MutationObserver&&(v=MutationObserver),"undefined"!=typeof performance&&(x=performance)}catch(t){}var w=(b.navigator||{}).userAgent,k=void 0===w?"":w,j=b,S=y,A=v,O=x;j.document;var P=!!S.documentElement&&!!S.head&&"function"==typeof S.addEventListener&&"function"==typeof S.createElement,I=~k.indexOf("MSIE")||~k.indexOf("Trident/"),N={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"},slab:{"fa-regular":"regular",faslr:"regular"},"slab-press":{"fa-regular":"regular",faslpr:"regular"},thumbprint:{"fa-light":"light",fatl:"light"},whiteboard:{"fa-semibold":"semibold",fawsb:"semibold"},notdog:{"fa-solid":"solid",fans:"solid"},"notdog-duo":{"fa-solid":"solid",fands:"solid"},etch:{"fa-solid":"solid",faes:"solid"},jelly:{"fa-regular":"regular",fajr:"regular"},"jelly-fill":{"fa-regular":"regular",fajfr:"regular"},"jelly-duo":{"fa-regular":"regular",fajdr:"regular"},chisel:{"fa-regular":"regular",facr:"regular"}},F=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-thumbprint","fa-whiteboard","fa-notdog","fa-notdog-duo","fa-chisel","fa-etch","fa-jelly","fa-jelly-fill","fa-jelly-duo","fa-slab","fa-slab-press"],E="classic",C="duotone",z="sharp",M="sharp-duotone",D="chisel",T="etch",R="jelly",L="jelly-duo",W="jelly-fill",Y="notdog",_="notdog-duo",U="slab",q="slab-press",H="thumbprint",J="whiteboard",B=[E,C,z,M,D,T,R,L,W,Y,_,U,q,H,J];o(o(o(o(o(o(o(o(o(o(p={},E,"Classic"),C,"Duotone"),z,"Sharp"),M,"Sharp Duotone"),D,"Chisel"),T,"Etch"),R,"Jelly"),L,"Jelly Duo"),W,"Jelly Fill"),Y,"Notdog"),o(o(o(o(o(p,_,"Notdog Duo"),U,"Slab"),q,"Slab Press"),H,"Thumbprint"),J,"Whiteboard");var V=new Map([["classic",{defaultShortPrefixId:"fas",defaultStyleId:"solid",styleIds:["solid","regular","light","thin","brands"],futureStyleIds:[],defaultFontWeight:900}],["duotone",{defaultShortPrefixId:"fad",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp",{defaultShortPrefixId:"fass",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp-duotone",{defaultShortPrefixId:"fasds",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["chisel",{defaultShortPrefixId:"facr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["etch",{defaultShortPrefixId:"faes",defaultStyleId:"solid",styleIds:["solid"],futureStyleIds:[],defaultFontWeight:900}],["jelly",{defaultShortPrefixId:"fajr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["jelly-duo",{defaultShortPrefixId:"fajdr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["jelly-fill",{defaultShortPrefixId:"fajfr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["notdog",{defaultShortPrefixId:"fans",defaultStyleId:"solid",styleIds:["solid"],futureStyleIds:[],defaultFontWeight:900}],["notdog-duo",{defaultShortPrefixId:"fands",defaultStyleId:"solid",styleIds:["solid"],futureStyleIds:[],defaultFontWeight:900}],["slab",{defaultShortPrefixId:"faslr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["slab-press",{defaultShortPrefixId:"faslpr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["thumbprint",{defaultShortPrefixId:"fatl",defaultStyleId:"light",styleIds:["light"],futureStyleIds:[],defaultFontWeight:300}],["whiteboard",{defaultShortPrefixId:"fawsb",defaultStyleId:"semibold",styleIds:["semibold"],futureStyleIds:[],defaultFontWeight:600}]]),K=["fak","fa-kit","fakd","fa-kit-duotone"],X={kit:{fak:"kit","fa-kit":"kit"},"kit-duotone":{fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"}};o(o({},"kit","Kit"),"kit-duotone","Kit Duotone");var G={kit:{kit:"fak"},"kit-duotone":{"kit-duotone":"fakd"}},$={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"};o(o(o(o(o(o(o(o(o(o(h={},"classic","Classic"),"duotone","Duotone"),"sharp","Sharp"),"sharp-duotone","Sharp Duotone"),"chisel","Chisel"),"etch","Etch"),"jelly","Jelly"),"jelly-duo","Jelly Duo"),"jelly-fill","Jelly Fill"),"notdog","Notdog"),o(o(o(o(o(h,"notdog-duo","Notdog Duo"),"slab","Slab"),"slab-press","Slab Press"),"thumbprint","Thumbprint"),"whiteboard","Whiteboard"),o(o({},"kit","Kit"),"kit-duotone","Kit Duotone");var Q={classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"},slab:{faslr:"fa-regular"},"slab-press":{faslpr:"fa-regular"},whiteboard:{fawsb:"fa-semibold"},thumbprint:{fatl:"fa-light"},notdog:{fans:"fa-solid"},"notdog-duo":{fands:"fa-solid"},etch:{faes:"fa-solid"},jelly:{fajr:"fa-regular"},"jelly-fill":{fajfr:"fa-regular"},"jelly-duo":{fajdr:"fa-regular"},chisel:{facr:"fa-regular"}},Z=["fa","fas","far","fal","fat","fad","fadr","fadl","fadt","fab","fass","fasr","fasl","fast","fasds","fasdr","fasdl","fasdt","faslr","faslpr","fawsb","fatl","fans","fands","faes","fajr","fajfr","fajdr","facr"].concat(["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-thumbprint","fa-whiteboard","fa-notdog","fa-notdog-duo","fa-chisel","fa-etch","fa-jelly","fa-jelly-fill","fa-jelly-duo","fa-slab","fa-slab-press"],["fa-solid","fa-regular","fa-light","fa-thin","fa-duotone","fa-brands","fa-semibold"]),tt=[1,2,3,4,5,6,7,8,9,10],te=tt.concat([11,12,13,14,15,16,17,18,19,20]),tn=[].concat(f(["classic","duotone","sharp","sharp-duotone","slab","slab-press","whiteboard","thumbprint","notdog","notdog-duo","etch","jelly","jelly-fill","jelly-duo","chisel"]),["solid","regular","light","thin","duotone","brands","semibold"],["aw","fw","pull-left","pull-right"],["2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","inverse","layers","layers-bottom-left","layers-bottom-right","layers-counter","layers-text","layers-top-left","layers-top-right","li","pull-end","pull-start","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul","width-auto","width-fixed",$.GROUP,$.SWAP_OPACITY,$.PRIMARY,$.SECONDARY]).concat(tt.map(function(t){return"".concat(t,"x")})).concat(te.map(function(t){return"w-".concat(t)})),ta="___FONT_AWESOME___",tr="svg-inline--fa",to="data-fa-i2svg",ti="data-fa-pseudo-element",ts="data-prefix",tl="data-icon",tf="fontawesome-i2svg",tc=["HTML","HEAD","STYLE","SCRIPT"],tu=["::before","::after",":before",":after"],td=function(){try{return!0}catch(t){return!1}}();function tm(t){return new Proxy(t,{get:function(t,e){return e in t?t[e]:t[E]}})}var tp=s({},N);tp[E]=s(s(s(s({},{"fa-duotone":"duotone"}),N[E]),X.kit),X["kit-duotone"]);var th=tm(tp),tg=s({},{chisel:{regular:"facr"},classic:{brands:"fab",light:"fal",regular:"far",solid:"fas",thin:"fat"},duotone:{light:"fadl",regular:"fadr",solid:"fad",thin:"fadt"},etch:{solid:"faes"},jelly:{regular:"fajr"},"jelly-duo":{regular:"fajdr"},"jelly-fill":{regular:"fajfr"},notdog:{solid:"fans"},"notdog-duo":{solid:"fands"},sharp:{light:"fasl",regular:"fasr",solid:"fass",thin:"fast"},"sharp-duotone":{light:"fasdl",regular:"fasdr",solid:"fasds",thin:"fasdt"},slab:{regular:"faslr"},"slab-press":{regular:"faslpr"},thumbprint:{light:"fatl"},whiteboard:{semibold:"fawsb"}});tg[E]=s(s(s(s({},{duotone:"fad"}),tg[E]),G.kit),G["kit-duotone"]);var tb=tm(tg),ty=s({},Q);ty[E]=s(s({},ty[E]),{fak:"fa-kit"});var tv=tm(ty),tx=s({},{classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"},slab:{"fa-regular":"faslr"},"slab-press":{"fa-regular":"faslpr"},whiteboard:{"fa-semibold":"fawsb"},thumbprint:{"fa-light":"fatl"},notdog:{"fa-solid":"fans"},"notdog-duo":{"fa-solid":"fands"},etch:{"fa-solid":"faes"},jelly:{"fa-regular":"fajr"},"jelly-fill":{"fa-regular":"fajfr"},"jelly-duo":{"fa-regular":"fajdr"},chisel:{"fa-regular":"facr"}});tx[E]=s(s({},tx[E]),{"fa-kit":"fak"}),tm(tx);var tw=/fa(k|kd|s|r|l|t|d|dr|dl|dt|b|slr|slpr|wsb|tl|ns|nds|es|jr|jfr|jdr|cr|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\-\ ]/,tk="fa-layers-text",tj=/Font ?Awesome ?([567 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit|Notdog Duo|Notdog|Chisel|Etch|Thumbprint|Jelly Fill|Jelly Duo|Jelly|Slab Press|Slab|Whiteboard)?.*/i;tm(s({},{classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"},slab:{400:"faslr"},"slab-press":{400:"faslpr"},whiteboard:{600:"fawsb"},thumbprint:{300:"fatl"},notdog:{900:"fans"},"notdog-duo":{900:"fands"},etch:{900:"faes"},chisel:{400:"facr"},jelly:{400:"fajr"},"jelly-fill":{400:"fajfr"},"jelly-duo":{400:"fajdr"}}));var tS=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],tA={GROUP:"duotone-group",PRIMARY:"primary",SECONDARY:"secondary"},tO=[].concat(f(["kit"]),f(tn)),tP=j.FontAwesomeConfig||{};S&&"function"==typeof S.querySelector&&[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-search-pseudo-elements","searchPseudoElements"],["data-search-pseudo-elements-warnings","searchPseudoElementsWarnings"],["data-search-pseudo-elements-full-scan","searchPseudoElementsFullScan"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(function(t){var e,n=l(t,2),a=n[0],r=n[1],o=""===(e=function(t){var e=S.querySelector("script["+t+"]");if(e)return e.getAttribute(t)}(a))||"false"!==e&&("true"===e||e);null!=o&&(tP[r]=o)});var tI={styleDefault:"solid",familyDefault:E,cssPrefix:"fa",replacementClass:tr,autoReplaceSvg:!0,autoAddCss:!0,searchPseudoElements:!1,searchPseudoElementsWarnings:!0,searchPseudoElementsFullScan:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};tP.familyPrefix&&(tP.cssPrefix=tP.familyPrefix);var tN=s(s({},tI),tP);tN.autoReplaceSvg||(tN.observeMutations=!1);var tF={};Object.keys(tI).forEach(function(t){Object.defineProperty(tF,t,{enumerable:!0,set:function(e){tN[t]=e,tE.forEach(function(t){return t(tF)})},get:function(){return tN[t]}})}),Object.defineProperty(tF,"familyPrefix",{enumerable:!0,set:function(t){tN.cssPrefix=t,tE.forEach(function(t){return t(tF)})},get:function(){return tN.cssPrefix}}),j.FontAwesomeConfig=tF;var tE=[],tC={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function tz(){for(var t=12,e="";t-- >0;)e+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[62*Math.random()|0];return e}function tM(t){for(var e=[],n=(t||[]).length>>>0;n--;)e[n]=t[n];return e}function tD(t){return t.classList?tM(t.classList):(t.getAttribute("class")||"").split(" ").filter(function(t){return t})}function tT(t){return"".concat(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function tR(t){return Object.keys(t||{}).reduce(function(e,n){return e+"".concat(n,": ").concat(t[n].trim(),";")},"")}function tL(t){return t.size!==tC.size||t.x!==tC.x||t.y!==tC.y||t.rotate!==tC.rotate||t.flipX||t.flipY}function tW(){var t=tF.cssPrefix,e=tF.replacementClass,n=':root, :host {\n  --fa-font-solid: normal 900 1em/1 "Font Awesome 7 Free";\n  --fa-font-regular: normal 400 1em/1 "Font Awesome 7 Free";\n  --fa-font-light: normal 300 1em/1 "Font Awesome 7 Pro";\n  --fa-font-thin: normal 100 1em/1 "Font Awesome 7 Pro";\n  --fa-font-duotone: normal 900 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-duotone-regular: normal 400 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-duotone-light: normal 300 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-duotone-thin: normal 100 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-brands: normal 400 1em/1 "Font Awesome 7 Brands";\n  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-sharp-duotone-light: normal 300 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-slab-regular: normal 400 1em/1 "Font Awesome 7 Slab";\n  --fa-font-slab-press-regular: normal 400 1em/1 "Font Awesome 7 Slab Press";\n  --fa-font-whiteboard-semibold: normal 600 1em/1 "Font Awesome 7 Whiteboard";\n  --fa-font-thumbprint-light: normal 300 1em/1 "Font Awesome 7 Thumbprint";\n  --fa-font-notdog-solid: normal 900 1em/1 "Font Awesome 7 Notdog";\n  --fa-font-notdog-duo-solid: normal 900 1em/1 "Font Awesome 7 Notdog Duo";\n  --fa-font-etch-solid: normal 900 1em/1 "Font Awesome 7 Etch";\n  --fa-font-jelly-regular: normal 400 1em/1 "Font Awesome 7 Jelly";\n  --fa-font-jelly-fill-regular: normal 400 1em/1 "Font Awesome 7 Jelly Fill";\n  --fa-font-jelly-duo-regular: normal 400 1em/1 "Font Awesome 7 Jelly Duo";\n  --fa-font-chisel-regular: normal 400 1em/1 "Font Awesome 7 Chisel";\n}\n\n.svg-inline--fa {\n  box-sizing: content-box;\n  display: var(--fa-display, inline-block);\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n  width: var(--fa-width, 1.25em);\n}\n.svg-inline--fa.fa-2xs {\n  vertical-align: 0.1em;\n}\n.svg-inline--fa.fa-xs {\n  vertical-align: 0em;\n}\n.svg-inline--fa.fa-sm {\n  vertical-align: -0.0714285714em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.2em;\n}\n.svg-inline--fa.fa-xl {\n  vertical-align: -0.25em;\n}\n.svg-inline--fa.fa-2xl {\n  vertical-align: -0.3125em;\n}\n.svg-inline--fa.fa-pull-left,\n.svg-inline--fa .fa-pull-start {\n  float: inline-start;\n  margin-inline-end: var(--fa-pull-margin, 0.3em);\n}\n.svg-inline--fa.fa-pull-right,\n.svg-inline--fa .fa-pull-end {\n  float: inline-end;\n  margin-inline-start: var(--fa-pull-margin, 0.3em);\n}\n.svg-inline--fa.fa-li {\n  width: var(--fa-li-width, 2em);\n  inset-inline-start: calc(-1 * var(--fa-li-width, 2em));\n  inset-block-start: 0.25em; /* syncing vertical alignment with Web Font rendering */\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: var(--fa-width, 1.25em);\n}\n.fa-layers .svg-inline--fa {\n  inset: 0;\n  margin: auto;\n  position: absolute;\n  transform-origin: center center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: var(--fa-counter-background-color, #ff253a);\n  border-radius: var(--fa-counter-border-radius, 1em);\n  box-sizing: border-box;\n  color: var(--fa-inverse, #fff);\n  line-height: var(--fa-counter-line-height, 1);\n  max-width: var(--fa-counter-max-width, 5em);\n  min-width: var(--fa-counter-min-width, 1.5em);\n  overflow: hidden;\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\n  right: var(--fa-right, 0);\n  text-overflow: ellipsis;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-counter-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: var(--fa-bottom, 0);\n  right: var(--fa-right, 0);\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: var(--fa-bottom, 0);\n  left: var(--fa-left, 0);\n  right: auto;\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  top: var(--fa-top, 0);\n  right: var(--fa-right, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: var(--fa-left, 0);\n  right: auto;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top left;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-2xs {\n  font-size: calc(10 / 16 * 1em); /* converts a 10px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 10 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 10 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-xs {\n  font-size: calc(12 / 16 * 1em); /* converts a 12px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 12 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 12 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-sm {\n  font-size: calc(14 / 16 * 1em); /* converts a 14px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 14 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 14 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-lg {\n  font-size: calc(20 / 16 * 1em); /* converts a 20px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 20 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 20 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-xl {\n  font-size: calc(24 / 16 * 1em); /* converts a 24px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 24 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 24 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-2xl {\n  font-size: calc(32 / 16 * 1em); /* converts a 32px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 32 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 32 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-width-auto {\n  --fa-width: auto;\n}\n\n.fa-fw,\n.fa-width-fixed {\n  --fa-width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-inline-start: var(--fa-li-margin, 2.5em);\n  padding-inline-start: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  inset-inline-start: calc(-1 * var(--fa-li-width, 2em));\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width, 2em);\n  line-height: inherit;\n}\n\n/* Heads Up: Bordered Icons will not be supported in the future!\n  - This feature will be deprecated in the next major release of Font Awesome (v8)!\n  - You may continue to use it in this version *v7), but it will not be supported in Font Awesome v8.\n*/\n/* Notes:\n* --@{v.$css-prefix}-border-width = 1/16 by default (to render as ~1px based on a 16px default font-size)\n* --@{v.$css-prefix}-border-padding =\n  ** 3/16 for vertical padding (to give ~2px of vertical whitespace around an icon considering it\'s vertical alignment)\n  ** 4/16 for horizontal padding (to give ~4px of horizontal whitespace around an icon)\n*/\n.fa-border {\n  border-color: var(--fa-border-color, #eee);\n  border-radius: var(--fa-border-radius, 0.1em);\n  border-style: var(--fa-border-style, solid);\n  border-width: var(--fa-border-width, 0.0625em);\n  box-sizing: var(--fa-border-box-sizing, content-box);\n  padding: var(--fa-border-padding, 0.1875em 0.25em);\n}\n\n.fa-pull-left,\n.fa-pull-start {\n  float: inline-start;\n  margin-inline-end: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-pull-right,\n.fa-pull-end {\n  float: inline-end;\n  margin-inline-start: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-beat {\n  animation-name: fa-beat;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-bounce {\n  animation-name: fa-bounce;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n}\n\n.fa-fade {\n  animation-name: fa-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-beat-fade {\n  animation-name: fa-beat-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-flip {\n  animation-name: fa-flip;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-shake {\n  animation-name: fa-shake;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin {\n  animation-name: fa-spin;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 2s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin-reverse {\n  --fa-animation-direction: reverse;\n}\n\n.fa-pulse,\n.fa-spin-pulse {\n  animation-name: fa-spin;\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, steps(8));\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .fa-beat,\n  .fa-bounce,\n  .fa-fade,\n  .fa-beat-fade,\n  .fa-flip,\n  .fa-pulse,\n  .fa-shake,\n  .fa-spin,\n  .fa-spin-pulse {\n    animation: none !important;\n    transition: none !important;\n  }\n}\n@keyframes fa-beat {\n  0%, 90% {\n    transform: scale(1);\n  }\n  45% {\n    transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@keyframes fa-bounce {\n  0% {\n    transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    transform: scale(1, 1) translateY(0);\n  }\n}\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@keyframes fa-flip {\n  50% {\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@keyframes fa-shake {\n  0% {\n    transform: rotate(-15deg);\n  }\n  4% {\n    transform: rotate(15deg);\n  }\n  8%, 24% {\n    transform: rotate(-18deg);\n  }\n  12%, 28% {\n    transform: rotate(18deg);\n  }\n  16% {\n    transform: rotate(-22deg);\n  }\n  20% {\n    transform: rotate(22deg);\n  }\n  32% {\n    transform: rotate(-12deg);\n  }\n  36% {\n    transform: rotate(12deg);\n  }\n  40%, 100% {\n    transform: rotate(0deg);\n  }\n}\n@keyframes fa-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  transform: scale(1, -1);\n}\n\n.fa-flip-both,\n.fa-flip-horizontal.fa-flip-vertical {\n  transform: scale(-1, -1);\n}\n\n.fa-rotate-by {\n  transform: rotate(var(--fa-rotate-angle, 0));\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}\n\n.svg-inline--fa.fa-inverse {\n  fill: var(--fa-inverse, #fff);\n}\n\n.fa-stack {\n  display: inline-block;\n  height: 2em;\n  line-height: 2em;\n  position: relative;\n  vertical-align: middle;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: var(--fa-stack-z-index, auto);\n}';if("fa"!==t||e!==tr){var a=RegExp("\\.".concat("fa","\\-"),"g"),r=RegExp("\\--".concat("fa","\\-"),"g"),o=RegExp("\\.".concat(tr),"g");n=n.replace(a,".".concat(t,"-")).replace(r,"--".concat(t,"-")).replace(o,".".concat(e))}return n}var tY=!1;function t_(){tF.autoAddCss&&!tY&&(!function(t){if(t&&P){var e=S.createElement("style");e.setAttribute("type","text/css"),e.innerHTML=t;for(var n=S.head.childNodes,a=null,r=n.length-1;r>-1;r--){var o=n[r];["STYLE","LINK"].indexOf((o.tagName||"").toUpperCase())>-1&&(a=o)}S.head.insertBefore(e,a)}}(tW()),tY=!0)}var tU=j||{};tU[ta]||(tU[ta]={}),tU[ta].styles||(tU[ta].styles={}),tU[ta].hooks||(tU[ta].hooks={}),tU[ta].shims||(tU[ta].shims=[]);var tq=tU[ta],tH=[],tJ=function(){S.removeEventListener("DOMContentLoaded",tJ),tB=1,tH.map(function(t){return t()})},tB=!1;function tV(t){var e,n=t.tag,a=t.attributes,r=t.children;return"string"==typeof t?tT(t):"<".concat(n," ").concat(Object.keys((e=void 0===a?{}:a)||{}).reduce(function(t,n){return t+"".concat(n,'="').concat(tT(e[n]),'" ')},"").trim(),">").concat((void 0===r?[]:r).map(tV).join(""),"</").concat(n,">")}function tK(t,e,n){if(t&&t[e]&&t[e][n])return{prefix:e,iconName:n,icon:t[e][n]}}P&&((tB=(S.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(S.readyState))||S.addEventListener("DOMContentLoaded",tJ));var tX=function(t,e,n,a){var r,o,i,s=Object.keys(t),l=s.length,f=void 0!==a?function(t,n,r,o){return e.call(a,t,n,r,o)}:e;for(void 0===n?(r=1,i=t[s[0]]):(r=0,i=n);r<l;r++)i=f(i,t[o=s[r]],o,t);return i};function tG(t){return 1!==f(t).length?null:t.codePointAt(0).toString(16)}function t$(t){return Object.keys(t).reduce(function(e,n){var a=t[n];return a.icon?e[a.iconName]=a.icon:e[n]=a,e},{})}var tQ=tq.styles,tZ=tq.shims,t0=Object.keys(tv),t1=t0.reduce(function(t,e){return t[e]=Object.keys(tv[e]),t},{}),t2=null,t4={},t5={},t7={},t3={},t6={},t9=function(){var t=function(t){return tX(tQ,function(e,n,a){return e[a]=tX(n,t,{}),e},{})};t4=t(function(t,e,n){return e[3]&&(t[e[3]]=n),e[2]&&e[2].filter(function(t){return"number"==typeof t}).forEach(function(e){t[e.toString(16)]=n}),t}),t5=t(function(t,e,n){return t[n]=n,e[2]&&e[2].filter(function(t){return"string"==typeof t}).forEach(function(e){t[e]=n}),t}),t6=t(function(t,e,n){var a=e[2];return t[n]=n,a.forEach(function(e){t[e]=n}),t});var e="far"in tQ||tF.autoFetchSvg,n=tX(tZ,function(t,n){var a=n[0],r=n[1],o=n[2];return"far"!==r||e||(r="fas"),"string"==typeof a&&(t.names[a]={prefix:r,iconName:o}),"number"==typeof a&&(t.unicodes[a.toString(16)]={prefix:r,iconName:o}),t},{names:{},unicodes:{}});t7=n.names,t3=n.unicodes,t2=ea(tF.styleDefault,{family:tF.familyDefault})};function t8(t,e){return(t4[t]||{})[e]}function et(t,e){return(t6[t]||{})[e]}function ee(t){return t7[t]||{prefix:null,iconName:null}}m=function(t){t2=ea(t.styleDefault,{family:tF.familyDefault})},tE.push(m),t9();var en=function(){return{prefix:null,iconName:null,rest:[]}};function ea(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.family,a=void 0===n?E:n,r=th[a][t];if(a===C&&!t)return"fad";var o=tb[a][t]||tb[a][r],i=t in tq.styles?t:null;return o||i||null}function er(t){return t.sort().filter(function(t,e,n){return n.indexOf(t)===e})}var eo=Z.concat(K);function ei(t){var e,n,a,r,o,i,f,c,u,d,m,p,h,g,b,y,v=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},x=v.skipLookups,w=null,k=er(t.filter(function(t){return eo.includes(t)})),j=er(t.filter(function(t){return!eo.includes(t)})),S=l(k.filter(function(t){return w=t,!F.includes(t)}),1)[0],A=(h=E,g=t0.reduce(function(t,e){return t[e]="".concat(tF.cssPrefix,"-").concat(e),t},{}),B.forEach(function(t){(k.includes(g[t])||k.some(function(e){return t1[t].includes(e)}))&&(h=t)}),h),O=s(s({},(b=[],y=null,j.forEach(function(t){var e,n,a,r,o=(e=tF.cssPrefix,a=(n=t.split("-"))[0],r=n.slice(1).join("-"),a!==e||""===r||~tO.indexOf(r)?null:r);o?y=o:t&&b.push(t)}),{iconName:y,rest:b})),{},{prefix:ea(void 0===S?null:S,{family:A})});return s(s(s({},O),(n=(e={values:t,family:A,styles:tQ,config:tF,canonical:O,givenPrefix:w}).values,a=e.family,r=e.canonical,o=e.givenPrefix,i=e.styles,c=void 0===(f=e.config)?{}:f,u=a===C,d=n.includes("fa-duotone")||n.includes("fad"),m="duotone"===c.familyDefault,p="fad"===r.prefix||"fa-duotone"===r.prefix,!u&&(d||m||p)&&(r.prefix="fad"),(n.includes("fa-brands")||n.includes("fab"))&&(r.prefix="fab"),!r.prefix&&es.includes(a)&&(Object.keys(void 0===i?{}:i).find(function(t){return el.includes(t)})||c.autoFetchSvg)&&(r.prefix=V.get(a).defaultShortPrefixId,r.iconName=et(r.prefix,r.iconName)||r.iconName),("fa"===r.prefix||"fa"===(void 0===o?"":o))&&(r.prefix=t2||"fas"),r)),function(t,e,n){var a=n.prefix,r=n.iconName;if(t||!a||!r)return{prefix:a,iconName:r};var o="fa"===e?ee(r):{},i=et(a,r);return r=o.iconName||i||r,"far"!==(a=o.prefix||a)||tQ.far||!tQ.fas||tF.autoFetchSvg||(a="fas"),{prefix:a,iconName:r}}(void 0!==x&&x,w,O))}var es=B.filter(function(t){return t!==E||t!==C}),el=Object.keys(Q).filter(function(t){return t!==E}).map(function(t){return Object.keys(Q[t])}).flat(),ef=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");this.definitions={}},e=[{key:"add",value:function(){for(var t=this,e=arguments.length,n=Array(e),a=0;a<e;a++)n[a]=arguments[a];var r=n.reduce(this._pullDefinitions,{});Object.keys(r).forEach(function(e){t.definitions[e]=s(s({},t.definitions[e]||{}),r[e]),function t(e,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=a.skipHooks,o=t$(n);"function"!=typeof tq.hooks.addPack||void 0!==r&&r?tq.styles[e]=s(s({},tq.styles[e]||{}),o):tq.hooks.addPack(e,t$(n)),"fas"===e&&t("fa",n)}(e,r[e]),t9()})}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(t,e){var n=e.prefix&&e.iconName&&e.icon?{0:e}:e;return Object.keys(n).map(function(e){var a=n[e],r=a.prefix,o=a.iconName,i=a.icon,s=i[2];t[r]||(t[r]={}),s.length>0&&s.forEach(function(e){"string"==typeof e&&(t[r][e]=i)}),t[r][o]=i}),t}}],function(t,e){for(var n=0;n<e.length;n++){var a=e[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,c(a.key),a)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}(),ec=[],eu={},ed={},em=Object.keys(ed);function ep(t,e){for(var n=arguments.length,a=Array(n>2?n-2:0),r=2;r<n;r++)a[r-2]=arguments[r];return(eu[t]||[]).forEach(function(t){e=t.apply(null,[e].concat(a))}),e}function eh(t){for(var e=arguments.length,n=Array(e>1?e-1:0),a=1;a<e;a++)n[a-1]=arguments[a];(eu[t]||[]).forEach(function(t){t.apply(null,n)})}function eg(){var t=arguments[0],e=Array.prototype.slice.call(arguments,1);return ed[t]?ed[t].apply(null,e):void 0}function eb(t){"fa"===t.prefix&&(t.prefix="fas");var e=t.iconName,n=t.prefix||t2;if(e)return e=et(n,e)||e,tK(ey.definitions,n,e)||tK(tq.styles,n,e)}var ey=new ef,ev={noAuto:function(){tF.autoReplaceSvg=!1,tF.observeMutations=!1,eh("noAuto")},config:tF,dom:{i2svg:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return P?(eh("beforeI2svg",t),eg("pseudoElements2svg",t),eg("i2svg",t)):Promise.reject(Error("Operation requires a DOM of some kind."))},watch:function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.autoReplaceSvgRoot;!1===tF.autoReplaceSvg&&(tF.autoReplaceSvg=!0),tF.observeMutations=!0,t=function(){ex({autoReplaceSvgRoot:n}),eh("watch",e)},P&&(tB?setTimeout(t,0):tH.push(t))}},parse:{icon:function(t){if(null===t)return null;if("object"===u(t)&&t.prefix&&t.iconName)return{prefix:t.prefix,iconName:et(t.prefix,t.iconName)||t.iconName};if(Array.isArray(t)&&2===t.length){var e=0===t[1].indexOf("fa-")?t[1].slice(3):t[1],n=ea(t[0]);return{prefix:n,iconName:et(n,e)||e}}if("string"==typeof t&&(t.indexOf("".concat(tF.cssPrefix,"-"))>-1||t.match(tw))){var a=ei(t.split(" "),{skipLookups:!0});return{prefix:a.prefix||t2,iconName:et(a.prefix,a.iconName)||a.iconName}}if("string"==typeof t){var r=t2;return{prefix:r,iconName:et(r,t)||t}}}},library:ey,findIconDefinition:eb,toHtml:tV},ex=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.autoReplaceSvgRoot,n=void 0===e?S:e;(Object.keys(tq.styles).length>0||tF.autoFetchSvg)&&P&&tF.autoReplaceSvg&&ev.dom.i2svg({node:n})};function ew(t,e){return Object.defineProperty(t,"abstract",{get:e}),Object.defineProperty(t,"html",{get:function(){return t.abstract.map(function(t){return tV(t)})}}),Object.defineProperty(t,"node",{get:function(){if(P){var e=S.createElement("div");return e.innerHTML=t.html,e.children}}}),t}function ek(t){var e,n,a,r,o,i,l,f=t.icons,c=f.main,u=f.mask,d=t.prefix,m=t.iconName,p=t.transform,h=t.symbol,g=t.maskId,b=t.extra,y=t.watchable,v=u.found?u:c,x=v.width,w=v.height,k=[tF.replacementClass,m?"".concat(tF.cssPrefix,"-").concat(m):""].filter(function(t){return -1===b.classes.indexOf(t)}).filter(function(t){return""!==t||!!t}).concat(b.classes).join(" "),j={children:[],attributes:s(s({},b.attributes),{},{"data-prefix":d,"data-icon":m,class:k,role:b.attributes.role||"img",viewBox:"0 0 ".concat(x," ").concat(w)})};e=b.attributes,["aria-label","aria-labelledby","title","role"].some(function(t){return t in e})||b.attributes["aria-hidden"]||(j.attributes["aria-hidden"]="true"),void 0!==y&&y&&(j.attributes[to]="");var S=s(s({},j),{},{prefix:d,iconName:m,main:c,mask:u,maskId:g,transform:p,symbol:h,styles:s({},b.styles)}),A=u.found&&c.found?eg("generateAbstractMask",S)||{children:[],attributes:{}}:eg("generateAbstractIcon",S)||{children:[],attributes:{}},O=A.children,P=A.attributes;return(S.children=O,S.attributes=P,h)?(n=S.prefix,a=S.iconName,r=S.children,o=S.attributes,l=!0===(i=S.symbol)?"".concat(n,"-").concat(tF.cssPrefix,"-").concat(a):i,[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:s(s({},o),{},{id:l}),children:r}]}]):function(t){var e=t.children,n=t.main,a=t.mask,r=t.attributes,o=t.styles,i=t.transform;if(tL(i)&&n.found&&!a.found){var l={x:n.width/n.height/2,y:.5};r.style=tR(s(s({},o),{},{"transform-origin":"".concat(l.x+i.x/16,"em ").concat(l.y+i.y/16,"em")}))}return[{tag:"svg",attributes:r,children:e}]}(S)}function ej(t){var e,n,a,r,o,i,l,f=t.content,c=t.width,u=t.height,d=t.transform,m=t.extra,p=t.watchable,h=s(s({},m.attributes),{},{class:m.classes.join(" ")});void 0!==p&&p&&(h[to]="");var g=s({},m.styles);tL(d)&&(n=(e={transform:d,startCentered:!0,width:c,height:u}).transform,a=e.width,r=e.height,i=void 0!==(o=e.startCentered)&&o,l="",i&&I?l+="translate(".concat(n.x/16-(void 0===a?16:a)/2,"em, ").concat(n.y/16-(void 0===r?16:r)/2,"em) "):i?l+="translate(calc(-50% + ".concat(n.x/16,"em), calc(-50% + ").concat(n.y/16,"em)) "):l+="translate(".concat(n.x/16,"em, ").concat(n.y/16,"em) "),l+="scale(".concat(n.size/16*(n.flipX?-1:1),", ").concat(n.size/16*(n.flipY?-1:1),") "),g.transform=l+="rotate(".concat(n.rotate,"deg) "),g["-webkit-transform"]=g.transform);var b=tR(g);b.length>0&&(h.style=b);var y=[];return y.push({tag:"span",attributes:h,children:[f]}),y}var eS=tq.styles;function eA(t){var e=t[0],n=t[1],a=l(t.slice(4),1)[0];return{found:!0,width:e,height:n,icon:Array.isArray(a)?{tag:"g",attributes:{class:"".concat(tF.cssPrefix,"-").concat(tA.GROUP)},children:[{tag:"path",attributes:{class:"".concat(tF.cssPrefix,"-").concat(tA.SECONDARY),fill:"currentColor",d:a[0]}},{tag:"path",attributes:{class:"".concat(tF.cssPrefix,"-").concat(tA.PRIMARY),fill:"currentColor",d:a[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:a}}}}var eO={found:!1,width:512,height:512};function eP(t,e){var n=e;return"fa"===e&&null!==tF.styleDefault&&(e=t2),new Promise(function(a,r){if("fa"===n){var o,i,l=ee(t)||{};t=l.iconName||t,e=l.prefix||e}if(t&&e&&eS[e]&&eS[e][t])return a(eA(eS[e][t]));o=t,i=e,td||tF.showMissingIcons||!o||console.error('Icon with name "'.concat(o,'" and prefix "').concat(i,'" is missing.')),a(s(s({},eO),{},{icon:tF.showMissingIcons&&t&&eg("missingIconAbstract")||{}}))})}var eI=function(){},eN=tF.measurePerformance&&O&&O.mark&&O.measure?O:{mark:eI,measure:eI},eF='FA "7.0.0"',eE=function(t){eN.mark("".concat(eF," ").concat(t," ends")),eN.measure("".concat(eF," ").concat(t),"".concat(eF," ").concat(t," begins"),"".concat(eF," ").concat(t," ends"))},eC={begin:function(t){return eN.mark("".concat(eF," ").concat(t," begins")),function(){return eE(t)}}},ez=function(){};function eM(t){return"string"==typeof(t.getAttribute?t.getAttribute(to):null)}function eD(t){return S.createElementNS("http://www.w3.org/2000/svg",t)}function eT(t){return S.createElement(t)}var eR={replace:function(t){var e=t[0];if(e.parentNode)if(t[1].forEach(function(t){e.parentNode.insertBefore(function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=n.ceFn,r=void 0===a?"svg"===e.tag?eD:eT:a;if("string"==typeof e)return S.createTextNode(e);var o=r(e.tag);return Object.keys(e.attributes||[]).forEach(function(t){o.setAttribute(t,e.attributes[t])}),(e.children||[]).forEach(function(e){o.appendChild(t(e,{ceFn:r}))}),o}(t),e)}),null===e.getAttribute(to)&&tF.keepOriginalSource){var n,a=S.createComment((n=" ".concat(e.outerHTML," "),n="".concat(n,"Font Awesome fontawesome.com ")));e.parentNode.replaceChild(a,e)}else e.remove()},nest:function(t){var e=t[0],n=t[1];if(~tD(e).indexOf(tF.replacementClass))return eR.replace(t);var a=new RegExp("".concat(tF.cssPrefix,"-.*"));if(delete n[0].attributes.id,n[0].attributes.class){var r=n[0].attributes.class.split(" ").reduce(function(t,e){return e===tF.replacementClass||e.match(a)?t.toSvg.push(e):t.toNode.push(e),t},{toNode:[],toSvg:[]});n[0].attributes.class=r.toSvg.join(" "),0===r.toNode.length?e.removeAttribute("class"):e.setAttribute("class",r.toNode.join(" "))}var o=n.map(function(t){return tV(t)}).join("\n");e.setAttribute(to,""),e.innerHTML=o}};function eL(t){t()}function eW(t,e){var n="function"==typeof e?e:ez;if(0===t.length)n();else{var a=eL;"async"===tF.mutateApproach&&(a=j.requestAnimationFrame||eL),a(function(){var e=!0===tF.autoReplaceSvg?eR.replace:eR[tF.autoReplaceSvg]||eR.replace,a=eC.begin("mutate");t.map(e),a(),n()})}}var eY=!1,e_=null;function eU(t){if(A&&tF.observeMutations){var e=t.treeCallback,n=void 0===e?ez:e,a=t.nodeCallback,r=void 0===a?ez:a,o=t.pseudoElementsCallback,i=void 0===o?ez:o,s=t.observeMutationsRoot,l=void 0===s?S:s;e_=new A(function(t){if(!eY){var e=t2;tM(t).forEach(function(t){if("childList"===t.type&&t.addedNodes.length>0&&!eM(t.addedNodes[0])&&(tF.searchPseudoElements&&i(t.target),n(t.target)),"attributes"===t.type&&t.target.parentNode&&tF.searchPseudoElements&&i([t.target],!0),"attributes"===t.type&&eM(t.target)&&~tS.indexOf(t.attributeName))if("class"===t.attributeName&&(o=(a=t.target).getAttribute?a.getAttribute(ts):null,s=a.getAttribute?a.getAttribute(tl):null,o&&s)){var a,o,s,l,f=ei(tD(t.target)),c=f.prefix,u=f.iconName;t.target.setAttribute(ts,c||e),u&&t.target.setAttribute(tl,u)}else(l=t.target)&&l.classList&&l.classList.contains&&l.classList.contains(tF.replacementClass)&&r(t.target)})}}),P&&e_.observe(l,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}}function eq(t){var e,n,a,r,o,i,l,f,c,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{styleParser:!0},d=(r=(e=t).getAttribute("data-prefix"),o=e.getAttribute("data-icon"),i=void 0!==e.innerText?e.innerText.trim():"",((l=ei(tD(e))).prefix||(l.prefix=t2),r&&o&&(l.prefix=r,l.iconName=o),l.iconName&&l.prefix)?l:(l.prefix&&i.length>0&&(n=l.prefix,a=e.innerText,l.iconName=(t5[n]||{})[a]||t8(l.prefix,tG(e.innerText))),!l.iconName&&tF.autoFetchSvg&&e.firstChild&&e.firstChild.nodeType===Node.TEXT_NODE&&(l.iconName=e.firstChild.data),l)),m=d.iconName,p=d.prefix,h=d.rest,g=tM(t.attributes).reduce(function(t,e){return"class"!==t.name&&"style"!==t.name&&(t[e.name]=e.value),t},{}),b=ep("parseNodeAttributes",{},t);return s({iconName:m,prefix:p,transform:tC,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:h,styles:u.styleParser?(f=t.getAttribute("style"),c=[],f&&(c=f.split(";").reduce(function(t,e){var n=e.split(":"),a=n[0],r=n.slice(1);return a&&r.length>0&&(t[a]=r.join(":").trim()),t},{})),c):[],attributes:g}},b)}var eH=tq.styles;function eJ(t){var e="nest"===tF.autoReplaceSvg?eq(t,{styleParser:!1}):eq(t);return~e.extra.classes.indexOf(tk)?eg("generateLayersText",t,e):eg("generateSvgReplacementMutation",t,e)}function eB(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!P)return Promise.resolve();var n=S.documentElement.classList,a=function(t){return n.add("".concat(tf,"-").concat(t))},r=function(t){return n.remove("".concat(tf,"-").concat(t))},o=tF.autoFetchSvg?[].concat(f(K),f(Z)):F.concat(Object.keys(eH));o.includes("fa")||o.push("fa");var i=[".".concat(tk,":not([").concat(to,"])")].concat(o.map(function(t){return".".concat(t,":not([").concat(to,"])")})).join(", ");if(0===i.length)return Promise.resolve();var s=[];try{s=tM(t.querySelectorAll(i))}catch(t){}if(!(s.length>0))return Promise.resolve();a("pending"),r("complete");var l=eC.begin("onTree"),c=s.reduce(function(t,e){try{var n=eJ(e);n&&t.push(n)}catch(t){td||"MissingIcon"!==t.name||console.error(t)}return t},[]);return new Promise(function(t,n){Promise.all(c).then(function(n){eW(n,function(){a("active"),a("complete"),r("pending"),"function"==typeof e&&e(),l(),t()})}).catch(function(t){l(),n(t)})})}function eV(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;eJ(t).then(function(t){t&&eW([t],e)})}var eK=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.transform,a=void 0===n?tC:n,r=e.symbol,o=void 0!==r&&r,i=e.mask,l=void 0===i?null:i,f=e.maskId,c=void 0===f?null:f,u=e.classes,d=void 0===u?[]:u,m=e.attributes,p=void 0===m?{}:m,h=e.styles,g=void 0===h?{}:h;if(t){var b=t.prefix,y=t.iconName,v=t.icon;return ew(s({type:"icon"},t),function(){return eh("beforeDOMElementCreation",{iconDefinition:t,params:e}),ek({icons:{main:eA(v),mask:l?eA(l.icon):{found:!1,width:null,height:null,icon:{}}},prefix:b,iconName:y,transform:s(s({},tC),a),symbol:o,maskId:c,extra:{attributes:p,styles:g,classes:d}})})}},eX=RegExp('"',"ug"),eG=s(s(s(s({},{FontAwesome:{normal:"fas",400:"fas"}}),{"Font Awesome 7 Free":{900:"fas",400:"far"},"Font Awesome 7 Pro":{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},"Font Awesome 7 Brands":{400:"fab",normal:"fab"},"Font Awesome 7 Duotone":{900:"fad",400:"fadr",normal:"fadr",300:"fadl",100:"fadt"},"Font Awesome 7 Sharp":{900:"fass",400:"fasr",normal:"fasr",300:"fasl",100:"fast"},"Font Awesome 7 Sharp Duotone":{900:"fasds",400:"fasdr",normal:"fasdr",300:"fasdl",100:"fasdt"},"Font Awesome 7 Jelly":{400:"fajr",normal:"fajr"},"Font Awesome 7 Jelly Fill":{400:"fajfr",normal:"fajfr"},"Font Awesome 7 Jelly Duo":{400:"fajdr",normal:"fajdr"},"Font Awesome 7 Slab":{400:"faslr",normal:"faslr"},"Font Awesome 7 Slab Press":{400:"faslpr",normal:"faslpr"},"Font Awesome 7 Thumbprint":{300:"fatl",normal:"fatl"},"Font Awesome 7 Notdog":{900:"fans",normal:"fans"},"Font Awesome 7 Notdog Duo":{900:"fands",normal:"fands"},"Font Awesome 7 Etch":{900:"faes",normal:"faes"},"Font Awesome 7 Chisel":{400:"facr",normal:"facr"},"Font Awesome 7 Whiteboard":{600:"fawsb",normal:"fawsb"}}),{"Font Awesome 5 Free":{900:"fas",400:"far"},"Font Awesome 5 Pro":{900:"fas",400:"far",normal:"far",300:"fal"},"Font Awesome 5 Brands":{400:"fab",normal:"fab"},"Font Awesome 5 Duotone":{900:"fad"}}),{"Font Awesome Kit":{400:"fak",normal:"fak"},"Font Awesome Kit Duotone":{400:"fakd",normal:"fakd"}}),e$=Object.keys(eG).reduce(function(t,e){return t[e.toLowerCase()]=eG[e],t},{}),eQ=Object.keys(e$).reduce(function(t,e){var n=e$[e];return t[e]=n[900]||f(Object.entries(n))[0][1],t},{});function eZ(t,e){var n="".concat("data-fa-pseudo-element-pending").concat(e.replace(":","-"));return new Promise(function(a,r){if(null!==t.getAttribute(n))return a();var o=tM(t.children).filter(function(t){return t.getAttribute(ti)===e})[0],i=j.getComputedStyle(t,e),l=i.getPropertyValue("font-family"),c=l.match(tj),u=i.getPropertyValue("font-weight"),d=i.getPropertyValue("content");if(o&&!c)return t.removeChild(o),a();if(c&&"none"!==d&&""!==d){var m=i.getPropertyValue("content"),p=(x=l.replace(/^['"]|['"]$/g,"").toLowerCase(),k=isNaN(w=parseInt(u))?"normal":w,(e$[x]||{})[k]||eQ[x]),h=tG(f(m.replace(eX,""))[0]||""),g=c[0].startsWith("FontAwesome"),b=(A=i.getPropertyValue("font-feature-settings").includes("ss01"),I=(P=(O=i.getPropertyValue("content").replace(eX,"")).codePointAt(0))>=1105920&&P<=1112319,N=2===O.length&&O[0]===O[1],I||N||A),y=t8(p,h),v=y;if(g){var x,w,k,A,O,P,I,N,F,E,C=(F=t3[h],E=t8("fas",h),F||(E?{prefix:"fas",iconName:E}:null)||{prefix:null,iconName:null});C.iconName&&C.prefix&&(y=C.iconName,p=C.prefix)}if(!y||b||o&&o.getAttribute(ts)===p&&o.getAttribute(tl)===v)a();else{t.setAttribute(n,v),o&&t.removeChild(o);var z={iconName:null,prefix:null,transform:tC,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}},M=z.extra;M.attributes[ti]=e,eP(y,p).then(function(r){var o=ek(s(s({},z),{},{icons:{main:r,mask:en()},prefix:p,iconName:v,extra:M,watchable:!0})),i=S.createElementNS("http://www.w3.org/2000/svg","svg");"::before"===e?t.insertBefore(i,t.firstChild):t.appendChild(i),i.outerHTML=o.map(function(t){return tV(t)}).join("\n"),t.removeAttribute(n),a()}).catch(r)}}else a()})}function e0(t){return Promise.all([eZ(t,"::before"),eZ(t,"::after")])}function e1(t){return t.parentNode!==document.head&&!~tc.indexOf(t.tagName.toUpperCase())&&!t.getAttribute(ti)&&(!t.parentNode||"svg"!==t.parentNode.tagName)}var e2=function(t){return!!t&&tu.some(function(e){return t.includes(e)})},e4=function(t){if(!t)return[];for(var e=new Set,n=[t],a=0,o=[/(?=\s:)/,/(?<=\)\)?[^,]*,)/];a<o.length;a++)!function(){var t=o[a];n=n.flatMap(function(e){return e.split(t).map(function(t){return t.replace(/,\s*$/,"").trim()})})}();var i,s=r(n=n.flatMap(function(t){return t.includes("(")?t:t.split(",").map(function(t){return t.trim()})}));try{for(s.s();!(i=s.n()).done;){var l=i.value;if(e2(l)){var f=tu.reduce(function(t,e){return t.replace(e,"")},l);""!==f&&"*"!==f&&e.add(f)}}}catch(t){s.e(t)}finally{s.f()}return e};function e5(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(P){if(e)n=t;else if(tF.searchPseudoElementsFullScan)n=t.querySelectorAll("*");else{var n,a,o=new Set,i=r(document.styleSheets);try{for(i.s();!(a=i.n()).done;){var s=a.value;try{var l,f=r(s.cssRules);try{for(f.s();!(l=f.n()).done;){var c,u=l.value,d=e4(u.selectorText),m=r(d);try{for(m.s();!(c=m.n()).done;){var p=c.value;o.add(p)}}catch(t){m.e(t)}finally{m.f()}}}catch(t){f.e(t)}finally{f.f()}}catch(t){tF.searchPseudoElementsWarnings&&console.warn("Font Awesome: cannot parse stylesheet: ".concat(s.href," (").concat(t.message,')\nIf it declares any Font Awesome CSS pseudo-elements, they will not be rendered as SVG icons. Add crossorigin="anonymous" to the <link>, enable searchPseudoElementsFullScan for slower but more thorough DOM parsing, or suppress this warning by setting searchPseudoElementsWarnings to false.'))}}}catch(t){i.e(t)}finally{i.f()}if(!o.size)return;var h=Array.from(o).join(", ");try{n=t.querySelectorAll(h)}catch(t){}}return new Promise(function(t,e){var a=tM(n).filter(e1).map(e0),r=eC.begin("searchPseudoElements");eY=!0,Promise.all(a).then(function(){r(),eY=!1,t()}).catch(function(){r(),eY=!1,e()})})}}var e7=!1,e3=function(t){return t.toLowerCase().split(" ").reduce(function(t,e){var n=e.toLowerCase().split("-"),a=n[0],r=n.slice(1).join("-");if(a&&"h"===r)return t.flipX=!0,t;if(a&&"v"===r)return t.flipY=!0,t;if(isNaN(r=parseFloat(r)))return t;switch(a){case"grow":t.size=t.size+r;break;case"shrink":t.size=t.size-r;break;case"left":t.x=t.x-r;break;case"right":t.x=t.x+r;break;case"up":t.y=t.y-r;break;case"down":t.y=t.y+r;break;case"rotate":t.rotate=t.rotate+r}return t},{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0})},e6={x:0,y:0,width:"100%",height:"100%"};function e9(t){var e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return t.attributes&&(t.attributes.fill||e)&&(t.attributes.fill="black"),t}!function(t,e){var n=e.mixoutsTo;ec=t,eu={},Object.keys(ed).forEach(function(t){-1===em.indexOf(t)&&delete ed[t]}),ec.forEach(function(t){var e=t.mixout?t.mixout():{};if(Object.keys(e).forEach(function(t){"function"==typeof e[t]&&(n[t]=e[t]),"object"===u(e[t])&&Object.keys(e[t]).forEach(function(a){n[t]||(n[t]={}),n[t][a]=e[t][a]})}),t.hooks){var a=t.hooks();Object.keys(a).forEach(function(t){eu[t]||(eu[t]=[]),eu[t].push(a[t])})}t.provides&&t.provides(ed)})}([{mixout:function(){return{dom:{css:tW,insertCss:t_}}},hooks:function(){return{beforeDOMElementCreation:function(){t_()},beforeI2svg:function(){t_()}}}},{mixout:function(){return{icon:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(t||{}).icon?t:eb(t||{}),a=e.mask;return a&&(a=(a||{}).icon?a:eb(a||{})),eK(n,s(s({},e),{},{mask:a}))}}},hooks:function(){return{mutationObserverCallbacks:function(t){return t.treeCallback=eB,t.nodeCallback=eV,t}}},provides:function(t){t.i2svg=function(t){var e=t.node,n=void 0===e?S:e,a=t.callback;return eB(n,void 0===a?function(){}:a)},t.generateSvgReplacementMutation=function(t,e){var n=e.iconName,a=e.prefix,r=e.transform,o=e.symbol,i=e.mask,s=e.maskId,f=e.extra;return new Promise(function(e,c){Promise.all([eP(n,a),i.iconName?eP(i.iconName,i.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then(function(i){var c=l(i,2);e([t,ek({icons:{main:c[0],mask:c[1]},prefix:a,iconName:n,transform:r,symbol:o,maskId:s,extra:f,watchable:!0})])}).catch(c)})},t.generateAbstractIcon=function(t){var e,n=t.children,a=t.attributes,r=t.main,o=t.transform,i=tR(t.styles);return i.length>0&&(a.style=i),tL(o)&&(e=eg("generateAbstractTransformGrouping",{main:r,transform:o,containerWidth:r.width,iconWidth:r.width})),n.push(e||r.icon),{children:n,attributes:a}}}},{mixout:function(){return{layer:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.classes,a=void 0===n?[]:n;return ew({type:"layer"},function(){eh("beforeDOMElementCreation",{assembler:t,params:e});var n=[];return t(function(t){Array.isArray(t)?t.map(function(t){n=n.concat(t.abstract)}):n=n.concat(t.abstract)}),[{tag:"span",attributes:{class:["".concat(tF.cssPrefix,"-layers")].concat(f(a)).join(" ")},children:n}]})}}}},{mixout:function(){return{counter:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.title,a=void 0===n?null:n,r=e.classes,o=void 0===r?[]:r,i=e.attributes,l=void 0===i?{}:i,c=e.styles,u=void 0===c?{}:c;return ew({type:"counter",content:t},function(){var n,r,i,c,d,m;return eh("beforeDOMElementCreation",{content:t,params:e}),r=(n={content:t.toString(),title:a,extra:{attributes:l,styles:u,classes:["".concat(tF.cssPrefix,"-layers-counter")].concat(f(o))}}).content,c=s(s({},(i=n.extra).attributes),{},{class:i.classes.join(" ")}),(d=tR(i.styles)).length>0&&(c.style=d),(m=[]).push({tag:"span",attributes:c,children:[r]}),m})}}}},{mixout:function(){return{text:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.transform,a=void 0===n?tC:n,r=e.classes,o=void 0===r?[]:r,i=e.attributes,l=void 0===i?{}:i,c=e.styles,u=void 0===c?{}:c;return ew({type:"text",content:t},function(){return eh("beforeDOMElementCreation",{content:t,params:e}),ej({content:t,transform:s(s({},tC),a),extra:{attributes:l,styles:u,classes:["".concat(tF.cssPrefix,"-layers-text")].concat(f(o))}})})}}},provides:function(t){t.generateLayersText=function(t,e){var n=e.transform,a=e.extra,r=null,o=null;if(I){var i=parseInt(getComputedStyle(t).fontSize,10),s=t.getBoundingClientRect();r=s.width/i,o=s.height/i}return Promise.resolve([t,ej({content:t.innerHTML,width:r,height:o,transform:n,extra:a,watchable:!0})])}}},{hooks:function(){return{mutationObserverCallbacks:function(t){return t.pseudoElementsCallback=e5,t}}},provides:function(t){t.pseudoElements2svg=function(t){var e=t.node,n=void 0===e?S:e;tF.searchPseudoElements&&e5(n)}}},{mixout:function(){return{dom:{unwatch:function(){eY=!0,e7=!0}}}},hooks:function(){return{bootstrap:function(){eU(ep("mutationObserverCallbacks",{}))},noAuto:function(){e_&&e_.disconnect()},watch:function(t){var e=t.observeMutationsRoot;e7?eY=!1:eU(ep("mutationObserverCallbacks",{observeMutationsRoot:e}))}}}},{mixout:function(){return{parse:{transform:function(t){return e3(t)}}}},hooks:function(){return{parseNodeAttributes:function(t,e){var n=e.getAttribute("data-fa-transform");return n&&(t.transform=e3(n)),t}}},provides:function(t){t.generateAbstractTransformGrouping=function(t){var e=t.main,n=t.transform,a=t.containerWidth,r=t.iconWidth,o="translate(".concat(32*n.x,", ").concat(32*n.y,") "),i="scale(".concat(n.size/16*(n.flipX?-1:1),", ").concat(n.size/16*(n.flipY?-1:1),") "),l="rotate(".concat(n.rotate," 0 0)"),f={transform:"".concat(o," ").concat(i," ").concat(l)},c={outer:{transform:"translate(".concat(a/2," 256)")},inner:f,path:{transform:"translate(".concat(-(r/2*1)," -256)")}};return{tag:"g",attributes:s({},c.outer),children:[{tag:"g",attributes:s({},c.inner),children:[{tag:e.icon.tag,children:e.icon.children,attributes:s(s({},e.icon.attributes),c.path)}]}]}}}},{hooks:function(){return{parseNodeAttributes:function(t,e){var n=e.getAttribute("data-fa-mask"),a=n?ei(n.split(" ").map(function(t){return t.trim()})):en();return a.prefix||(a.prefix=t2),t.mask=a,t.maskId=e.getAttribute("data-fa-mask-id"),t}}},provides:function(t){t.generateAbstractMask=function(t){var e,n,a,r,o,i,l,f,c=t.children,u=t.attributes,d=t.main,m=t.mask,p=t.maskId,h=t.transform,g=d.width,b=d.icon,y=m.width,v=m.icon,x=(n=(e={transform:h,containerWidth:y,iconWidth:g}).transform,a=e.containerWidth,r=e.iconWidth,o="translate(".concat(32*n.x,", ").concat(32*n.y,") "),i="scale(".concat(n.size/16*(n.flipX?-1:1),", ").concat(n.size/16*(n.flipY?-1:1),") "),l="rotate(".concat(n.rotate," 0 0)"),f={transform:"".concat(o," ").concat(i," ").concat(l)},{outer:{transform:"translate(".concat(a/2," 256)")},inner:f,path:{transform:"translate(".concat(-(r/2*1)," -256)")}}),w={tag:"rect",attributes:s(s({},e6),{},{fill:"white"})},k=b.children?{children:b.children.map(e9)}:{},j={tag:"g",attributes:s({},x.inner),children:[e9(s({tag:b.tag,attributes:s(s({},b.attributes),x.path)},k))]},S={tag:"g",attributes:s({},x.outer),children:[j]},A="mask-".concat(p||tz()),O="clip-".concat(p||tz()),P={tag:"mask",attributes:s(s({},e6),{},{id:A,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[w,S]},I={tag:"defs",children:[{tag:"clipPath",attributes:{id:O},children:"g"===v.tag?v.children:[v]},P]};return c.push(I,{tag:"rect",attributes:s({fill:"currentColor","clip-path":"url(#".concat(O,")"),mask:"url(#".concat(A,")")},e6)}),{children:c,attributes:u}}}},{provides:function(t){var e=!1;j.matchMedia&&(e=j.matchMedia("(prefers-reduced-motion: reduce)").matches),t.missingIconAbstract=function(){var t=[],n={fill:"currentColor"},a={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};t.push({tag:"path",attributes:s(s({},n),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});var r=s(s({},a),{},{attributeName:"opacity"}),o={tag:"circle",attributes:s(s({},n),{},{cx:"256",cy:"364",r:"28"}),children:[]};return e||o.children.push({tag:"animate",attributes:s(s({},a),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:s(s({},r),{},{values:"1;0;1;1;0;1;"})}),t.push(o),t.push({tag:"path",attributes:s(s({},n),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:e?[]:[{tag:"animate",attributes:s(s({},r),{},{values:"1;0;0;0;0;1;"})}]}),e||t.push({tag:"path",attributes:s(s({},n),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:s(s({},r),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:t}}}},{hooks:function(){return{parseNodeAttributes:function(t,e){var n=e.getAttribute("data-fa-symbol");return t.symbol=null!==n&&(""===n||n),t}}}}],{mixoutsTo:ev}),ev.noAuto,ev.config;var e8=ev.library;ev.dom;var nt=ev.parse;ev.findIconDefinition,ev.toHtml;var ne=ev.icon;ev.layer,ev.text,ev.counter},36505:(t,e,n)=>{"use strict";n.d(e,{g:()=>j});var a,r=n(4181),o=n(38637),i=n.n(o),s=n(12115),l=n(49509);function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=Array(e);n<e;n++)a[n]=t[n];return a}function c(t,e,n){var a;return(e="symbol"==typeof(a=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?a:a+"")in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,a)}return n}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach(function(e){c(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function m(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var a,r,o,i,s=[],l=!0,f=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=o.call(n)).done)&&(s.push(a.value),s.length!==e);l=!0);}catch(t){f=!0,r=t}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(f)throw r}}return s}}(t,e)||g(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t){return function(t){if(Array.isArray(t))return f(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||g(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t){return(h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function g(t,e){if(t){if("string"==typeof t)return f(t,e);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(t,e):void 0}}try{a=n(67361).version}catch(t){a=l.env.FA_VERSION||"7.0.0-alpha8"}function b(t){var e;return(e=t-0)==e?t:(t=t.replace(/[\-_\s]+(.)?/g,function(t,e){return e?e.toUpperCase():""})).substr(0,1).toLowerCase()+t.substr(1)}var y=["style"],v=!1;try{v=!0}catch(t){}function x(t){return t&&"object"===h(t)&&t.prefix&&t.iconName&&t.icon?t:r.qg.icon?r.qg.icon(t):null===t?null:t&&"object"===h(t)&&t.prefix&&t.iconName?t:Array.isArray(t)&&2===t.length?{prefix:t[0],iconName:t[1]}:"string"==typeof t?{prefix:"fas",iconName:t}:void 0}function w(t,e){return Array.isArray(e)&&e.length>0||!Array.isArray(e)&&e?c({},t,e):{}}var k={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,rotateBy:!1,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1,widthAuto:!1},j=s.forwardRef(function(t,e){var n,o,i,s,l,f,u,h,g,b,y,j,A,O,P,I,N,F,E,C,z,M,D,T=d(d({},k),t),R=T.icon,L=T.mask,W=T.symbol,Y=T.className,_=T.title,U=T.titleId,q=T.maskId,H=x(R),J=w("classes",[].concat(p((n=T.beat,o=T.fade,i=T.beatFade,s=T.bounce,l=T.shake,f=T.flash,u=T.spin,h=T.spinPulse,g=T.spinReverse,b=T.pulse,y=T.fixedWidth,j=T.inverse,A=T.border,O=T.listItem,P=T.flip,I=T.size,N=T.rotation,F=T.pull,E=T.swapOpacity,C=T.rotateBy,z=T.widthAuto,M=function(t,e){for(var n=m(t.split("-"),2),a=n[0],r=n[1],o=m(e.split("-"),2),i=o[0],s=o[1],l=a.split("."),f=i.split("."),c=0;c<Math.max(l.length,f.length);c++){var u=l[c]||"0",d=f[c]||"0",p=parseInt(u,10),h=parseInt(d,10);if(p!==h)return p>h}for(var g=0;g<Math.max(l.length,f.length);g++){var b=l[g]||"0",y=f[g]||"0";if(b!==y&&b.length!==y.length)return b.length<y.length}return!r||!!s}(a,"7.0.0-alpha1"),Object.keys(D=c(c(c(c(c(c({"fa-beat":n,"fa-fade":o,"fa-beat-fade":i,"fa-bounce":s,"fa-shake":l,"fa-flash":f,"fa-spin":u,"fa-spin-reverse":g,"fa-spin-pulse":h,"fa-pulse":b,"fa-fw":y,"fa-inverse":j,"fa-border":A,"fa-li":O,"fa-flip":!0===P,"fa-flip-horizontal":"horizontal"===P||"both"===P,"fa-flip-vertical":"vertical"===P||"both"===P},"fa-".concat(I),null!=I),"fa-rotate-".concat(N),null!=N&&0!==N),"fa-pull-".concat(F),null!=F),"fa-swap-opacity",E),"fa-rotate-by",M&&C),"fa-width-auto",M&&z)).map(function(t){return D[t]?t:null}).filter(function(t){return t}))),p((Y||"").split(" ")))),B=w("transform","string"==typeof T.transform?r.qg.transform(T.transform):T.transform),V=w("mask",x(L)),K=(0,r.Kk)(H,d(d(d(d({},J),B),V),{},{symbol:W,title:_,titleId:U,maskId:q}));if(!K)return!function(){if(!v&&console&&"function"==typeof console.error){var t;(t=console).error.apply(t,arguments)}}("Could not find icon",H),null;var X=K.abstract,G={ref:e};return Object.keys(T).forEach(function(t){k.hasOwnProperty(t)||(G[t]=T[t])}),S(X[0],G)});j.displayName="FontAwesomeIcon",j.propTypes={beat:i().bool,border:i().bool,beatFade:i().bool,bounce:i().bool,className:i().string,fade:i().bool,flash:i().bool,mask:i().oneOfType([i().object,i().array,i().string]),maskId:i().string,fixedWidth:i().bool,inverse:i().bool,flip:i().oneOf([!0,!1,"horizontal","vertical","both"]),icon:i().oneOfType([i().object,i().array,i().string]),listItem:i().bool,pull:i().oneOf(["right","left"]),pulse:i().bool,rotation:i().oneOf([0,90,180,270]),rotateBy:i().bool,shake:i().bool,size:i().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:i().bool,spinPulse:i().bool,spinReverse:i().bool,symbol:i().oneOfType([i().bool,i().string]),title:i().string,titleId:i().string,transform:i().oneOfType([i().string,i().object]),swapOpacity:i().bool,widthAuto:i().bool};var S=(function t(e,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof n)return n;var r=(n.children||[]).map(function(n){return t(e,n)}),o=Object.keys(n.attributes||{}).reduce(function(t,e){var a=n.attributes[e];switch(e){case"class":t.attrs.className=a,delete n.attributes.class;break;case"style":t.attrs.style=a.split(";").map(function(t){return t.trim()}).filter(function(t){return t}).reduce(function(t,e){var n=e.indexOf(":"),a=b(e.slice(0,n)),r=e.slice(n+1).trim();return a.startsWith("webkit")?t[a.charAt(0).toUpperCase()+a.slice(1)]=r:t[a]=r,t},{});break;default:0===e.indexOf("aria-")||0===e.indexOf("data-")?t.attrs[e.toLowerCase()]=a:t.attrs[b(e)]=a}return t},{attrs:{}}),i=a.style,s=function(t,e){if(null==t)return{};var n,a,r=function(t,e){if(null==t)return{};var n={};for(var a in t)if(({}).hasOwnProperty.call(t,a)){if(-1!==e.indexOf(a))continue;n[a]=t[a]}return n}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(a=0;a<o.length;a++)n=o[a],-1===e.indexOf(n)&&({}).propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}(a,y);return o.attrs.style=d(d({},o.attrs.style),void 0===i?{}:i),e.apply(void 0,[n.tag,d(d({},o.attrs),s)].concat(p(r)))}).bind(null,s.createElement)},38637:(t,e,n)=>{t.exports=n(79399)()},67361:t=>{"use strict";t.exports=JSON.parse('{"description":"The iconic font, CSS, and SVG framework","keywords":["font","awesome","fontawesome","icon","svg","bootstrap"],"homepage":"https://fontawesome.com","bugs":{"url":"https://github.com/FortAwesome/Font-Awesome/issues"},"author":"The Font Awesome Team (https://github.com/orgs/FortAwesome/people)","repository":{"type":"git","url":"https://github.com/FortAwesome/Font-Awesome"},"engines":{"node":">=6"},"dependencies":{"@fortawesome/fontawesome-common-types":"7.0.0"},"version":"7.0.0","name":"@fortawesome/fontawesome-svg-core","main":"index.js","module":"index.mjs","jsnext:main":"index.mjs","style":"styles.css","license":"MIT","types":"./index.d.ts","exports":{".":{"types":"./index.d.ts","module":"./index.mjs","import":"./index.mjs","require":"./index.js","style":"./styles.css","default":"./index.js"},"./index":{"types":"./index.d.ts","module":"./index.mjs","import":"./index.mjs","require":"./index.js","default":"./index.js"},"./index.js":{"types":"./index.d.ts","module":"./index.mjs","import":"./index.mjs","require":"./index.js","default":"./index.js"},"./plugins":{"types":"./index.d.ts","module":"./plugins.mjs","import":"./plugins.mjs","default":"./plugins.mjs"},"./import.macro":"./import.macro.js","./import.macro.js":"./import.macro.js","./styles":"./styles.css","./styles.css":"./styles.css","./package.json":"./package.json"},"sideEffects":["./index.js","./index.mjs","./styles.css"]}')},72948:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},79399:(t,e,n)=>{"use strict";var a=n(72948);function r(){}function o(){}o.resetWarningCache=r,t.exports=function(){function t(t,e,n,r,o,i){if(i!==a){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function e(){return t}t.isRequired=t;var n={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:o,resetWarningCache:r};return n.PropTypes=n,n}}}]);