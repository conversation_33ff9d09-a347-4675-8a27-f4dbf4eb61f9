-- =====================================================
-- 修复 tags 表问题
-- =====================================================
-- 版本: 1.0.0
-- 创建时间: 2025-07-26
-- 描述: 确保 tags 表和相关功能正常工作
-- =====================================================

-- 检查并创建 tags 表
DO $$
BEGIN
    -- 检查 tags 表是否存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'tags' AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '创建 tags 表...';
        
        -- 创建 tags 表
        CREATE TABLE tags (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            color VARCHAR(7) DEFAULT '#10b981',
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            
            CONSTRAINT tags_name_user_unique UNIQUE(name, user_id)
        );
        
        RAISE NOTICE '✅ tags 表创建成功';
    ELSE
        RAISE NOTICE '✅ tags 表已存在';
    END IF;
END $$;

-- 检查并创建 prompt_tags 表
DO $$
BEGIN
    -- 检查 prompt_tags 表是否存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'prompt_tags' AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '创建 prompt_tags 表...';
        
        -- 创建 prompt_tags 表
        CREATE TABLE prompt_tags (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            prompt_id UUID REFERENCES prompts(id) ON DELETE CASCADE,
            tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            
            CONSTRAINT prompt_tags_unique UNIQUE(prompt_id, tag_id)
        );
        
        RAISE NOTICE '✅ prompt_tags 表创建成功';
    ELSE
        RAISE NOTICE '✅ prompt_tags 表已存在';
    END IF;
END $$;

-- 创建索引
DO $$
BEGIN
    -- tags 表索引
    CREATE INDEX IF NOT EXISTS idx_tags_user_id ON tags(user_id);
    CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
    
    -- prompt_tags 表索引
    CREATE INDEX IF NOT EXISTS idx_prompt_tags_prompt_id ON prompt_tags(prompt_id);
    CREATE INDEX IF NOT EXISTS idx_prompt_tags_tag_id ON prompt_tags(tag_id);
    
    RAISE NOTICE '✅ 索引创建完成';
END $$;

-- 启用 RLS
DO $$
BEGIN
    -- 启用 tags 表 RLS
    ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
    
    -- 启用 prompt_tags 表 RLS
    ALTER TABLE prompt_tags ENABLE ROW LEVEL SECURITY;
    
    RAISE NOTICE '✅ RLS 已启用';
END $$;

-- 创建 RLS 策略
DO $$
BEGIN
    -- tags 表 RLS 策略
    DROP POLICY IF EXISTS "Users can view their own tags" ON tags;
    CREATE POLICY "Users can view their own tags" ON tags
        FOR SELECT USING (auth.uid() = user_id);

    DROP POLICY IF EXISTS "Users can insert their own tags" ON tags;
    CREATE POLICY "Users can insert their own tags" ON tags
        FOR INSERT WITH CHECK (auth.uid() = user_id);

    DROP POLICY IF EXISTS "Users can update their own tags" ON tags;
    CREATE POLICY "Users can update their own tags" ON tags
        FOR UPDATE USING (auth.uid() = user_id);

    DROP POLICY IF EXISTS "Users can delete their own tags" ON tags;
    CREATE POLICY "Users can delete their own tags" ON tags
        FOR DELETE USING (auth.uid() = user_id);

    -- prompt_tags 表 RLS 策略
    DROP POLICY IF EXISTS "Users can view their own prompt_tags" ON prompt_tags;
    CREATE POLICY "Users can view their own prompt_tags" ON prompt_tags
        FOR SELECT USING (
            EXISTS (
                SELECT 1 FROM prompts 
                WHERE prompts.id = prompt_tags.prompt_id 
                AND prompts.user_id = auth.uid()
            )
        );

    DROP POLICY IF EXISTS "Users can insert their own prompt_tags" ON prompt_tags;
    CREATE POLICY "Users can insert their own prompt_tags" ON prompt_tags
        FOR INSERT WITH CHECK (
            EXISTS (
                SELECT 1 FROM prompts 
                WHERE prompts.id = prompt_tags.prompt_id 
                AND prompts.user_id = auth.uid()
            )
        );

    DROP POLICY IF EXISTS "Users can delete their own prompt_tags" ON prompt_tags;
    CREATE POLICY "Users can delete their own prompt_tags" ON prompt_tags
        FOR DELETE USING (
            EXISTS (
                SELECT 1 FROM prompts 
                WHERE prompts.id = prompt_tags.prompt_id 
                AND prompts.user_id = auth.uid()
            )
        );
    
    RAISE NOTICE '✅ RLS 策略创建完成';
END $$;

-- 创建一些默认标签（可选）
DO $$
DECLARE
    current_user_id UUID;
BEGIN
    -- 获取当前用户 ID
    current_user_id := auth.uid();
    
    IF current_user_id IS NOT NULL THEN
        -- 插入一些默认标签（如果不存在）
        INSERT INTO tags (name, color, user_id) VALUES
            ('重要', '#ef4444', current_user_id),
            ('工作', '#3b82f6', current_user_id),
            ('学习', '#10b981', current_user_id),
            ('代码', '#8b5cf6', current_user_id),
            ('创意', '#f59e0b', current_user_id)
        ON CONFLICT (name, user_id) DO NOTHING;
        
        RAISE NOTICE '✅ 默认标签创建完成';
    ELSE
        RAISE NOTICE '⚠️ 未检测到当前用户，跳过默认标签创建';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '⚠️ 默认标签创建失败（这是正常的，如果用户未登录）';
END $$;

-- 验证表结构
DO $$
DECLARE
    tags_count INTEGER;
    prompt_tags_count INTEGER;
BEGIN
    -- 检查表是否可以查询
    SELECT COUNT(*) INTO tags_count FROM tags;
    SELECT COUNT(*) INTO prompt_tags_count FROM prompt_tags;
    
    RAISE NOTICE '🎉 tags 表修复完成！';
    RAISE NOTICE '📊 当前 tags 表记录数: %', tags_count;
    RAISE NOTICE '📊 当前 prompt_tags 表记录数: %', prompt_tags_count;
    RAISE NOTICE '✅ 所有表都可以正常访问';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ 表访问测试失败: %', SQLERRM;
END $$;
