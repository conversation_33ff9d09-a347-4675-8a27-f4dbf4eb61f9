-- =====================================================
-- 性能优化脚本
-- =====================================================
-- 版本: 1.0.0
-- 创建时间: 2025-07-26
-- 描述: 优化数据库查询性能，添加必要的索引
-- =====================================================

-- 1. 优化分类查询索引
CREATE INDEX IF NOT EXISTS idx_categories_user_sort_active 
ON categories(user_id, sort_order) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_categories_user_created 
ON categories(user_id, created_at DESC) 
WHERE deleted_at IS NULL;

-- 2. 优化提示词查询索引
CREATE INDEX IF NOT EXISTS idx_prompts_user_category_active 
ON prompts(user_id, category_id) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_prompts_user_created 
ON prompts(user_id, created_at DESC) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_prompts_user_updated 
ON prompts(user_id, updated_at DESC) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_prompts_usage_count 
ON prompts(usage_count DESC) 
WHERE deleted_at IS NULL;

-- 3. 优化搜索相关索引
CREATE INDEX IF NOT EXISTS idx_prompts_title_search 
ON prompts USING gin(to_tsvector('simple', title)) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_prompts_content_search 
ON prompts USING gin(to_tsvector('simple', content)) 
WHERE deleted_at IS NULL;

-- 4. 优化标签查询索引（检查字段是否存在）
DO $$
BEGIN
    -- 检查 tags 表是否有 deleted_at 字段
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'tags' AND column_name = 'deleted_at'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_tags_user_name
        ON tags(user_id, name)
        WHERE deleted_at IS NULL;
    ELSE
        CREATE INDEX IF NOT EXISTS idx_tags_user_name
        ON tags(user_id, name);
    END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_prompt_tags_prompt
ON prompt_tags(prompt_id);

CREATE INDEX IF NOT EXISTS idx_prompt_tags_tag
ON prompt_tags(tag_id);

-- 5. 优化用户偏好查询
CREATE INDEX IF NOT EXISTS idx_user_preferences_user
ON user_preferences(user_id);

-- 6. 优化搜索历史查询
DO $$
BEGIN
    -- 检查 search_history 表是否存在
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'search_history'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_search_history_user_created
        ON search_history(user_id, created_at DESC);
    END IF;
END $$;

-- 7. 创建复合查询优化视图
CREATE OR REPLACE VIEW v_prompts_with_details AS
SELECT 
    p.*,
    c.name as category_name,
    c.color as category_color,
    c.icon as category_icon,
    COALESCE(tag_list.tags, '[]'::json) as tags
FROM prompts p
LEFT JOIN categories c ON p.category_id = c.id AND c.deleted_at IS NULL
LEFT JOIN (
    SELECT 
        pt.prompt_id,
        json_agg(
            json_build_object(
                'id', t.id,
                'name', t.name,
                'color', t.color
            )
        ) as tags
    FROM prompt_tags pt
    JOIN tags t ON pt.tag_id = t.id AND t.deleted_at IS NULL
    GROUP BY pt.prompt_id
) tag_list ON p.id = tag_list.prompt_id
WHERE p.deleted_at IS NULL;

-- 8. 创建分类统计优化视图
CREATE OR REPLACE VIEW v_categories_with_count AS
SELECT 
    c.*,
    COALESCE(prompt_count.count, 0) as prompt_count
FROM categories c
LEFT JOIN (
    SELECT 
        category_id,
        COUNT(*) as count
    FROM prompts 
    WHERE deleted_at IS NULL
    GROUP BY category_id
) prompt_count ON c.id = prompt_count.category_id
WHERE c.deleted_at IS NULL;

-- 9. 创建高效的搜索函数
CREATE OR REPLACE FUNCTION search_prompts_optimized(
    search_query TEXT,
    user_uuid UUID,
    category_filter UUID DEFAULT NULL,
    tag_filters UUID[] DEFAULT NULL,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    category_id UUID,
    category_name TEXT,
    category_color TEXT,
    usage_count INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    tags JSON
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.title,
        p.content,
        p.category_id,
        p.category_name,
        p.category_color,
        p.usage_count,
        p.created_at,
        p.updated_at,
        p.tags
    FROM v_prompts_with_details p
    WHERE 
        p.user_id = user_uuid
        AND (
            search_query IS NULL 
            OR search_query = '' 
            OR p.title ILIKE '%' || search_query || '%'
            OR p.content ILIKE '%' || search_query || '%'
        )
        AND (category_filter IS NULL OR p.category_id = category_filter)
        AND (
            tag_filters IS NULL 
            OR tag_filters = ARRAY[]::UUID[]
            OR EXISTS (
                SELECT 1 FROM prompt_tags pt 
                WHERE pt.prompt_id = p.id 
                AND pt.tag_id = ANY(tag_filters)
            )
        )
    ORDER BY 
        CASE WHEN search_query IS NOT NULL AND search_query != '' THEN
            CASE 
                WHEN p.title ILIKE search_query || '%' THEN 1
                WHEN p.title ILIKE '%' || search_query || '%' THEN 2
                WHEN p.content ILIKE '%' || search_query || '%' THEN 3
                ELSE 4
            END
        ELSE 0 END,
        p.updated_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql STABLE;

-- 10. 授予权限
GRANT SELECT ON v_prompts_with_details TO authenticated;
GRANT SELECT ON v_categories_with_count TO authenticated;
GRANT EXECUTE ON FUNCTION search_prompts_optimized TO authenticated;

-- 11. 分析表统计信息
ANALYZE categories;
ANALYZE prompts;
ANALYZE tags;
ANALYZE prompt_tags;
ANALYZE user_preferences;
ANALYZE search_history;

-- 验证优化完成
DO $$
BEGIN
    RAISE NOTICE '✅ 数据库性能优化完成';
    RAISE NOTICE '📊 已创建 % 个索引', (
        SELECT COUNT(*) 
        FROM pg_indexes 
        WHERE tablename IN ('categories', 'prompts', 'tags', 'prompt_tags', 'user_preferences', 'search_history')
    );
    RAISE NOTICE '🔍 已创建优化视图和搜索函数';
END $$;
