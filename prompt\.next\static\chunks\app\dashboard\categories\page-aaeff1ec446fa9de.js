(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[38],{53771:(e,s,a)=>{Promise.resolve().then(a.bind(a,81356))},81356:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var r=a(95155),l=a(12115),t=a(75143),n=a(50402),i=a(21206),c=a(97168),o=a(81704),d=a(88298),m=a(57462),x=a(78266),h=a(88145),u=a(19987);function g(e){let{category:s,onEdit:a,onDelete:l}=e,{attributes:t,listeners:i,setNodeRef:d,transform:m,transition:g,isDragging:b}=(0,n.gl)({id:s.id}),j={transform:x.Ks.Transform.toString(m),transition:g};return(0,r.jsxs)("div",{ref:d,style:j,className:"\n        group relative bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 \n        rounded-lg p-4 transition-all duration-200\n        ".concat(b?"shadow-lg scale-105 z-10":"hover:shadow-md hover:border-gray-300 dark:hover:border-gray-500","\n      "),children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{...t,...i,className:"cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600",children:(0,r.jsx)(o.Icon,{name:"bars",className:"h-4 w-4 text-gray-400"})}),(0,r.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg",style:{backgroundColor:"".concat(s.color,"20")},children:(0,r.jsx)(o.Icon,{name:s.icon||"folder",className:"h-5 w-5",color:s.color})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white truncate",children:s.name}),(0,r.jsxs)(h.E,{variant:"secondary",style:{backgroundColor:"".concat(s.color,"20"),color:s.color},children:[s.prompt_count," 个提示词"]})]}),s.description&&(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 line-clamp-2",children:s.description}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("span",{children:["排序: ",s.sort_order]}),(0,r.jsxs)("span",{children:["创建于 ",(0,u.Yq)(s.created_at)]}),s.updated_at!==s.created_at&&(0,r.jsxs)("span",{children:["更新于 ",(0,u.Yq)(s.updated_at)]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>a(s),className:"hover:bg-blue-100 hover:text-blue-600",children:(0,r.jsx)(o.Icon,{name:"edit",className:"h-4 w-4"})}),(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>l(s),className:"hover:bg-red-100 hover:text-red-600",disabled:s.prompt_count>0,children:(0,r.jsx)(o.Icon,{name:"trash",className:"h-4 w-4"})})]})]}),b&&(0,r.jsx)("div",{className:"absolute inset-0 bg-blue-100 dark:bg-blue-900/20 rounded-lg border-2 border-blue-300 dark:border-blue-600"})]})}var b=a(92669),j=a(53580),p=a(34938),f=a(11790);function v(){let[e,s]=(0,l.useState)([]),[a,x]=(0,l.useState)(!0),[h,u]=(0,l.useState)(!1),[v,y]=(0,l.useState)(null),[N,w]=(0,l.useState)(!1),[k,C]=(0,l.useState)(null),[S,I]=(0,l.useState)(!1),{toast:_}=(0,j.dj)(),E=(0,t.FR)((0,t.MS)(t.AN),(0,t.MS)(t.uN,{coordinateGetter:n.JR}));(0,l.useEffect)(()=>{J()},[]);let J=async()=>{try{x(!0),console.log("\uD83D\uDE80 使用本地优先存储加载分类");let e=await f.J.getCategories();s(e),console.log("✅ 加载了 ".concat(e.length," 个分类"))}catch(e){console.error("加载分类失败:",e),_({title:"加载失败",description:"无法加载分类列表",variant:"destructive"})}finally{x(!1)}},F=()=>{y(null),u(!0)},$=e=>{y(e),u(!0)},D=async e=>{try{let s=await (0,p.MN)(e.id);if(s>0)return void _({title:"无法删除",description:"该分类下还有 ".concat(s," 个提示词，请先移动或删除这些提示词"),variant:"destructive"})}catch(e){console.error("检查分类提示词数量失败:",e)}C(e),w(!0)},O=async()=>{if(k)try{if(I(!0),console.log("\uD83D\uDE80 使用本地优先删除分类"),await f.J.deleteCategory(k.id)){s(e=>e.filter(e=>e.id!==k.id)),_({title:"删除成功",description:"分类已成功删除"}),w(!1),C(null),console.log("✅ 分类已从本地状态中移除");let e=f.J.getDetailedSyncStatus();console.log("\uD83D\uDD0D 删除后同步状态:",e)}else throw Error("删除失败")}catch(e){console.error("删除分类失败:",e),_({title:"删除失败",description:"删除分类时出现错误",variant:"destructive"})}finally{I(!1)}},q=async a=>{let{active:r,over:l}=a;if(r.id!==(null==l?void 0:l.id)){let a=e.findIndex(e=>e.id===r.id),t=e.findIndex(e=>e.id===l.id),i=(0,n.be)(e,a,t);s(i);try{let e=i.map(e=>e.id);await (0,p.wp)(e),_({title:"排序已更新",description:"分类排序已成功保存"})}catch(a){console.error("更新分类排序失败:",a),s(e),_({title:"排序失败",description:"更新分类排序时出现错误",variant:"destructive"})}}};return a?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(o.Icon,{name:"spinner",className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"加载中..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)(b.a,{}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white",children:"分类管理"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2",children:"管理您的提示词分类，支持拖拽排序"})]}),(0,r.jsxs)(c.$,{onClick:F,className:"w-full sm:w-auto",children:[(0,r.jsx)(o.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),"新建分类"]})]}),e.length>0?(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold",children:["分类列表 (",e.length,")"]}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"拖拽可调整排序"})]}),(0,r.jsx)(t.Mp,{sensors:E,collisionDetection:t.fp,onDragEnd:q,modifiers:[i.FN],children:(0,r.jsx)(n.gB,{items:e.map(e=>e.id),strategy:n._G,children:(0,r.jsx)("div",{className:"space-y-2",children:e.map(e=>(0,r.jsx)(g,{category:e,onEdit:$,onDelete:D},e.id))})})})]})}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(o.Icon,{name:"folder",className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"暂无分类"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"创建您的第一个分类来组织提示词"}),(0,r.jsxs)(c.$,{onClick:F,children:[(0,r.jsx)(o.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),"创建分类"]})]}),(0,r.jsx)("div",{className:"mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(o.Icon,{name:"info-circle",className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-blue-900 dark:text-blue-100 mb-1",children:"使用说明"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-700 dark:text-blue-200 space-y-1",children:[(0,r.jsx)("li",{children:"• 拖拽分类项可以调整显示顺序"}),(0,r.jsx)("li",{children:"• 删除分类前需要先移动或删除该分类下的所有提示词"}),(0,r.jsx)("li",{children:"• 分类颜色和图标会在侧边栏中显示"}),(0,r.jsx)("li",{children:"• 分类名称在同一用户下必须唯一"})]})]})]})})]}),(0,r.jsx)(d.K,{category:v,isOpen:h,onClose:()=>{u(!1),y(null)},onSuccess:()=>{J()}}),(0,r.jsx)(m.o,{isOpen:N,onClose:()=>{w(!1),C(null)},onConfirm:O,title:"删除分类",description:"此操作无法撤销，确定要删除这个分类吗？",itemName:null==k?void 0:k.name,isLoading:S})]})}},88298:(e,s,a)=>{"use strict";a.d(s,{K:()=>j});var r=a(95155),l=a(12115),t=a(99840),n=a(97168),i=a(89852),c=a(99474),o=a(82714),d=a(81704),m=a(53580),x=a(34938),h=a(11790),u=a(19987);let g=[{name:"folder",label:"文件夹"},{name:"code",label:"代码"},{name:"pen-to-square",label:"写作"},{name:"bullhorn",label:"营销"},{name:"rocket",label:"效率"},{name:"graduation-cap",label:"学习"},{name:"lightbulb",label:"创意"},{name:"cog",label:"工具"},{name:"heart",label:"收藏"},{name:"star",label:"重要"},{name:"fire",label:"热门"},{name:"gem",label:"精选"},{name:"bullseye",label:"目标"},{name:"flag",label:"标记"},{name:"bookmark",label:"书签"},{name:"database",label:"数据"},{name:"cloud",label:"云端"},{name:"mobile",label:"移动"},{name:"desktop",label:"桌面"},{name:"palette",label:"设计"}],b=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9","#3b82f6","#6366f1","#8b5cf6","#a855f7","#d946ef","#ec4899"];function j(e){let{category:s,isOpen:a,onClose:j,onSuccess:p}=e,[f,v]=(0,l.useState)(""),[y,N]=(0,l.useState)(""),[w,k]=(0,l.useState)("#6366f1"),[C,S]=(0,l.useState)("folder"),[I,_]=(0,l.useState)(""),[E,J]=(0,l.useState)(!1),[F,$]=(0,l.useState)(""),{toast:D}=(0,m.dj)(),O=!!s;(0,l.useEffect)(()=>{s?(v(s.name),N(s.description||""),k(s.color),S(s.icon),_("")):q()},[s]);let q=()=>{v(""),N(""),k("#6366f1"),S("folder"),_(""),$("")},M=async e=>{if(!e.trim())return $("分类名称不能为空"),!1;if(e.length>50)return $("分类名称不能超过50个字符"),!1;try{if(await (0,x.c1)(e.trim(),O?null==s?void 0:s.id:void 0))return $("分类名称已存在"),!1}catch(e){console.error("检查分类名称失败:",e)}return $(""),!0},z=async e=>{if(e.preventDefault(),!await M(f))return;let a=I&&(0,u.o1)(I)?I:w;try{J(!0);let e={name:f.trim(),description:y.trim()||void 0,color:a,icon:C};if(O&&s)if(console.log("\uD83D\uDE80 使用本地优先更新分类"),await h.J.updateCategory(s.id,e))D({title:"更新成功",description:"分类已成功更新"});else throw Error("更新失败");else{console.log("\uD83D\uDE80 使用本地优先创建分类");let s=await h.J.createCategory(e);if(s)D({title:"创建成功",description:"分类已成功创建"}),console.log("✅ 分类创建成功:",s);else throw Error("创建失败")}p(),j(),q()}catch(e){console.error("保存分类失败:",e),D({title:"保存失败",description:O?"更新分类时出现错误":"创建分类时出现错误",variant:"destructive"})}finally{J(!1)}};return(0,r.jsx)(t.lG,{open:a,onOpenChange:j,children:(0,r.jsxs)(t.Cf,{className:"max-w-md",children:[(0,r.jsxs)(t.c7,{children:[(0,r.jsx)(t.L3,{children:O?"编辑分类":"创建新分类"}),(0,r.jsx)(t.rr,{children:O?"修改分类的信息和外观":"创建一个新的提示词分类"})]}),(0,r.jsxs)("form",{onSubmit:z,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"name",children:"分类名称 *"}),(0,r.jsx)(i.p,{id:"name",value:f,onChange:e=>{v(e.target.value),$("")},onBlur:()=>M(f),placeholder:"输入分类名称",className:F?"border-red-500":"",required:!0}),F&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:F})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"description",children:"描述"}),(0,r.jsx)(c.T,{id:"description",value:y,onChange:e=>N(e.target.value),placeholder:"输入分类描述（可选）",className:"min-h-[80px]"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{children:"图标"}),(0,r.jsx)("div",{className:"grid grid-cols-5 gap-2",children:g.map(e=>(0,r.jsx)("button",{type:"button",className:"\n                    flex items-center justify-center w-10 h-10 rounded-lg border-2 transition-colors\n                    ".concat(C===e.name?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50","\n                  "),onClick:()=>S(e.name),title:e.label,children:(0,r.jsx)(d.Icon,{name:e.name,className:"h-5 w-5"})},e.name))})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{children:"颜色"}),(0,r.jsx)("div",{className:"grid grid-cols-8 gap-2",children:b.map(e=>(0,r.jsx)("button",{type:"button",className:"\n                    w-8 h-8 rounded-lg border-2 transition-all\n                    ".concat(w===e?"border-gray-400 scale-110":"border-gray-200 hover:scale-105","\n                  "),style:{backgroundColor:e},onClick:()=>{k(e),_("")}},e))}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.p,{type:"text",value:I,onChange:e=>{var s;_(s=e.target.value),(0,u.o1)(s)&&k(s)},placeholder:"#6366f1",className:"flex-1"}),(0,r.jsx)("div",{className:"w-8 h-8 rounded border border-gray-200",style:{backgroundColor:w}})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{children:"预览"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 p-3 border rounded-lg bg-gray-50",children:[(0,r.jsx)(d.Icon,{name:C,className:"h-5 w-5",color:w}),(0,r.jsx)("span",{className:"font-medium",children:f||"分类名称"}),y&&(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:["- ",y]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-4",children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:j,children:"取消"}),(0,r.jsx)(n.$,{type:"submit",disabled:E||!!F,children:E?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),O?"更新中...":"创建中..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.Icon,{name:"save",className:"h-4 w-4 mr-2"}),O?"更新":"创建"]})})]})]})]})})}}},e=>{e.O(0,[266,352,865,874,576,561,949,505,44,938,647,441,964,358],()=>e(e.s=53771)),_N_E=e.O()}]);