(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[909],{4671:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var l=a(95155),t=a(92669),r=a(12115),c=a(88482),n=a(88145),d=a(81704),i=a(19987),x=a(34938);function m(){let[e,s]=(0,r.useState)(null),[a,t]=(0,r.useState)(!0);(0,r.useEffect)(()=>{m()},[]);let m=async()=>{try{t(!0);let[e,a,l,r,c,n]=await Promise.all([(0,x.oO)({limit:1e3}),(0,x.bW)(),(0,x.Q2)(),(0,x.tf)(5),(0,x.PH)(10),(0,x.R9)(5)]),d=e.data.reduce((e,s)=>e+(s.usage_count||0),0),i=e.data.sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime()).slice(0,5);s({totalPrompts:e.total,totalCategories:a.length,totalTags:l.length,totalUsage:d,popularPrompts:r,popularTags:c,popularSearches:n,recentPrompts:i})}catch(e){console.error("加载统计数据失败:",e)}finally{t(!1)}};return a?(0,l.jsx)("div",{className:"space-y-6",children:(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,l.jsxs)(c.Zp,{className:"animate-pulse",children:[(0,l.jsx)(c.aR,{className:"pb-2",children:(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),(0,l.jsx)(c.Wu,{children:(0,l.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})})]},s))})}):e?e?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,l.jsxs)(c.Zp,{children:[(0,l.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(c.ZB,{className:"text-sm font-medium",children:"总提示词"}),(0,l.jsx)(d.Icon,{name:"file-text",className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(c.Wu,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:(0,i.ZV)(e.totalPrompts)}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"已创建的提示词数量"})]})]}),(0,l.jsxs)(c.Zp,{children:[(0,l.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(c.ZB,{className:"text-sm font-medium",children:"总分类"}),(0,l.jsx)(d.Icon,{name:"folder",className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(c.Wu,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:(0,i.ZV)(e.totalCategories)}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"已创建的分类数量"})]})]}),(0,l.jsxs)(c.Zp,{children:[(0,l.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(c.ZB,{className:"text-sm font-medium",children:"总标签"}),(0,l.jsx)(d.Icon,{name:"tags",className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(c.Wu,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:(0,i.ZV)(e.totalTags)}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"已创建的标签数量"})]})]}),(0,l.jsxs)(c.Zp,{children:[(0,l.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(c.ZB,{className:"text-sm font-medium",children:"总使用次数"}),(0,l.jsx)(d.Icon,{name:"eye",className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(c.Wu,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:(0,i.ZV)(e.totalUsage)}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"累计复制使用次数"})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,l.jsxs)(c.Zp,{children:[(0,l.jsxs)(c.aR,{children:[(0,l.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(d.Icon,{name:"fire",className:"h-5 w-5 text-orange-500"}),"热门提示词"]}),(0,l.jsx)(c.BT,{children:"使用次数最多的提示词"})]}),(0,l.jsx)(c.Wu,{children:(0,l.jsx)("div",{className:"space-y-3",children:e.popularPrompts.length>0?e.popularPrompts.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 text-orange-600 text-xs font-medium",children:s+1}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsx)("p",{className:"font-medium truncate",children:e.title}),(0,l.jsxs)("p",{className:"text-sm text-muted-foreground",children:["使用 ",e.usage_count," 次"]})]}),e.category&&(0,l.jsx)(n.E,{variant:"secondary",style:{backgroundColor:"".concat(e.category.color,"20"),color:e.category.color},children:e.category.name})]},e.id)):(0,l.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"暂无使用数据"})})})]}),(0,l.jsxs)(c.Zp,{children:[(0,l.jsxs)(c.aR,{children:[(0,l.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(d.Icon,{name:"hashtag",className:"h-5 w-5 text-blue-500"}),"热门标签"]}),(0,l.jsx)(c.BT,{children:"使用最频繁的标签"})]}),(0,l.jsx)(c.Wu,{children:(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:e.popularTags.length>0?e.popularTags.map(e=>(0,l.jsxs)(n.E,{variant:"secondary",className:"text-sm",style:{backgroundColor:"".concat(e.color,"20"),color:e.color},children:[e.name,(0,l.jsx)("span",{className:"ml-1 text-xs opacity-75",children:e.usage_count})]},e.id)):(0,l.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4 w-full",children:"暂无标签数据"})})})]}),(0,l.jsxs)(c.Zp,{children:[(0,l.jsxs)(c.aR,{children:[(0,l.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(d.Icon,{name:"search",className:"h-5 w-5 text-green-500"}),"热门搜索"]}),(0,l.jsx)(c.BT,{children:"搜索次数最多的关键词"})]}),(0,l.jsx)(c.Wu,{children:(0,l.jsx)("div",{className:"space-y-2",children:e.popularSearches.length>0?e.popularSearches.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsxs)("span",{className:"text-sm font-medium text-green-600",children:["#",s+1]}),(0,l.jsx)("span",{className:"text-sm",children:e.search_term})]}),(0,l.jsxs)(n.E,{variant:"outline",children:[e.search_count," 次"]})]},e.id)):(0,l.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"暂无搜索数据"})})})]}),(0,l.jsxs)(c.Zp,{children:[(0,l.jsxs)(c.aR,{children:[(0,l.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(d.Icon,{name:"clock",className:"h-5 w-5 text-purple-500"}),"最近创建"]}),(0,l.jsx)(c.BT,{children:"最新创建的提示词"})]}),(0,l.jsx)(c.Wu,{children:(0,l.jsx)("div",{className:"space-y-3",children:e.recentPrompts.length>0?e.recentPrompts.map(e=>(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsx)("p",{className:"font-medium truncate",children:e.title}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:(0,i.fw)(e.created_at)})]}),e.category&&(0,l.jsx)(n.E,{variant:"secondary",style:{backgroundColor:"".concat(e.category.color,"20"),color:e.category.color},children:e.category.name})]},e.id)):(0,l.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"暂无提示词"})})})]})]})]}):null:(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)(d.Icon,{name:"exclamation-triangle",className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:"无法加载统计数据"})]})}var o=a(97168);function h(){return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,l.jsx)(t.a,{}),(0,l.jsxs)("div",{className:"max-w-7xl mx-auto p-6",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white",children:"数据统计"}),(0,l.jsx)("p",{className:"text-muted-foreground mt-2",children:"查看您的提示词使用情况和统计数据"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsxs)(o.$,{variant:"outline",size:"sm",onClick:()=>{window.location.reload()},className:"flex-1 sm:flex-none",children:[(0,l.jsx)(d.Icon,{name:"refresh",className:"h-4 w-4 sm:mr-2"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"刷新数据"})]}),(0,l.jsxs)(o.$,{variant:"outline",size:"sm",onClick:()=>{console.log("导出数据")},className:"flex-1 sm:flex-none",children:[(0,l.jsx)(d.Icon,{name:"download",className:"h-4 w-4 sm:mr-2"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"导出数据"})]})]})]}),(0,l.jsx)(m,{}),(0,l.jsx)("div",{className:"mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6",children:(0,l.jsxs)("div",{className:"flex items-start gap-3",children:[(0,l.jsx)(d.Icon,{name:"info-circle",className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-blue-900 dark:text-blue-100 mb-2",children:"统计说明"}),(0,l.jsxs)("div",{className:"text-sm text-blue-700 dark:text-blue-200 space-y-1",children:[(0,l.jsxs)("p",{children:["• ",(0,l.jsx)("strong",{children:"总使用次数"}),"：所有提示词的复制使用次数总和"]}),(0,l.jsxs)("p",{children:["• ",(0,l.jsx)("strong",{children:"热门提示词"}),"：按使用次数排序的前5个提示词"]}),(0,l.jsxs)("p",{children:["• ",(0,l.jsx)("strong",{children:"热门标签"}),"：按关联提示词数量排序的标签"]}),(0,l.jsxs)("p",{children:["• ",(0,l.jsx)("strong",{children:"热门搜索"}),"：搜索次数最多的关键词"]}),(0,l.jsxs)("p",{children:["• ",(0,l.jsx)("strong",{children:"最近创建"}),"：按创建时间排序的最新提示词"]})]})]})]})}),(0,l.jsxs)("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:(0,l.jsx)(d.Icon,{name:"lightbulb",className:"h-5 w-5 text-green-600"})}),(0,l.jsx)("h3",{className:"font-medium",children:"提升效率"})]}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"通过查看热门提示词，了解哪些内容最受欢迎，优化您的提示词库。"})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,l.jsx)(d.Icon,{name:"bullseye",className:"h-5 w-5 text-blue-600"})}),(0,l.jsx)("h3",{className:"font-medium",children:"精准分类"})]}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"根据使用统计调整分类结构，让常用的提示词更容易找到。"})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,l.jsx)(d.Icon,{name:"chart",className:"h-5 w-5 text-purple-600"})}),(0,l.jsx)("h3",{className:"font-medium",children:"数据驱动"})]}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"基于搜索和使用数据，持续优化您的提示词管理策略。"})]})]})]})]})}},66764:(e,s,a)=>{Promise.resolve().then(a.bind(a,4671))},88482:(e,s,a)=>{"use strict";a.d(s,{BT:()=>i,Wu:()=>x,ZB:()=>d,Zp:()=>c,aR:()=>n});var l=a(95155),t=a(12115),r=a(53999);let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,r.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...t})});c.displayName="Card";let n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",a),...t})});n.displayName="CardHeader";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,r.cn)("font-semibold leading-none tracking-tight",a),...t})});d.displayName="CardTitle";let i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,r.cn)("text-sm text-muted-foreground",a),...t})});i.displayName="CardDescription";let x=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,r.cn)("p-6 pt-0",a),...t})});x.displayName="CardContent",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,r.cn)("flex items-center p-6 pt-0",a),...t})}).displayName="CardFooter"}},e=>{e.O(0,[266,352,865,874,576,561,949,505,938,441,964,358],()=>e(e.s=66764)),_N_E=e.O()}]);