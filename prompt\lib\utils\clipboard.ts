/**
 * 剪贴板操作工具函数
 */

/**
 * 复制文本到剪贴板
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    }

    // 降级到传统方法
    return fallbackCopyToClipboard(text)
  } catch (error) {
    console.error('复制到剪贴板失败:', error)
    return fallbackCopyToClipboard(text)
  }
}

/**
 * 降级复制方法（兼容旧浏览器）
 */
function fallbackCopyToClipboard(text: string): boolean {
  try {
    // 创建临时文本区域
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    
    // 选择并复制
    textArea.focus()
    textArea.select()
    const successful = document.execCommand('copy')
    
    // 清理
    document.body.removeChild(textArea)
    
    return successful
  } catch (error) {
    console.error('降级复制方法失败:', error)
    return false
  }
}

/**
 * 检查是否支持剪贴板操作
 */
export function isClipboardSupported(): boolean {
  return !!(navigator.clipboard || document.execCommand)
}

/**
 * 从剪贴板读取文本
 */
export async function readFromClipboard(): Promise<string | null> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      const text = await navigator.clipboard.readText()
      return text
    }
    
    // 传统方法无法读取剪贴板内容
    console.warn('当前环境不支持读取剪贴板内容')
    return null
  } catch (error) {
    console.error('读取剪贴板失败:', error)
    return null
  }
}

/**
 * 复制文本并显示反馈
 */
export async function copyWithFeedback(
  text: string,
  onSuccess?: () => void,
  onError?: (error: string) => void
): Promise<boolean> {
  const success = await copyToClipboard(text)
  
  if (success) {
    onSuccess?.()
  } else {
    onError?.('复制失败，请手动复制')
  }
  
  return success
}

/**
 * 格式化文本用于复制
 */
export function formatTextForCopy(text: string): string {
  // 移除多余的空白字符
  return text
    .replace(/\r\n/g, '\n') // 统一换行符
    .replace(/\t/g, '  ') // 制表符转换为空格
    .trim() // 移除首尾空白
}

/**
 * 复制 JSON 数据
 */
export async function copyJSON(data: any, pretty: boolean = true): Promise<boolean> {
  try {
    const jsonString = pretty 
      ? JSON.stringify(data, null, 2)
      : JSON.stringify(data)
    
    return await copyToClipboard(jsonString)
  } catch (error) {
    console.error('复制JSON失败:', error)
    return false
  }
}

/**
 * 复制 Markdown 格式文本
 */
export async function copyAsMarkdown(title: string, content: string): Promise<boolean> {
  const markdown = `# ${title}\n\n${content}`
  return await copyToClipboard(markdown)
}

/**
 * 复制为代码块格式
 */
export async function copyAsCodeBlock(content: string, language: string = ''): Promise<boolean> {
  const codeBlock = `\`\`\`${language}\n${content}\n\`\`\``
  return await copyToClipboard(codeBlock)
}
