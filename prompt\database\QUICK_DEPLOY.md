# 数据库快速部署指南

## 🚨 修复说明

原始的 `schema.sql` 包含中文全文搜索配置，在 Supabase 中会报错：
```
ERROR: 42704: text search configuration "chinese" does not exist
```

**已修复**：现在使用 `'english'` 配置，确保在所有 PostgreSQL 环境中都能正常运行。

## 🚀 快速部署步骤

### 1. 创建 Supabase 项目
1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 点击 "New Project"
3. 填写项目信息并等待创建完成

### 2. 执行数据库脚本

#### 步骤 1: 创建表结构
1. 进入 Supabase 项目的 SQL Editor
2. 复制 `schema.sql` 的全部内容
3. 粘贴到 SQL Editor 并执行
4. 确认所有表和索引创建成功

#### 步骤 2: 创建初始数据函数
1. 在 SQL Editor 中执行 `seed.sql`
2. 这会创建 `create_default_categories_for_user()` 函数

### 3. 验证部署

执行以下 SQL 验证表是否创建成功：

```sql
-- 检查所有表
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE';

-- 检查 RLS 是否启用
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- 检查索引
SELECT indexname, tablename 
FROM pg_indexes 
WHERE schemaname = 'public';
```

### 4. 配置认证

1. 进入 **Authentication > Settings**
2. 配置 Site URL: `http://localhost:3000` (开发环境)
3. 添加 Redirect URLs (生产环境域名)

### 5. 获取连接信息

1. 进入 **Settings > API**
2. 复制以下信息：
   - Project URL: `https://xxx.supabase.co`
   - Anon/Public Key: `eyJhbGciOiJIUzI1NiIs...`

## 📋 表结构概览

创建成功后，您将拥有以下表：

| 表名 | 描述 | 主要字段 |
|------|------|----------|
| `categories` | 分类表 | id, name, color, icon, sort_order |
| `tags` | 标签表 | id, name, color |
| `prompts` | 提示词表 | id, title, content, category_id |
| `prompt_tags` | 提示词标签关联 | prompt_id, tag_id |
| `search_history` | 搜索历史 | id, query, filters |
| `user_preferences` | 用户偏好 | id, theme, language |

## 🔒 安全特性

- ✅ 行级安全 (RLS) 已启用
- ✅ 用户数据隔离
- ✅ 外键约束
- ✅ 软删除支持

## 🔧 常用函数

部署后可用的函数：

### 1. 为新用户创建默认数据
```sql
SELECT create_default_categories_for_user();
```

### 2. 增加提示词使用次数
```sql
SELECT increment_prompt_usage('prompt-uuid-here');
```

### 3. 获取用户统计
```sql
SELECT get_user_stats();
```

### 4. 搜索提示词
```sql
SELECT * FROM search_prompts(
    search_query := '搜索关键词',
    limit_count := 20,
    offset_count := 0
);
```

## 🐛 常见问题

### Q: 执行 schema.sql 时报错 "chinese" 配置不存在
**A**: 使用修复版的 schema.sql，已改为使用 'english' 配置。

### Q: RLS 策略不生效
**A**: 确保在应用中使用正确的 Supabase 客户端配置，并且用户已认证。

### Q: 全文搜索不支持中文
**A**: 虽然使用 'english' 配置，但仍支持中文搜索，只是分词效果可能不如专门的中文配置。

### Q: 如何重置数据库
**A**: 在 Supabase Dashboard 的 Settings > Database 中可以重置数据库。

## 📱 应用集成

在应用中使用时，确保：

1. **环境变量配置**:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

2. **首次登录时创建默认数据**:
```typescript
// 在用户首次登录后调用
await supabase.rpc('create_default_categories_for_user')
```

3. **搜索功能**:
```typescript
// 使用全文搜索
const { data } = await supabase
  .from('prompts')
  .select('*')
  .textSearch('title', 'search query')
```

## ✅ 部署检查清单

- [ ] Supabase 项目创建成功
- [ ] schema.sql 执行无错误
- [ ] seed.sql 执行成功
- [ ] 所有表创建完成
- [ ] RLS 策略启用
- [ ] 索引创建成功
- [ ] 认证配置完成
- [ ] API 密钥获取
- [ ] 应用环境变量配置

## 🎉 完成

数据库部署完成！现在可以启动应用并开始使用提示词管理工具了。

如有问题，请参考：
- [完整部署指南](../DEPLOYMENT.md)
- [开发文档](../DEVELOPMENT.md)
- [API 文档](../API.md)
