# 📚 项目文档目录

本目录包含提示词管理工具项目的完整文档。

## 📁 文档结构

```
docs/
├── README.md                    # 本文件 - 文档导航
├── user/                        # 用户文档
│   ├── getting-started.md       # 快速开始指南
│   ├── user-guide.md           # 用户使用手册
│   └── features.md             # 功能特性说明
├── technical/                   # 技术文档
│   ├── architecture.md         # 系统架构设计
│   ├── api-reference.md        # API 接口文档
│   ├── database-design.md      # 数据库设计文档
│   └── performance.md          # 性能优化文档
├── development/                 # 开发文档
│   ├── setup.md               # 开发环境配置
│   ├── coding-standards.md    # 代码规范
│   ├── testing.md             # 测试指南
│   └── contributing.md        # 贡献指南
└── deployment/                  # 部署文档
    ├── vercel-deployment.md   # Vercel 部署指南
    ├── supabase-setup.md      # Supabase 配置指南
    └── environment-config.md  # 环境变量配置
```

## 🎯 快速导航

### 👥 用户相关
- [快速开始](./user/getting-started.md) - 新用户入门指南
- [用户手册](./user/user-guide.md) - 详细使用说明
- [功能特性](./user/features.md) - 完整功能介绍

### 🔧 技术相关
- [系统架构](./technical/architecture.md) - 技术架构设计
- [API 文档](./technical/api-reference.md) - 接口说明
- [数据库设计](./technical/database-design.md) - 数据模型
- [性能优化](./technical/performance.md) - 性能相关

### 💻 开发相关
- [开发环境](./development/setup.md) - 环境配置
- [代码规范](./development/coding-standards.md) - 编码标准
- [测试指南](./development/testing.md) - 测试方法
- [贡献指南](./development/contributing.md) - 参与开发

### 🚀 部署相关
- [Vercel 部署](./deployment/vercel-deployment.md) - 生产部署
- [Supabase 配置](./deployment/supabase-setup.md) - 数据库配置
- [环境变量](./deployment/environment-config.md) - 配置说明

## 📝 文档维护

### 更新原则
- 保持文档与代码同步
- 及时更新功能变更
- 确保示例代码可运行
- 定期检查链接有效性

### 贡献方式
1. 发现文档问题请提交 Issue
2. 改进建议通过 Pull Request
3. 新功能需同步更新文档
4. 遵循文档写作规范

## 🔗 相关链接

- [项目主页](../README.md)
- [更新日志](../CHANGELOG.md)
- [问题反馈](https://github.com/your-repo/issues)
- [讨论区](https://github.com/your-repo/discussions)
