"use client"

import { useState } from 'react'
import { MarkdownPreview } from '@/components/ui/markdown-preview'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@/components/ui/icon'
import { useToast } from '@/hooks/use-toast'
import { copyToClipboard } from '@/lib/utils/clipboard'
import { formatDate, formatRelativeTime } from '@/lib/utils/format'
import type { PromptWithDetails } from '@/types/database'

interface PromptDetailModalProps {
  prompt: PromptWithDetails | null
  isOpen: boolean
  onClose: () => void
  onEdit: (promptId: string) => void
  onDelete: (promptId: string) => void
  onCopy: (content: string, promptId: string) => void
}

export function PromptDetailModal({
  prompt,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onCopy
}: PromptDetailModalProps) {
  const [isMarkdownView, setIsMarkdownView] = useState(true)
  const { toast } = useToast()

  if (!prompt) return null

  const handleCopy = async () => {
    const success = await copyToClipboard(prompt.content)
    if (success) {
      onCopy(prompt.content, prompt.id)
      toast({
        title: "复制成功",
        description: "提示词已复制到剪贴板",
      })
    } else {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板，请手动复制",
        variant: "destructive",
      })
    }
  }

  const handleEdit = () => {
    onEdit(prompt.id)
    onClose()
  }

  const handleDelete = () => {
    onDelete(prompt.id)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col">
        <DialogHeader>
          <div className="flex items-start justify-between pr-8">
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-xl font-semibold mb-2">
                {prompt.title}
              </DialogTitle>
              {prompt.description && (
                <DialogDescription className="text-base">
                  {prompt.description}
                </DialogDescription>
              )}
            </div>

            {/* 操作按钮 - 添加右边距避免与关闭按钮重叠 */}
            <div className="flex items-center gap-2 ml-4 flex-wrap">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
                className="flex-shrink-0"
              >
                <Icon name="copy" className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">复制</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                className="flex-shrink-0"
              >
                <Icon name="edit" className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">编辑</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                className="text-red-600 hover:text-red-700 flex-shrink-0"
              >
                <Icon name="trash" className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">删除</span>
              </Button>
            </div>
          </div>

          {/* 元信息 */}
          <div className="flex flex-wrap items-center gap-4 pt-4 border-t">
            {/* 分类 */}
            {prompt.category && (
              <div className="flex items-center gap-2">
                <Icon name="folder" className="h-4 w-4 text-muted-foreground" />
                <Badge
                  variant="secondary"
                  style={{ 
                    backgroundColor: `${prompt.category.color}20`,
                    color: prompt.category.color 
                  }}
                >
                  <Icon name={prompt.category.icon as any} className="h-3 w-3 mr-1" />
                  {prompt.category.name}
                </Badge>
              </div>
            )}

            {/* 标签 */}
            {prompt.tags && prompt.tags.length > 0 && (
              <div className="flex items-center gap-2">
                <Icon name="tags" className="h-4 w-4 text-muted-foreground" />
                <div className="flex flex-wrap gap-1">
                  {prompt.tags.map((tag) => (
                    <Badge
                      key={tag.id}
                      variant="outline"
                      className="text-xs"
                      style={{ 
                        backgroundColor: `${tag.color}20`,
                        color: tag.color,
                        borderColor: `${tag.color}40`
                      }}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* 使用次数 */}
            <div className="flex items-center gap-2">
              <Icon name="eye" className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                使用 {prompt.usage_count} 次
              </span>
            </div>

            {/* 时间信息 */}
            <div className="flex items-center gap-2">
              <Icon name="clock" className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                创建于 {formatDate(prompt.created_at)}
              </span>
            </div>

            {prompt.updated_at !== prompt.created_at && (
              <div className="flex items-center gap-2">
                <Icon name="refresh" className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  更新于 {formatRelativeTime(prompt.updated_at)}
                </span>
              </div>
            )}
          </div>
        </DialogHeader>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* 视图切换 */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Button
                variant={!isMarkdownView ? "default" : "outline"}
                size="sm"
                onClick={() => setIsMarkdownView(false)}
                className="relative"
              >
                <Icon name="file-text" className="h-4 w-4 mr-2" />
                原始文本
              </Button>
              <Button
                variant={isMarkdownView ? "default" : "outline"}
                size="sm"
                onClick={() => setIsMarkdownView(true)}
                className="relative"
              >
                <Icon name="eye" className="h-4 w-4 mr-2" />
                Markdown 预览
                {isMarkdownView && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                )}
              </Button>

              {/* Markdown 功能提示 */}
              <div className="flex items-center gap-1 text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950 px-2 py-1 rounded-md">
                <Icon name="circle-info" className="h-3 w-3" />
                <span>支持 Markdown 格式</span>
              </div>
            </div>

            <div className="text-sm text-muted-foreground">
              {prompt.content.length} 字符
            </div>
          </div>

          {/* 内容显示 */}
          <div className="flex-1 overflow-y-auto border rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
            {isMarkdownView ? (
              <div className="relative">
                {/* Markdown 渲染指示器 */}
                <div className="absolute top-0 right-0 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md">
                  <Icon name="eye" className="h-3 w-3 inline mr-1" />
                  Markdown 渲染
                </div>
                <MarkdownPreview
                  content={prompt.content}
                  className="pt-6"
                />
              </div>
            ) : (
              <div className="relative">
                {/* 原始文本指示器 */}
                <div className="absolute top-0 right-0 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md">
                  <Icon name="file-text" className="h-3 w-3 inline mr-1" />
                  原始文本
                </div>
                <pre className="whitespace-pre-wrap text-sm font-mono pt-6">
                  {prompt.content}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // TODO: 实现导出功能
                console.log('导出提示词')
              }}
            >
              <Icon name="download" className="h-4 w-4 mr-2" />
              导出
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // TODO: 实现分享功能
                console.log('分享提示词')
              }}
            >
              <Icon name="share" className="h-4 w-4 mr-2" />
              分享
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onClose}>
              关闭
            </Button>
            <Button onClick={handleCopy}>
              <Icon name="copy" className="h-4 w-4 mr-2" />
              复制内容
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
