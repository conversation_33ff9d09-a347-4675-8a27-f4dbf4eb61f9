"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@/components/ui/icon'
import { useToast } from '@/hooks/use-toast'
import { MarkdownPreview } from '@/components/ui/markdown-preview'
import { cn } from '@/lib/utils'

interface Tag {
  id: string
  name: string
  color: string
}

interface Category {
  id: string
  name: string
  color: string
  icon: string
}

interface PromptCardProps {
  id: string
  title: string
  description?: string
  content: string
  category?: Category
  isLocal?: boolean
  tags?: Tag[]
  usageCount: number
  createdAt: string
  updatedAt: string
  onView: (id: string) => void
  onEdit: (id: string) => void
  onDelete: (id: string) => void
  onCopy: (content: string, id: string) => void
  className?: string
}

export function PromptCard({
  id,
  title,
  description,
  content,
  category,
  tags = [],
  usageCount,
  createdAt,
  updatedAt,
  onView,
  onEdit,
  onDelete,
  onCopy,
  isLocal = false,
  className
}: PromptCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { toast } = useToast()

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content)
      onCopy(content, id)
      toast({
        title: "复制成功",
        description: "提示词已复制到剪贴板",
      })
    } catch {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板，请手动复制",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Card 
      className={cn(
        "group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02] cursor-pointer",
        "border-l-4",
        category?.color ? `border-l-[${category.color}]` : "border-l-blue-500",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => onView(id)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-semibold mb-1 overflow-hidden">
              <div className="flex items-center gap-2">
                <div className="line-clamp-2 flex-1">
                  {title}
                </div>
                {isLocal && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shrink-0">
                    <Icon name="smartphone" className="h-3 w-3 mr-1" />
                    本地
                  </span>
                )}
              </div>
            </CardTitle>
            {description && (
              <CardDescription className="text-sm text-muted-foreground overflow-hidden">
                <div className="line-clamp-2">
                  {description}
                </div>
              </CardDescription>
            )}
          </div>
          
          {/* 操作按钮 */}
          <div className={cn(
            "flex items-center gap-1 transition-opacity duration-200",
            isHovered ? "opacity-100" : "opacity-0 md:opacity-0",
            "sm:opacity-100" // 在小屏幕上始终显示
          )}>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 hover:bg-blue-100 hover:text-blue-600"
              onClick={(e) => {
                e.stopPropagation()
                handleCopy()
              }}
            >
              <Icon name="copy" className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 hover:bg-green-100 hover:text-green-600"
              onClick={(e) => {
                e.stopPropagation()
                onEdit(id)
              }}
            >
              <Icon name="edit" className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 hover:bg-red-100 hover:text-red-600"
              onClick={(e) => {
                e.stopPropagation()
                onDelete(id)
              }}
            >
              <Icon name="trash" className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 分类信息 - 上半部分 */}
        {category && (
          <div className="flex items-center gap-2 mt-1.5">
            <div
              className="flex items-center gap-1 px-2 py-1 rounded text-xs font-medium"
              style={{
                backgroundColor: `${category.color}12`,
                color: category.color,
                border: `1px solid ${category.color}25`
              }}
            >
              <Icon name={category.icon as any} className="h-3 w-3" />
              {category.name}
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        {/* 内容预览 */}
        <div className="relative text-sm text-muted-foreground mb-4 overflow-hidden">
          {/* Markdown 指示器 */}
          <div className="absolute top-0 right-0 z-10 bg-blue-50 dark:bg-blue-950 text-blue-600 dark:text-blue-400 text-xs px-1.5 py-0.5 rounded-sm opacity-70">
            <Icon name="eye" className="h-2.5 w-2.5 inline" />
          </div>

          <div className="line-clamp-3 pr-8">
            <MarkdownPreview
              content={content}
              truncate={true}
              maxLines={3}
              className="text-sm text-muted-foreground prose-p:inline prose-headings:inline prose-strong:font-medium prose-em:italic"
            />
          </div>
        </div>

        {/* 底部信息 */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Icon name="eye" className="h-3 w-3" />
              <span>{usageCount} 次使用</span>
            </div>
            <div className="flex items-center gap-1">
              <Icon name="clock" className="h-3 w-3" />
              <span>{formatDate(updatedAt)}</span>
            </div>
          </div>

          {/* 快速复制按钮 */}
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs hover:bg-blue-100 hover:text-blue-600"
            onClick={(e) => {
              e.stopPropagation()
              handleCopy()
            }}
          >
            <Icon name="copy" className="h-3 w-3 mr-1" />
            复制
          </Button>
        </div>

        {/* 标签 - 最底部 */}
        {tags.length > 0 && (
          <div className="mt-2 pt-2 border-t border-gray-100">
            <div className="flex flex-wrap gap-1">
              {tags.slice(0, 5).map((tag) => (
                <Badge
                  key={tag.id}
                  variant="outline"
                  className="text-xs px-1.5 py-0.5 border-dashed h-5"
                  style={{
                    backgroundColor: `${tag.color}06`,
                    color: tag.color,
                    borderColor: `${tag.color}30`
                  }}
                >
                  {tag.name}
                </Badge>
              ))}
              {tags.length > 5 && (
                <Badge variant="outline" className="text-xs px-1.5 py-0.5 border-dashed text-muted-foreground h-5">
                  +{tags.length - 5}
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>

      {/* 悬停时的边框效果 */}
      <div className={cn(
        "absolute inset-0 border-2 border-transparent transition-colors duration-300 rounded-lg pointer-events-none",
        isHovered && "border-blue-200"
      )} />
    </Card>
  )
}
