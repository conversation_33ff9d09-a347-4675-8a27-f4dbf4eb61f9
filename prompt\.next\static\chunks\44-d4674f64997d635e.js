"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[44],{15452:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>el,ZL:()=>ee,bL:()=>Q,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>$});var r=n(12115),l=n(85185),i=n(6101),o=n(46081),a=n(61285),u=n(5845),s=n(19178),c=n(25519),d=n(34378),f=n(28905),h=n(63655),g=n(92293),p=n(93795),v=n(38168),b=n(99708),m=n(95155),y="Dialog",[w,x]=(0,o.A)(y),[D,E]=w(y),C=e=>{let{__scopeDialog:t,children:n,open:l,defaultOpen:i,onOpenChange:o,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,h]=(0,u.i)({prop:l,defaultProp:null!=i&&i,onChange:o,caller:y});return(0,m.jsx)(D,{scope:t,triggerRef:c,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:h,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),modal:s,children:n})};C.displayName=y;var R="DialogTrigger",M=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(R,n),a=(0,i.s)(t,o.triggerRef);return(0,m.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":X(o.open),...r,ref:a,onClick:(0,l.m)(e.onClick,o.onOpenToggle)})});M.displayName=R;var S="DialogPortal",[k,T]=w(S,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:n,children:l,container:i}=e,o=E(S,t);return(0,m.jsx)(k,{scope:t,forceMount:n,children:r.Children.map(l,e=>(0,m.jsx)(f.C,{present:n||o.open,children:(0,m.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};O.displayName=S;var N="DialogOverlay",L=r.forwardRef((e,t)=>{let n=T(N,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,i=E(N,e.__scopeDialog);return i.modal?(0,m.jsx)(f.C,{present:r||i.open,children:(0,m.jsx)(A,{...l,ref:t})}):null});L.displayName=N;var I=(0,b.TL)("DialogOverlay.RemoveScroll"),A=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(N,n);return(0,m.jsx)(p.A,{as:I,allowPinchZoom:!0,shards:[l.contentRef],children:(0,m.jsx)(h.sG.div,{"data-state":X(l.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),j="DialogContent",z=r.forwardRef((e,t)=>{let n=T(j,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,i=E(j,e.__scopeDialog);return(0,m.jsx)(f.C,{present:r||i.open,children:i.modal?(0,m.jsx)(P,{...l,ref:t}):(0,m.jsx)(F,{...l,ref:t})})});z.displayName=j;var P=r.forwardRef((e,t)=>{let n=E(j,e.__scopeDialog),o=r.useRef(null),a=(0,i.s)(t,n.contentRef,o);return r.useEffect(()=>{let e=o.current;if(e)return(0,v.Eq)(e)},[]),(0,m.jsx)(B,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=E(j,e.__scopeDialog),l=r.useRef(!1),i=r.useRef(!1);return(0,m.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,o;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(l.current||null==(o=n.triggerRef.current)||o.focus(),t.preventDefault()),l.current=!1,i.current=!1},onInteractOutside:t=>{var r,o;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(l.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;(null==(o=n.triggerRef.current)?void 0:o.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:l,onOpenAutoFocus:o,onCloseAutoFocus:a,...u}=e,d=E(j,n),f=r.useRef(null),h=(0,i.s)(t,f);return(0,g.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:l,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,m.jsx)(s.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":X(d.open),...u,ref:h,onDismiss:()=>d.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(Z,{titleId:d.titleId}),(0,m.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",Y=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(W,n);return(0,m.jsx)(h.sG.h2,{id:l.titleId,...r,ref:t})});Y.displayName=W;var _="DialogDescription",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(_,n);return(0,m.jsx)(h.sG.p,{id:l.descriptionId,...r,ref:t})});K.displayName=_;var G="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(G,n);return(0,m.jsx)(h.sG.button,{type:"button",...r,ref:t,onClick:(0,l.m)(e.onClick,()=>i.onOpenChange(!1))})});function X(e){return e?"open":"closed"}U.displayName=G;var q="DialogTitleWarning",[V,H]=(0,o.q)(q,{contentName:j,titleName:W,docsSlug:"dialog"}),Z=e=>{let{titleId:t}=e,n=H(q),l="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(l))},[l,t]),null},J=e=>{let{contentRef:t,descriptionId:n}=e,l=H("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(l.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},Q=C,$=M,ee=O,et=L,en=z,er=Y,el=K,ei=U},21206:(e,t,n)=>{n.d(t,{FN:()=>r}),n(78266);let r=e=>{let{transform:t}=e;return{...t,x:0}}},40968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(12115),l=n(63655),i=n(95155),o=r.forwardRef((e,t)=>(0,i.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var a=o},50402:(e,t,n)=>{n.d(t,{JR:()=>E,_G:()=>c,be:()=>o,gB:()=>h,gl:()=>w});var r=n(12115),l=n(75143),i=n(78266);function o(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function a(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=o(t,r,n),a=t[l],u=i[l];return u&&a?{x:u.left-a.left,y:u.top-a.top,scaleX:u.width/a.width,scaleY:u.height/a.height}:null},s={scaleX:1,scaleY:1},c=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:l,rects:i,overIndex:o}=e,a=null!=(t=i[n])?t:r;if(!a)return null;if(l===n){let e=i[o];return e?{x:0,y:n<o?e.top+e.height-(a.top+a.height):e.top-a.top,...s}:null}let u=function(e,t,n){let r=e[t],l=e[t-1],i=e[t+1];return r?n<t?l?r.top-(l.top+l.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):l?r.top-(l.top+l.height):0:0}(i,l,n);return l>n&&l<=o?{x:0,y:-a.height-u,...s}:l<n&&l>=o?{x:0,y:a.height+u,...s}:{x:0,y:0,...s}},d="Sortable",f=r.createContext({activeIndex:-1,containerId:d,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function h(e){let{children:t,id:n,items:o,strategy:a=u,disabled:s=!1}=e,{active:c,dragOverlay:h,droppableRects:g,over:p,measureDroppableContainers:v}=(0,l.fF)(),b=(0,i.YG)(d,n),m=null!==h.rect,y=(0,r.useMemo)(()=>o.map(e=>"object"==typeof e&&"id"in e?e.id:e),[o]),w=null!=c,x=c?y.indexOf(c.id):-1,D=p?y.indexOf(p.id):-1,E=(0,r.useRef)(y),C=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(y,E.current),R=-1!==D&&-1===x||C,M="boolean"==typeof s?{draggable:s,droppable:s}:s;(0,i.Es)(()=>{C&&w&&v(y)},[C,y,w,v]),(0,r.useEffect)(()=>{E.current=y},[y]);let S=(0,r.useMemo)(()=>({activeIndex:x,containerId:b,disabled:M,disableTransforms:R,items:y,overIndex:D,useDragOverlay:m,sortedRects:y.reduce((e,t,n)=>{let r=g.get(t);return r&&(e[n]=r),e},Array(y.length)),strategy:a}),[x,b,M.draggable,M.droppable,R,y,D,g,m,a]);return r.createElement(f.Provider,{value:S},t)}let g=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return o(n,r,l).indexOf(t)},p=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:o,previousItems:a,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(a===i||l!==o)&&(!!n||o!==l&&t===u)},v={duration:200,easing:"ease"},b="transform",m=i.Ks.Transition.toString({property:b,duration:0,easing:"linear"}),y={roleDescription:"sortable"};function w(e){var t,n,o,u;let{animateLayoutChanges:s=p,attributes:c,disabled:d,data:h,getNewIndex:w=g,id:x,strategy:D,resizeObserverConfig:E,transition:C=v}=e,{items:R,containerId:M,activeIndex:S,disabled:k,disableTransforms:T,sortedRects:O,overIndex:N,useDragOverlay:L,strategy:I}=(0,r.useContext)(f),A=(t=d,n=k,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(o=null==t?void 0:t.draggable)?o:n.draggable,droppable:null!=(u=null==t?void 0:t.droppable)?u:n.droppable}),j=R.indexOf(x),z=(0,r.useMemo)(()=>({sortable:{containerId:M,index:j,items:R},...h}),[M,h,j,R]),P=(0,r.useMemo)(()=>R.slice(R.indexOf(x)),[R,x]),{rect:F,node:B,isOver:W,setNodeRef:Y}=(0,l.zM)({id:x,data:z,disabled:A.droppable,resizeObserverConfig:{updateMeasurementsFor:P,...E}}),{active:_,activatorEvent:K,activeNodeRect:G,attributes:U,setNodeRef:X,listeners:q,isDragging:V,over:H,setActivatorNodeRef:Z,transform:J}=(0,l.PM)({id:x,data:z,attributes:{...y,...c},disabled:A.draggable}),Q=(0,i.jn)(Y,X),$=!!_,ee=$&&!T&&a(S)&&a(N),et=!L&&V,en=et&&ee?J:null,er=ee?null!=en?en:(null!=D?D:I)({rects:O,activeNodeRect:G,activeIndex:S,overIndex:N,index:j}):null,el=a(S)&&a(N)?w({id:x,items:R,activeIndex:S,overIndex:N}):j,ei=null==_?void 0:_.id,eo=(0,r.useRef)({activeId:ei,items:R,newIndex:el,containerId:M}),ea=R!==eo.current.items,eu=s({active:_,containerId:M,isDragging:V,isSorting:$,id:x,index:j,items:R,newIndex:eo.current.newIndex,previousItems:eo.current.items,previousContainerId:eo.current.containerId,transition:C,wasDragging:null!=eo.current.activeId}),es=function(e){let{disabled:t,index:n,node:o,rect:a}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.Es)(()=>{if(!t&&n!==c.current&&o.current){let e=a.current;if(e){let t=(0,l.Sj)(o.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,o,a]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eu,index:j,node:B,rect:F});return(0,r.useEffect)(()=>{$&&eo.current.newIndex!==el&&(eo.current.newIndex=el),M!==eo.current.containerId&&(eo.current.containerId=M),R!==eo.current.items&&(eo.current.items=R)},[$,el,M,R]),(0,r.useEffect)(()=>{if(ei===eo.current.activeId)return;if(null!=ei&&null==eo.current.activeId){eo.current.activeId=ei;return}let e=setTimeout(()=>{eo.current.activeId=ei},50);return()=>clearTimeout(e)},[ei]),{active:_,activeIndex:S,attributes:U,data:z,rect:F,index:j,newIndex:el,items:R,isOver:W,isSorting:$,isDragging:V,listeners:q,node:B,overIndex:N,over:H,setNodeRef:Q,setActivatorNodeRef:Z,setDroppableNodeRef:Y,setDraggableNodeRef:X,transform:null!=es?es:er,transition:es||ea&&eo.current.newIndex===j?m:(!et||(0,i.kx)(K))&&C&&($||eu)?i.Ks.Transition.toString({...C,property:b}):void 0}}function x(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let D=[l.vL.Down,l.vL.Right,l.vL.Up,l.vL.Left],E=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:o,droppableContainers:a,over:u,scrollableAncestors:s}}=t;if(D.includes(e.code)){if(e.preventDefault(),!n||!r)return;let t=[];a.getEnabled().forEach(n=>{if(!n||null!=n&&n.disabled)return;let i=o.get(n.id);if(i)switch(e.code){case l.vL.Down:r.top<i.top&&t.push(n);break;case l.vL.Up:r.top>i.top&&t.push(n);break;case l.vL.Left:r.left>i.left&&t.push(n);break;case l.vL.Right:r.left<i.left&&t.push(n)}});let c=(0,l.y$)({active:n,collisionRect:r,droppableRects:o,droppableContainers:t,pointerCoordinates:null}),d=(0,l.Vy)(c,"id");if(d===(null==u?void 0:u.id)&&c.length>1&&(d=c[1].id),null!=d){let e=a.get(n.id),t=a.get(d),u=t?o.get(t.id):null,c=null==t?void 0:t.node.current;if(c&&u&&e&&t){let n=(0,l.sl)(c).some((e,t)=>s[t]!==e),o=C(e,t),a=function(e,t){return!!x(e)&&!!x(t)&&!!C(e,t)&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),d=n||!o?{x:0,y:0}:{x:a?r.width-u.width:0,y:a?r.height-u.height:0},f={x:u.left,y:u.top};return d.x&&d.y?f:(0,i.Re)(f,d)}}}};function C(e,t){return!!x(e)&&!!x(t)&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},75143:(e,t,n)=>{n.d(t,{Mp:()=>eP,vL:()=>o,uN:()=>ei,AN:()=>ec,fp:()=>I,y$:()=>A,Sj:()=>B,Vy:()=>N,sl:()=>Y,fF:()=>eY,PM:()=>eW,zM:()=>eK,MS:()=>C,FR:()=>R});var r,l,i,o,a,u,s,c,d,f,h=n(12115),g=n(47650),p=n(78266);let v={display:"none"};function b(e){let{id:t,value:n}=e;return h.createElement("div",{id:t,style:v},n)}function m(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return h.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let y=(0,h.createContext)(null),w={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},x={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function D(e){let{announcements:t=x,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=w}=e,{announce:i,announcement:o}=function(){let[e,t]=(0,h.useState)("");return{announce:(0,h.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),a=(0,p.YG)("DndLiveRegion"),[u,s]=(0,h.useState)(!1);(0,h.useEffect)(()=>{s(!0)},[]);var c=(0,h.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let d=(0,h.useContext)(y);if((0,h.useEffect)(()=>{if(!d)throw Error("useDndMonitor must be used within a children of <DndContext>");return d(c)},[c,d]),!u)return null;let f=h.createElement(h.Fragment,null,h.createElement(b,{id:r,value:l.draggable}),h.createElement(m,{id:a,announcement:o}));return n?(0,g.createPortal)(f,n):f}function E(){}function C(e,t){return(0,h.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function R(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,h.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(r||(r={}));let M=Object.freeze({x:0,y:0});function S(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function k(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function T(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function O(e){let{left:t,top:n,height:r,width:l}=e;return[{x:t,y:n},{x:t+l,y:n},{x:t,y:n+r},{x:t+l,y:n+r}]}function N(e,t){if(!e||0===e.length)return null;let[n]=e;return t?n[t]:n}function L(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let I=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=L(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=S(L(r),l);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(k)},A=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=O(t),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=O(r),o=Number((l.reduce((e,t,r)=>e+S(n[r],t),0)/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:o}})}}return i.sort(k)},j=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let o=t.width*t.height,a=e.width*e.height,u=(l-r)*(i-n);return Number((u/(o+a-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(T)};function z(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:M}let P=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1),F={ignoreTransform:!1};function B(e,t){void 0===t&&(t=F);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,p.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;let{scaleX:l,scaleY:i,x:o,y:a}=r,u=e.left-o-(1-l)*parseFloat(n),s=e.top-a-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:o,bottom:a,right:u}=n;return{top:r,left:l,width:i,height:o,bottom:a,right:u}}function W(e){return B(e,{ignoreTransform:!0})}function Y(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,p.wz)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,p.sb)(l)||(0,p.xZ)(l)||n.includes(l))return n;let o=(0,p.zk)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,p.zk)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,o)&&n.push(l),void 0===(i=o)&&(i=(0,p.zk)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function _(e){let[t]=Y(e,1);return null!=t?t:null}function K(e){return p.Sw&&e?(0,p.l6)(e)?e:(0,p.Ll)(e)?(0,p.wz)(e)||e===(0,p.TW)(e).scrollingElement?window:(0,p.sb)(e)?e:null:null:null}function G(e){return(0,p.l6)(e)?e.scrollX:e.scrollLeft}function U(e){return(0,p.l6)(e)?e.scrollY:e.scrollTop}function X(e){return{x:G(e),y:U(e)}}function q(e){return!!p.Sw&&!!e&&e===document.scrollingElement}function V(e){let t={x:0,y:0},n=q(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(l||(l={}));let H={x:.2,y:.2};function Z(e){return e.reduce((e,t)=>(0,p.WQ)(e,X(t)),M)}let J=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+G(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+U(t),0)}]];class Q{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=Y(t),r=Z(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,J))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),o=r[t]-l;return this.rect[e]+o},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ${constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function ee(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function et(e){e.preventDefault()}function en(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(i||(i={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let er={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},el=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class ei{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new $((0,p.TW)(t)),this.windowListeners=new $((0,p.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(i.Resize,this.handleCancel),this.windowListeners.add(i.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(i.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=B),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);_(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(M)}handleKeyDown(e){if((0,p.kx)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=er,coordinateGetter:i=el,scrollBehavior:a="smooth"}=r,{code:u}=e;if(l.end.includes(u))return void this.handleEnd(e);if(l.cancel.includes(u))return void this.handleCancel(e);let{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:M;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:n.current,currentCoordinates:c});if(d){let t=(0,p.Re)(d,c),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:u,isLeft:s,isBottom:c,maxScroll:f,minScroll:h}=V(n),g=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(l===o.Right?g.right-g.width/2:g.right,Math.max(l===o.Right?g.left:g.left+g.width/2,d.x)),y:Math.min(l===o.Down?g.bottom-g.height/2:g.bottom,Math.max(l===o.Down?g.top:g.top+g.height/2,d.y))},v=l===o.Right&&!u||l===o.Left&&!s,b=l===o.Down&&!c||l===o.Up&&!i;if(v&&p.x!==d.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=f.x||l===o.Left&&e>=h.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-f.x:n.scrollLeft-h.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(b&&p.y!==d.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=f.y||l===o.Up&&e>=h.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-f.y:n.scrollTop-h.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,p.WQ)((0,p.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eo(e){return!!(e&&"distance"in e)}function ea(e){return!!(e&&"delay"in e)}ei.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=er,onActivation:l}=t,{active:i}=n,{code:o}=e.nativeEvent;if(r.start.includes(o)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class eu{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,p.zk)(e);return e instanceof t?e:(0,p.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,p.TW)(i),this.documentListeners=new $(this.document),this.listeners=new $(n),this.windowListeners=new $((0,p.zk)(i)),this.initialCoordinates=null!=(r=(0,p.e_)(l))?r:M,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(i.Resize,this.handleCancel),this.windowListeners.add(i.DragStart,et),this.windowListeners.add(i.VisibilityChange,this.handleCancel),this.windowListeners.add(i.ContextMenu,et),this.documentListeners.add(i.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(ea(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eo(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(i.Click,en,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(i.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:o}}=l;if(!r)return;let a=null!=(t=(0,p.e_)(e))?t:M,u=(0,p.Re)(r,a);if(!n&&o){if(eo(o)){if(null!=o.tolerance&&ee(u,o.tolerance))return this.handleCancel();if(ee(u,o.distance))return this.handleStart()}return ea(o)&&ee(u,o.tolerance)?this.handleCancel():void this.handlePending(o,u)}e.cancelable&&e.preventDefault(),i(a)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let es={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class ec extends eu{constructor(e){let{event:t}=e;super(e,es,(0,p.TW)(t.target))}}ec.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let ed={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(a||(a={}));class ef extends eu{constructor(e){super(e,ed,(0,p.TW)(e.event.target))}}ef.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==a.RightClick&&(null==r||r({event:n}),!0)}}];let eh={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class eg extends eu{constructor(e){super(e,eh)}static setup(){return window.addEventListener(eh.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(eh.move.name,e)};function e(){}}}eg.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(u||(u={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(s||(s={}));let ep={x:{[l.Backward]:!1,[l.Forward]:!1},y:{[l.Backward]:!1,[l.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(c||(c={})),(d||(d={})).Optimized="optimized";let ev=new Map;function eb(e,t){return(0,p.KG)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function em(e){let{callback:t,disabled:n}=e,r=(0,p._q)(t),l=(0,h.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,h.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function ey(e){return new Q(B(e),e)}function ew(e,t,n){void 0===t&&(t=ey);let[r,l]=(0,h.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let o=function(e){let{callback:t,disabled:n}=e,r=(0,p._q)(t),l=(0,h.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,h.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),a=em({callback:i});return(0,p.Es)(()=>{i(),e?(null==a||a.observe(e),null==o||o.observe(document.body,{childList:!0,subtree:!0})):(null==a||a.disconnect(),null==o||o.disconnect())},[e]),r}let ex=[];function eD(e,t){void 0===t&&(t=[]);let n=(0,h.useRef)(null);return(0,h.useEffect)(()=>{n.current=null},t),(0,h.useEffect)(()=>{let t=e!==M;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,p.Re)(e,n.current):M}function eE(e){return(0,h.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eC=[],eR=[{sensor:ec,options:{}},{sensor:ei,options:{}}],eM={current:{}},eS={draggable:{measure:W},droppable:{measure:W,strategy:c.WhileDragging,frequency:d.Optimized},dragOverlay:{measure:B}};class ek extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eT={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new ek,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:E},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eS,measureDroppableContainers:E,windowRect:null,measuringScheduled:!1},eO={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:E,draggableNodes:new Map,over:null,measureDroppableContainers:E},eN=(0,h.createContext)(eO),eL=(0,h.createContext)(eT);function eI(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new ek}}}function eA(e,t){switch(t.type){case r.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case r.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case r.DragEnd:case r.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case r.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new ek(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case r.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let o=new ek(e.droppable.containers);return o.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:o}}}case r.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new ek(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function ej(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,h.useContext)(eN),i=(0,p.ZC)(r),o=(0,p.ZC)(null==n?void 0:n.id);return(0,h.useEffect)(()=>{if(!t&&!r&&i&&null!=o){if(!(0,p.kx)(i)||document.activeElement===i.target)return;let e=l.get(o);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,p.ag)(e);if(t){t.focus();break}}})}},[r,t,l,o,i]),null}let ez=(0,h.createContext)({...M,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(f||(f={}));let eP=(0,h.memo)(function(e){var t,n,i,o,a,d;let{id:v,accessibility:b,autoScroll:m=!0,children:w,sensors:x=eR,collisionDetection:E=j,measuring:C,modifiers:R,...S}=e,[k,T]=(0,h.useReducer)(eA,void 0,eI),[O,L]=function(){let[e]=(0,h.useState)(()=>new Set),t=(0,h.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,h.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[I,A]=(0,h.useState)(f.Uninitialized),F=I===f.Initialized,{draggable:{active:W,nodes:G,translate:U},droppable:{containers:J}}=k,$=null!=W?G.get(W):null,ee=(0,h.useRef)({initial:null,translated:null}),et=(0,h.useMemo)(()=>{var e;return null!=W?{id:W,data:null!=(e=null==$?void 0:$.data)?e:eM,rect:ee}:null},[W,$]),en=(0,h.useRef)(null),[er,el]=(0,h.useState)(null),[ei,eo]=(0,h.useState)(null),ea=(0,p.YN)(S,Object.values(S)),eu=(0,p.YG)("DndDescribedBy",v),es=(0,h.useMemo)(()=>J.getEnabled(),[J]),ec=(0,h.useMemo)(()=>({draggable:{...eS.draggable,...null==C?void 0:C.draggable},droppable:{...eS.droppable,...null==C?void 0:C.droppable},dragOverlay:{...eS.dragOverlay,...null==C?void 0:C.dragOverlay}}),[null==C?void 0:C.draggable,null==C?void 0:C.droppable,null==C?void 0:C.dragOverlay]),{droppableRects:ed,measureDroppableContainers:ef,measuringScheduled:eh}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,o]=(0,h.useState)(null),{frequency:a,measure:u,strategy:s}=l,d=(0,h.useRef)(e),f=function(){switch(s){case c.Always:return!1;case c.BeforeDragging:return n;default:return!n}}(),g=(0,p.YN)(f),v=(0,h.useCallback)(function(e){void 0===e&&(e=[]),g.current||o(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[g]),b=(0,h.useRef)(null),m=(0,p.KG)(t=>{if(f&&!n)return ev;if(!t||t===ev||d.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new Q(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,f,u]);return(0,h.useEffect)(()=>{d.current=e},[e]),(0,h.useEffect)(()=>{f||v()},[n,f]),(0,h.useEffect)(()=>{i&&i.length>0&&o(null)},[JSON.stringify(i)]),(0,h.useEffect)(()=>{f||"number"!=typeof a||null!==b.current||(b.current=setTimeout(()=>{v(),b.current=null},a))},[a,f,v,...r]),{droppableRects:m,measureDroppableContainers:v,measuringScheduled:null!=i}}(es,{dragging:F,dependencies:[U.x,U.y],config:ec.droppable}),eg=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,p.KG)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(G,W),ey=(0,h.useMemo)(()=>ei?(0,p.e_)(ei):null,[ei]),ek=function(){let e=(null==er?void 0:er.autoScrollEnabled)===!1,t="object"==typeof m?!1===m.enabled:!1===m,n=F&&!e&&!t;return"object"==typeof m?{...m,enabled:n}:{enabled:n}}(),eT=eb(eg,ec.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,h.useRef)(!1),{x:o,y:a}="boolean"==typeof l?{x:l,y:l}:l;(0,p.Es)(()=>{if(!o&&!a||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=z(n(e),r);if(o||(l.x=0),a||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=_(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,o,a,r,n])}({activeNode:null!=W?G.get(W):null,config:ek.layoutShiftCompensation,initialRect:eT,measure:ec.draggable.measure});let eO=ew(eg,ec.draggable.measure,eT),eP=ew(eg?eg.parentElement:null),eF=(0,h.useRef)({activatorEvent:null,active:null,activeNode:eg,collisionRect:null,collisions:null,droppableRects:ed,draggableNodes:G,draggingNode:null,draggingNodeRect:null,droppableContainers:J,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eB=J.getNodeFor(null==(t=eF.current.over)?void 0:t.id),eW=function(e){let{measure:t}=e,[n,r]=(0,h.useState)(null),l=em({callback:(0,h.useCallback)(e=>{for(let{target:n}of e)if((0,p.sb)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,h.useCallback)(e=>{let n=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,p.sb)(t)?t:e}(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[o,a]=(0,p.lk)(i);return(0,h.useMemo)(()=>({nodeRef:o,rect:n,setRef:a}),[n,o,a])}({measure:ec.dragOverlay.measure}),eY=null!=(n=eW.nodeRef.current)?n:eg,e_=F?null!=(i=eW.rect)?i:eO:null,eK=!!(eW.nodeRef.current&&eW.rect),eG=function(e){let t=eb(e);return z(e,t)}(eK?null:eO),eU=eE(eY?(0,p.zk)(eY):null),eX=function(e){let t=(0,h.useRef)(e),n=(0,p.KG)(n=>e?n&&n!==ex&&e&&t.current&&e.parentNode===t.current.parentNode?n:Y(e):ex,[e]);return(0,h.useEffect)(()=>{t.current=e},[e]),n}(F?null!=eB?eB:eg:null),eq=function(e,t){void 0===t&&(t=B);let[n]=e,r=eE(n?(0,p.zk)(n):null),[l,i]=(0,h.useState)(eC);function o(){i(()=>e.length?e.map(e=>q(e)?r:new Q(t(e),e)):eC)}let a=em({callback:o});return(0,p.Es)(()=>{null==a||a.disconnect(),o(),e.forEach(e=>null==a?void 0:a.observe(e))},[e]),l}(eX),eV=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}(R,{transform:{x:U.x-eG.x,y:U.y-eG.y,scaleX:1,scaleY:1},activatorEvent:ei,active:et,activeNodeRect:eO,containerNodeRect:eP,draggingNodeRect:e_,over:eF.current.over,overlayNodeRect:eW.rect,scrollableAncestors:eX,scrollableAncestorRects:eq,windowRect:eU}),eH=ey?(0,p.WQ)(ey,U):null,eZ=function(e){let[t,n]=(0,h.useState)(null),r=(0,h.useRef)(e),l=(0,h.useCallback)(e=>{let t=K(e.target);t&&n(e=>e?(e.set(t,X(t)),new Map(e)):null)},[]);return(0,h.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let o=e.map(e=>{let t=K(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,X(t)]):null}).filter(e=>null!=e);n(o.length?new Map(o):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=K(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,h.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,p.WQ)(e,t),M):Z(e):M,[e,t])}(eX),eJ=eD(eZ),eQ=eD(eZ,[eO]),e$=(0,p.WQ)(eV,eJ),e0=e_?P(e_,eV):null,e1=et&&e0?E({active:et,collisionRect:e0,droppableRects:ed,droppableContainers:es,pointerCoordinates:eH}):null,e5=N(e1,"id"),[e2,e6]=(0,h.useState)(null),e8=(a=eK?eV:(0,p.WQ)(eV,eQ),d=null!=(o=null==e2?void 0:e2.rect)?o:null,{...a,scaleX:d&&eO?d.width/eO.width:1,scaleY:d&&eO?d.height/eO.height:1}),e4=(0,h.useRef)(null),e9=(0,h.useCallback)((e,t)=>{let{sensor:n,options:l}=t;if(null==en.current)return;let i=G.get(en.current);if(!i)return;let o=e.nativeEvent,a=new n({active:en.current,activeNode:i,event:o,options:l,context:eF,onAbort(e){if(!G.get(e))return;let{onDragAbort:t}=ea.current,n={id:e};null==t||t(n),O({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!G.get(e))return;let{onDragPending:l}=ea.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),O({type:"onDragPending",event:i})},onStart(e){let t=en.current;if(null==t)return;let n=G.get(t);if(!n)return;let{onDragStart:l}=ea.current,i={activatorEvent:o,active:{id:t,data:n.data,rect:ee}};(0,g.unstable_batchedUpdates)(()=>{null==l||l(i),A(f.Initializing),T({type:r.DragStart,initialCoordinates:e,active:t}),O({type:"onDragStart",event:i}),el(e4.current),eo(o)})},onMove(e){T({type:r.DragMove,coordinates:e})},onEnd:u(r.DragEnd),onCancel:u(r.DragCancel)});function u(e){return async function(){let{active:t,collisions:n,over:l,scrollAdjustedTranslate:i}=eF.current,a=null;if(t&&i){let{cancelDrop:u}=ea.current;a={activatorEvent:o,active:t,collisions:n,delta:i,over:l},e===r.DragEnd&&"function"==typeof u&&await Promise.resolve(u(a))&&(e=r.DragCancel)}en.current=null,(0,g.unstable_batchedUpdates)(()=>{T({type:e}),A(f.Uninitialized),e6(null),el(null),eo(null),e4.current=null;let t=e===r.DragEnd?"onDragEnd":"onDragCancel";if(a){let e=ea.current[t];null==e||e(a),O({type:t,event:a})}})}}e4.current=a},[G]),e3=(0,h.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=G.get(r);null!==en.current||!i||l.dndKit||l.defaultPrevented||!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},en.current=r,e9(n,t))},[G,e9]),e7=(0,h.useMemo)(()=>x.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e3(e.handler,t)}))]},[]),[x,e3]);(0,h.useEffect)(()=>{if(!p.Sw)return;let e=x.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},x.map(e=>{let{sensor:t}=e;return t})),(0,p.Es)(()=>{eO&&I===f.Initializing&&A(f.Initialized)},[eO,I]),(0,h.useEffect)(()=>{let{onDragMove:e}=ea.current,{active:t,activatorEvent:n,collisions:r,over:l}=eF.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e$.x,y:e$.y},over:l};(0,g.unstable_batchedUpdates)(()=>{null==e||e(i),O({type:"onDragMove",event:i})})},[e$.x,e$.y]),(0,h.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=eF.current;if(!e||null==en.current||!t||!l)return;let{onDragOver:i}=ea.current,o=r.get(e5),a=o&&o.rect.current?{id:o.id,rect:o.rect.current,data:o.data,disabled:o.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:a};(0,g.unstable_batchedUpdates)(()=>{e6(a),null==i||i(u),O({type:"onDragOver",event:u})})},[e5]),(0,p.Es)(()=>{eF.current={activatorEvent:ei,active:et,activeNode:eg,collisionRect:e0,collisions:e1,droppableRects:ed,draggableNodes:G,draggingNode:eY,draggingNodeRect:e_,droppableContainers:J,over:e2,scrollableAncestors:eX,scrollAdjustedTranslate:e$},ee.current={initial:e_,translated:e0}},[et,eg,e1,e0,G,eY,e_,ed,J,e2,eX,e$]),function(e){let{acceleration:t,activator:n=u.Pointer,canScroll:r,draggingRect:i,enabled:o,interval:a=5,order:c=s.TreeOrder,pointerCoordinates:d,scrollableAncestors:f,scrollableAncestorRects:g,delta:v,threshold:b}=e,m=function(e){let{delta:t,disabled:n}=e,r=(0,p.ZC)(t);return(0,p.KG)(e=>{if(n||!r||!e)return ep;let i={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[l.Backward]:e.x[l.Backward]||-1===i.x,[l.Forward]:e.x[l.Forward]||1===i.x},y:{[l.Backward]:e.y[l.Backward]||-1===i.y,[l.Forward]:e.y[l.Forward]||1===i.y}}},[n,t,r])}({delta:v,disabled:!o}),[y,w]=(0,p.$$)(),x=(0,h.useRef)({x:0,y:0}),D=(0,h.useRef)({x:0,y:0}),E=(0,h.useMemo)(()=>{switch(n){case u.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case u.DraggableRect:return i}},[n,i,d]),C=(0,h.useRef)(null),R=(0,h.useCallback)(()=>{let e=C.current;if(!e)return;let t=x.current.x*D.current.x,n=x.current.y*D.current.y;e.scrollBy(t,n)},[]),M=(0,h.useMemo)(()=>c===s.TreeOrder?[...f].reverse():f,[c,f]);(0,h.useEffect)(()=>{if(!o||!f.length||!E)return void w();for(let e of M){if((null==r?void 0:r(e))===!1)continue;let n=g[f.indexOf(e)];if(!n)continue;let{direction:i,speed:o}=function(e,t,n,r,i){let{top:o,left:a,right:u,bottom:s}=n;void 0===r&&(r=10),void 0===i&&(i=H);let{isTop:c,isBottom:d,isLeft:f,isRight:h}=V(e),g={x:0,y:0},p={x:0,y:0},v={height:t.height*i.y,width:t.width*i.x};return!c&&o<=t.top+v.height?(g.y=l.Backward,p.y=r*Math.abs((t.top+v.height-o)/v.height)):!d&&s>=t.bottom-v.height&&(g.y=l.Forward,p.y=r*Math.abs((t.bottom-v.height-s)/v.height)),!h&&u>=t.right-v.width?(g.x=l.Forward,p.x=r*Math.abs((t.right-v.width-u)/v.width)):!f&&a<=t.left+v.width&&(g.x=l.Backward,p.x=r*Math.abs((t.left+v.width-a)/v.width)),{direction:g,speed:p}}(e,n,E,t,b);for(let e of["x","y"])m[e][i[e]]||(o[e]=0,i[e]=0);if(o.x>0||o.y>0){w(),C.current=e,y(R,a),x.current=o,D.current=i;return}}x.current={x:0,y:0},D.current={x:0,y:0},w()},[t,R,r,w,o,a,JSON.stringify(E),JSON.stringify(m),y,f,M,g,JSON.stringify(b)])}({...ek,delta:U,draggingRect:e0,pointerCoordinates:eH,scrollableAncestors:eX,scrollableAncestorRects:eq});let te=(0,h.useMemo)(()=>({active:et,activeNode:eg,activeNodeRect:eO,activatorEvent:ei,collisions:e1,containerNodeRect:eP,dragOverlay:eW,draggableNodes:G,droppableContainers:J,droppableRects:ed,over:e2,measureDroppableContainers:ef,scrollableAncestors:eX,scrollableAncestorRects:eq,measuringConfiguration:ec,measuringScheduled:eh,windowRect:eU}),[et,eg,eO,ei,e1,eP,eW,G,J,ed,e2,ef,eX,eq,ec,eh,eU]),tt=(0,h.useMemo)(()=>({activatorEvent:ei,activators:e7,active:et,activeNodeRect:eO,ariaDescribedById:{draggable:eu},dispatch:T,draggableNodes:G,over:e2,measureDroppableContainers:ef}),[ei,e7,et,eO,T,eu,G,e2,ef]);return h.createElement(y.Provider,{value:L},h.createElement(eN.Provider,{value:tt},h.createElement(eL.Provider,{value:te},h.createElement(ez.Provider,{value:e8},w)),h.createElement(ej,{disabled:(null==b?void 0:b.restoreFocus)===!1})),h.createElement(D,{...b,hiddenTextDescribedById:eu}))}),eF=(0,h.createContext)(null),eB="button";function eW(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,p.YG)("Draggable"),{activators:o,activatorEvent:a,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:f}=(0,h.useContext)(eN),{role:g=eB,roleDescription:v="draggable",tabIndex:b=0}=null!=l?l:{},m=(null==u?void 0:u.id)===t,y=(0,h.useContext)(m?ez:eF),[w,x]=(0,p.lk)(),[D,E]=(0,p.lk)(),C=(0,h.useMemo)(()=>o.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[o,t]),R=(0,p.YN)(n);return(0,p.Es)(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:D,data:R}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:a,activeNodeRect:s,attributes:(0,h.useMemo)(()=>({role:g,tabIndex:b,"aria-disabled":r,"aria-pressed":!!m&&g===eB||void 0,"aria-roledescription":v,"aria-describedby":c.draggable}),[r,g,b,m,v,c.draggable]),isDragging:m,listeners:r?void 0:C,node:w,over:f,setNodeRef:x,setActivatorNodeRef:E,transform:y}}function eY(){return(0,h.useContext)(eL)}let e_={timeout:25};function eK(e){let{data:t,disabled:n=!1,id:l,resizeObserverConfig:i}=e,o=(0,p.YG)("Droppable"),{active:a,dispatch:u,over:s,measureDroppableContainers:c}=(0,h.useContext)(eN),d=(0,h.useRef)({disabled:n}),f=(0,h.useRef)(!1),g=(0,h.useRef)(null),v=(0,h.useRef)(null),{disabled:b,updateMeasurementsFor:m,timeout:y}={...e_,...i},w=(0,p.YN)(null!=m?m:l),x=em({callback:(0,h.useCallback)(()=>{if(!f.current){f.current=!0;return}null!=v.current&&clearTimeout(v.current),v.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),v.current=null},y)},[y]),disabled:b||!a}),D=(0,h.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),f.current=!1),e&&x.observe(e))},[x]),[E,C]=(0,p.lk)(D),R=(0,p.YN)(t);return(0,h.useEffect)(()=>{x&&E.current&&(x.disconnect(),f.current=!1,x.observe(E.current))},[E,x]),(0,h.useEffect)(()=>(u({type:r.RegisterDroppable,element:{id:l,key:o,disabled:n,node:E,rect:g,data:R}}),()=>u({type:r.UnregisterDroppable,key:o,id:l})),[l]),(0,h.useEffect)(()=>{n!==d.current.disabled&&(u({type:r.SetDroppableDisabled,id:l,key:o,disabled:n}),d.current.disabled=n)},[l,o,n,u]),{active:a,rect:g,isOver:(null==s?void 0:s.id)===l,node:E,over:s,setNodeRef:C}}},78266:(e,t,n)=>{n.d(t,{$$:()=>p,Es:()=>h,KG:()=>b,Ks:()=>S,Ll:()=>a,Re:()=>C,Sw:()=>i,TW:()=>f,WQ:()=>E,YG:()=>x,YN:()=>v,ZC:()=>y,_q:()=>g,ag:()=>T,e_:()=>M,jn:()=>l,kx:()=>R,l6:()=>o,lk:()=>m,sb:()=>c,wz:()=>s,xZ:()=>d,zk:()=>u});var r=n(12115);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function o(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function a(e){return"nodeType"in e}function u(e){var t,n;return e?o(e)?e:a(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!o(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function f(e){return e?o(e)?e.document:a(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let h=i?r.useLayoutEffect:r.useEffect;function g(e){let t=(0,r.useRef)(e);return h(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function p(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return h(()=>{n.current!==e&&(n.current=e)},t),n}function b(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function m(e){let t=g(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function y(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function D(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let E=D(1),C=D(-1);function R(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function M(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let S=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[S.Translate.toString(e),S.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),k="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function T(e){return e.matches(k)?e:e.querySelector(k)}}}]);