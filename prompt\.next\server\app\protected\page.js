(()=>{var a={};a.id=379,a.ids=[379],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(a,b,c)=>{let{createProxy:d}=c(39844);a.exports=d("D:\\Cursor Project\\prompy augment\\prompt\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10726:(a,b,c)=>{Promise.resolve().then(c.bind(c,30125)),Promise.resolve().then(c.bind(c,11811))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11811:(a,b,c)=>{"use strict";c.d(b,{Checkbox:()=>D});var d=c(60687),e=c(43210),f=c(98599),g=c(11273),h=c(70569),i=c(65551),j=c(18853),k=c(46059),l=c(14163),m="Checkbox",[n,o]=(0,g.A)(m),[p,q]=n(m);function r(a){let{__scopeCheckbox:b,checked:c,children:f,defaultChecked:g,disabled:h,form:j,name:k,onCheckedChange:l,required:n,value:o="on",internal_do_not_use_render:q}=a,[r,s]=(0,i.i)({prop:c,defaultProp:g??!1,onChange:l,caller:m}),[t,u]=e.useState(null),[v,w]=e.useState(null),x=e.useRef(!1),y=!t||!!j||!!t.closest("form"),A={checked:r,disabled:h,setChecked:s,control:t,setControl:u,name:k,form:j,value:o,hasConsumerStoppedPropagationRef:x,required:n,defaultChecked:!z(g)&&g,isFormControl:y,bubbleInput:v,setBubbleInput:w};return(0,d.jsx)(p,{scope:b,...A,children:"function"==typeof q?q(A):f})}var s="CheckboxTrigger",t=e.forwardRef(({__scopeCheckbox:a,onKeyDown:b,onClick:c,...g},i)=>{let{control:j,value:k,disabled:m,checked:n,required:o,setControl:p,setChecked:r,hasConsumerStoppedPropagationRef:t,isFormControl:u,bubbleInput:v}=q(s,a),w=(0,f.s)(i,p),x=e.useRef(n);return e.useEffect(()=>{let a=j?.form;if(a){let b=()=>r(x.current);return a.addEventListener("reset",b),()=>a.removeEventListener("reset",b)}},[j,r]),(0,d.jsx)(l.sG.button,{type:"button",role:"checkbox","aria-checked":z(n)?"mixed":n,"aria-required":o,"data-state":A(n),"data-disabled":m?"":void 0,disabled:m,value:k,...g,ref:w,onKeyDown:(0,h.m)(b,a=>{"Enter"===a.key&&a.preventDefault()}),onClick:(0,h.m)(c,a=>{r(a=>!!z(a)||!a),v&&u&&(t.current=a.isPropagationStopped(),t.current||a.stopPropagation())})})});t.displayName=s;var u=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,name:e,checked:f,defaultChecked:g,required:h,disabled:i,value:j,onCheckedChange:k,form:l,...m}=a;return(0,d.jsx)(r,{__scopeCheckbox:c,checked:f,defaultChecked:g,disabled:i,required:h,onCheckedChange:k,name:e,form:l,value:j,internal_do_not_use_render:({isFormControl:a})=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(t,{...m,ref:b,__scopeCheckbox:c}),a&&(0,d.jsx)(y,{__scopeCheckbox:c})]})})});u.displayName=m;var v="CheckboxIndicator",w=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,forceMount:e,...f}=a,g=q(v,c);return(0,d.jsx)(k.C,{present:e||z(g.checked)||!0===g.checked,children:(0,d.jsx)(l.sG.span,{"data-state":A(g.checked),"data-disabled":g.disabled?"":void 0,...f,ref:b,style:{pointerEvents:"none",...a.style}})})});w.displayName=v;var x="CheckboxBubbleInput",y=e.forwardRef(({__scopeCheckbox:a,...b},c)=>{let{control:g,hasConsumerStoppedPropagationRef:h,checked:i,defaultChecked:k,required:m,disabled:n,name:o,value:p,form:r,bubbleInput:s,setBubbleInput:t}=q(x,a),u=(0,f.s)(c,t),v=function(a){let b=e.useRef({value:a,previous:a});return e.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(i),w=(0,j.X)(g);e.useEffect(()=>{if(!s)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,b=!h.current;if(v!==i&&a){let c=new Event("click",{bubbles:b});s.indeterminate=z(i),a.call(s,!z(i)&&i),s.dispatchEvent(c)}},[s,v,i,h]);let y=e.useRef(!z(i)&&i);return(0,d.jsx)(l.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:k??y.current,required:m,disabled:n,name:o,value:p,form:r,...b,tabIndex:-1,ref:u,style:{...b.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function z(a){return"indeterminate"===a}function A(a){return z(a)?"indeterminate":a?"checked":"unchecked"}y.displayName=x;var B=c(13964),C=c(96241);let D=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(u,{ref:c,className:(0,C.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...b,children:(0,d.jsx)(w,{className:(0,C.cn)("flex items-center justify-center text-current"),children:(0,d.jsx)(B.A,{className:"h-4 w-4"})})}));D.displayName=u.displayName},11997:a=>{"use strict";a.exports=require("punycode")},13755:(a,b,c)=>{"use strict";c.d(b,{CodeBlock:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\components\\tutorial\\code-block.tsx","CodeBlock")},16189:(a,b,c)=>{"use strict";var d=c(65773);c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24574:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["protected",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,81300)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\protected\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,84295)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\protected\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,38836)),"D:\\Cursor Project\\prompy augment\\prompt\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]}.children,H=["D:\\Cursor Project\\prompy augment\\prompt\\app\\protected\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/protected/page",pathname:"/protected",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/protected/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(96241);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28086:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(9866),e=c(44999);async function f(){let a=await (0,e.UL)();return(0,d.createServerClient)("https://vigxjamjjlxzmuzwxwyl.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZpZ3hqYW1qamx4em11end4d3lsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Nzc4NTAsImV4cCI6MjA2OTA1Mzg1MH0.re45eDFz2pz7Tswcx5sE1bWuCP7MH481XHgsecj578E",{cookies:{getAll:()=>a.getAll(),setAll(b){try{b.forEach(({name:b,value:c,options:d})=>a.set(b,c,d))}catch{}}}})}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30125:(a,b,c)=>{"use strict";c.d(b,{CodeBlock:()=>i});var d=c(60687),e=c(43210),f=c(24934);let g=()=>(0,d.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,d.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,d.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]}),h=()=>(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,d.jsx)("polyline",{points:"20 6 9 17 4 12"})});function i({code:a}){let[b,c]=(0,e.useState)(g),i=async()=>{await navigator?.clipboard?.writeText(a),c(h),setTimeout(()=>c(g),2e3)};return(0,d.jsxs)("pre",{className:"bg-muted rounded-md p-6 my-6 relative",children:[(0,d.jsx)(f.$,{size:"icon",onClick:i,variant:"outline",className:"absolute right-2 top-2",children:b}),(0,d.jsx)("code",{className:"text-xs p-3",children:a})]})}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47828:(a,b,c)=>{"use strict";c.d(b,{U:()=>e});var d=c(59522);function e(){return(0,d.createBrowserClient)("https://vigxjamjjlxzmuzwxwyl.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZpZ3hqYW1qamx4em11end4d3lsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Nzc4NTAsImV4cCI6MjA2OTA1Mzg1MH0.re45eDFz2pz7Tswcx5sE1bWuCP7MH481XHgsecj578E")}},47990:()=>{},49196:(a,b,c)=>{"use strict";c.d(b,{ThemeSwitcher:()=>u});var d=c(60687),e=c(24934),f=c(43210),g=c(10436),h=c(14952),i=c(13964),j=c(65822),k=c(96241);let l=g.bL,m=g.l9;g.YJ,g.ZL,g.Pb;let n=g.z6;f.forwardRef(({className:a,inset:b,children:c,...e},f)=>(0,d.jsxs)(g.ZP,{ref:f,className:(0,k.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",b&&"pl-8",a),...e,children:[c,(0,d.jsx)(h.A,{className:"ml-auto"})]})).displayName=g.ZP.displayName,f.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.G5,{ref:c,className:(0,k.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...b})).displayName=g.G5.displayName;let o=f.forwardRef(({className:a,sideOffset:b=4,...c},e)=>(0,d.jsx)(g.ZL,{children:(0,d.jsx)(g.UC,{ref:e,sideOffset:b,className:(0,k.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...c})}));o.displayName=g.UC.displayName,f.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(g.q7,{ref:e,className:(0,k.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",b&&"pl-8",a),...c})).displayName=g.q7.displayName,f.forwardRef(({className:a,children:b,checked:c,...e},f)=>(0,d.jsxs)(g.H_,{ref:f,className:(0,k.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:c,...e,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(g.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),b]})).displayName=g.H_.displayName;let p=f.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(g.hN,{ref:e,className:(0,k.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(g.VF,{children:(0,d.jsx)(j.A,{className:"h-2 w-2 fill-current"})})}),b]}));p.displayName=g.hN.displayName,f.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(g.JU,{ref:e,className:(0,k.cn)("px-2 py-1.5 text-sm font-semibold",b&&"pl-8",a),...c})).displayName=g.JU.displayName,f.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.wv,{ref:c,className:(0,k.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=g.wv.displayName;var q=c(21134),r=c(363),s=c(34410),t=c(10218);let u=()=>{let[a,b]=(0,f.useState)(!1),{theme:c,setTheme:g}=(0,t.D)();return((0,f.useEffect)(()=>{b(!0)},[]),a)?(0,d.jsxs)(l,{children:[(0,d.jsx)(m,{asChild:!0,children:(0,d.jsx)(e.$,{variant:"ghost",size:"sm",children:"light"===c?(0,d.jsx)(q.A,{size:16,className:"text-muted-foreground"},"light"):"dark"===c?(0,d.jsx)(r.A,{size:16,className:"text-muted-foreground"},"dark"):(0,d.jsx)(s.A,{size:16,className:"text-muted-foreground"},"system")})}),(0,d.jsx)(o,{className:"w-content",align:"start",children:(0,d.jsxs)(n,{value:c,onValueChange:a=>g(a),children:[(0,d.jsxs)(p,{className:"flex gap-2",value:"light",children:[(0,d.jsx)(q.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"Light"})]}),(0,d.jsxs)(p,{className:"flex gap-2",value:"dark",children:[(0,d.jsx)(r.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"Dark"})]}),(0,d.jsxs)(p,{className:"flex gap-2",value:"system",children:[(0,d.jsx)(s.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"System"})]})]})})]}):null}},53506:(a,b,c)=>{"use strict";c.d(b,{AuthButton:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AuthButton() from the server but AuthButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\components\\auth-button.tsx","AuthButton")},54656:(a,b,c)=>{"use strict";c.d(b,{ThemeSwitcher:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ThemeSwitcher() from the server but ThemeSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\components\\theme-switcher.tsx","ThemeSwitcher")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59463:(a,b,c)=>{"use strict";c.d(b,{AuthButton:()=>l});var d=c(60687),e=c(85814),f=c.n(e),g=c(24934),h=c(47828),i=c(16189);function j(){let a=(0,i.useRouter)(),b=async()=>{let b=(0,h.U)();await b.auth.signOut(),a.push("/auth/login")};return(0,d.jsx)(g.$,{onClick:b,children:"Logout"})}var k=c(43210);function l(){let[a,b]=(0,k.useState)(null),[c,e]=(0,k.useState)(!0);return((0,h.U)(),c)?(0,d.jsx)("div",{className:"h-8 w-20 bg-gray-200 animate-pulse rounded"}):a?(0,d.jsxs)("div",{className:"flex items-center gap-4",children:["Hey, ",a.email,"!",(0,d.jsx)(j,{})]}):(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(g.$,{asChild:!0,size:"sm",variant:"outline",children:(0,d.jsx)(f(),{href:"/auth/login",children:"Sign in"})}),(0,d.jsx)(g.$,{asChild:!0,size:"sm",variant:"default",children:(0,d.jsx)(f(),{href:"/auth/sign-up",children:"Sign up"})})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64169:(a,b,c)=>{Promise.resolve().then(c.bind(c,59463)),Promise.resolve().then(c.bind(c,49196)),Promise.resolve().then(c.t.bind(c,85814,23))},66819:(a,b,c)=>{"use strict";c.d(b,{A:()=>g,cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}let g="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZpZ3hqYW1qamx4em11end4d3lsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Nzc4NTAsImV4cCI6MjA2OTA1Mzg1MH0.re45eDFz2pz7Tswcx5sE1bWuCP7MH481XHgsecj578E"},70558:(a,b,c)=>{Promise.resolve().then(c.bind(c,13755)),Promise.resolve().then(c.bind(c,98975))},73897:(a,b,c)=>{Promise.resolve().then(c.bind(c,53506)),Promise.resolve().then(c.bind(c,54656)),Promise.resolve().then(c.t.bind(c,4536,23))},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81300:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>t});var d=c(37413),e=c(39916),f=c(28086),g=c(61120);let h=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},i=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var j={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let k=(0,g.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:e="",children:f,iconNode:h,...k},l)=>(0,g.createElement)("svg",{ref:l,...j,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:i("lucide",e),...!f&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...h.map(([a,b])=>(0,g.createElement)(a,b)),...Array.isArray(f)?f:[f]])),l=((a,b)=>{let c=(0,g.forwardRef)(({className:c,...d},e)=>(0,g.createElement)(k,{ref:e,iconNode:b,className:i(`lucide-${h(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...d}));return c.displayName=h(a),c})("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var m=c(98975);function n({title:a,children:b}){return(0,d.jsxs)("li",{className:"relative",children:[(0,d.jsx)(m.Checkbox,{id:a,name:a,className:"absolute top-[3px] mr-2 peer"}),(0,d.jsxs)("label",{htmlFor:a,className:"relative text-base text-foreground peer-checked:line-through font-medium",children:[(0,d.jsx)("span",{className:"ml-8",children:a}),(0,d.jsx)("div",{className:"ml-8 text-sm peer-checked:line-through font-normal text-muted-foreground",children:b})]})]})}var o=c(13755);let p=`create table notes (
  id bigserial primary key,
  title text
);

insert into notes(title)
values
  ('Today I created a Supabase project.'),
  ('I added some data and queried it from Next.js.'),
  ('It was awesome!');
`.trim(),q=`import { createClient } from '@/utils/supabase/server'

export default async function Page() {
  const supabase = await createClient()
  const { data: notes } = await supabase.from('notes').select()

  return <pre>{JSON.stringify(notes, null, 2)}</pre>
}
`.trim(),r=`'use client'

import { createClient } from '@/utils/supabase/client'
import { useEffect, useState } from 'react'

export default function Page() {
  const [notes, setNotes] = useState<any[] | null>(null)
  const supabase = createClient()

  useEffect(() => {
    const getData = async () => {
      const { data } = await supabase.from('notes').select()
      setNotes(data)
    }
    getData()
  }, [])

  return <pre>{JSON.stringify(notes, null, 2)}</pre>
}
`.trim();function s(){return(0,d.jsxs)("ol",{className:"flex flex-col gap-6",children:[(0,d.jsxs)(n,{title:"Create some tables and insert some data",children:[(0,d.jsxs)("p",{children:["Head over to the"," ",(0,d.jsx)("a",{href:"https://supabase.com/dashboard/project/_/editor",className:"font-bold hover:underline text-foreground/80",target:"_blank",rel:"noreferrer",children:"Table Editor"})," ","for your Supabase project to create a table and insert some example data. If you're stuck for creativity, you can copy and paste the following into the"," ",(0,d.jsx)("a",{href:"https://supabase.com/dashboard/project/_/sql/new",className:"font-bold hover:underline text-foreground/80",target:"_blank",rel:"noreferrer",children:"SQL Editor"})," ","and click RUN!"]}),(0,d.jsx)(o.CodeBlock,{code:p})]}),(0,d.jsxs)(n,{title:"Query Supabase data from Next.js",children:[(0,d.jsxs)("p",{children:["To create a Supabase client and query data from an Async Server Component, create a new page.tsx file at"," ",(0,d.jsx)("span",{className:"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-xs font-medium text-secondary-foreground border",children:"/app/notes/page.tsx"})," ","and add the following."]}),(0,d.jsx)(o.CodeBlock,{code:q}),(0,d.jsx)("p",{children:"Alternatively, you can use a Client Component."}),(0,d.jsx)(o.CodeBlock,{code:r})]}),(0,d.jsxs)(n,{title:"Explore the Supabase UI Library",children:[(0,d.jsxs)("p",{children:["Head over to the"," ",(0,d.jsx)("a",{href:"https://supabase.com/ui",className:"font-bold hover:underline text-foreground/80",children:"Supabase UI library"})," ","and try installing some blocks. For example, you can install a Realtime Chat block by running:"]}),(0,d.jsx)(o.CodeBlock,{code:"npx shadcn@latest add https://supabase.com/ui/r/realtime-chat-nextjs.json"})]}),(0,d.jsx)(n,{title:"Build in a weekend and scale to millions!",children:(0,d.jsx)("p",{children:"You're ready to launch your product to the world! \uD83D\uDE80"})})]})}async function t(){let a=await (0,f.U)(),{data:b,error:c}=await a.auth.getClaims();return(c||!b?.claims)&&(0,e.redirect)("/auth/login"),(0,d.jsxs)("div",{className:"flex-1 w-full flex flex-col gap-12",children:[(0,d.jsx)("div",{className:"w-full",children:(0,d.jsxs)("div",{className:"bg-accent text-sm p-3 px-5 rounded-md text-foreground flex gap-3 items-center",children:[(0,d.jsx)(l,{size:"16",strokeWidth:2}),"This is a protected page that you can only see as an authenticated user"]})}),(0,d.jsxs)("div",{className:"flex flex-col gap-2 items-start",children:[(0,d.jsx)("h2",{className:"font-bold text-2xl mb-4",children:"Your user details"}),(0,d.jsx)("pre",{className:"text-xs font-mono p-3 rounded border max-h-32 overflow-auto",children:JSON.stringify(b.claims,null,2)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"font-bold text-2xl mb-4",children:"Next steps"}),(0,d.jsx)(s,{})]})]})}},81630:a=>{"use strict";a.exports=require("http")},84295:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y});var d=c(37413),e=c(4536),f=c.n(e),g=c(61120);function h(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}var i=function(a){let b=function(a){let b=g.forwardRef((a,b)=>{let{children:c,...d}=a;if(g.isValidElement(c)){var e;let a,f,i=(e=c,(f=(a=Object.getOwnPropertyDescriptor(e.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.ref:(f=(a=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props);return c.type!==g.Fragment&&(j.ref=b?function(...a){return b=>{let c=!1,d=a.map(a=>{let d=h(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():h(a[b],null)}}}}(b,i):i),g.cloneElement(c,j)}return g.Children.count(c)>1?g.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=g.forwardRef((a,c)=>{let{children:e,...f}=a,h=g.Children.toArray(e),i=h.find(k);if(i){let a=i.props.children,e=h.map(b=>b!==i?b:g.Children.count(a)>1?g.Children.only(null):g.isValidElement(a)?a.props.children:null);return(0,d.jsx)(b,{...f,ref:c,children:g.isValidElement(a)?g.cloneElement(a,void 0,e):null})}return(0,d.jsx)(b,{...f,ref:c,children:e})});return c.displayName=`${a}.Slot`,c}("Slot"),j=Symbol("radix.slottable");function k(a){return g.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===j}var l=c(75986);let m=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,n=l.$,o=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return n(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:e,defaultVariants:f}=b,g=Object.keys(e).map(a=>{let b=null==c?void 0:c[a],d=null==f?void 0:f[a];if(null===b)return null;let g=m(b)||m(d);return e[a][g]}),h=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return n(a,g,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...f,...h}[b]):({...f,...h})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)};var p=c(66819);let q=o("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),r=g.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},g)=>(0,d.jsx)(e?i:"button",{className:(0,p.cn)(q({variant:b,size:c,className:a})),ref:g,...f}));function s(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(f(),{href:"https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&project-name=nextjs-with-supabase&repository-name=nextjs-with-supabase&demo-title=nextjs-with-supabase&demo-description=This+starter+configures+Supabase+Auth+to+use+cookies%2C+making+the+user%27s+session+available+throughout+the+entire+Next.js+app+-+Client+Components%2C+Server+Components%2C+Route+Handlers%2C+Server+Actions+and+Middleware.&demo-url=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2F&external-id=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&demo-image=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2Fopengraph-image.png",target:"_blank",children:(0,d.jsxs)(r,{className:"flex items-center gap-2",size:"sm",children:[(0,d.jsx)("svg",{className:"h-3 w-3",viewBox:"0 0 76 65",fill:"hsl(var(--background)/1)",xmlns:"http://www.w3.org/2000/svg",children:(0,d.jsx)("path",{d:"M37.5274 0L75.0548 65H0L37.5274 0Z",fill:"inherit"})}),(0,d.jsx)("span",{children:"Deploy to Vercel"})]})})})}r.displayName="Button";let t=o("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function u({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,p.cn)(t({variant:b}),a),...c})}function v(){return(0,d.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,d.jsx)(u,{variant:"outline",className:"font-normal",children:"Supabase environment variables required"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(r,{size:"sm",variant:"outline",disabled:!0,children:"Sign in"}),(0,d.jsx)(r,{size:"sm",variant:"default",disabled:!0,children:"Sign up"})]})]})}var w=c(53506),x=c(54656);function y({children:a}){return(0,d.jsx)("main",{className:"min-h-screen flex flex-col items-center",children:(0,d.jsxs)("div",{className:"flex-1 w-full flex flex-col gap-20 items-center",children:[(0,d.jsx)("nav",{className:"w-full flex justify-center border-b border-b-foreground/10 h-16",children:(0,d.jsxs)("div",{className:"w-full max-w-5xl flex justify-between items-center p-3 px-5 text-sm",children:[(0,d.jsxs)("div",{className:"flex gap-5 items-center font-semibold",children:[(0,d.jsx)(f(),{href:"/",children:"Next.js Supabase Starter"}),(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsx)(s,{})})]}),p.A?(0,d.jsx)(w.AuthButton,{}):(0,d.jsx)(v,{})]})}),(0,d.jsx)("div",{className:"flex-1 flex flex-col gap-20 max-w-5xl p-5",children:a}),(0,d.jsxs)("footer",{className:"w-full flex items-center justify-center border-t mx-auto text-center text-xs gap-8 py-16",children:[(0,d.jsxs)("p",{children:["Powered by"," ",(0,d.jsx)("a",{href:"https://supabase.com/?utm_source=create-next-app&utm_medium=template&utm_term=nextjs",target:"_blank",className:"font-bold hover:underline",rel:"noreferrer",children:"Supabase"})]}),(0,d.jsx)(x.ThemeSwitcher,{})]})]})})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},98975:(a,b,c)=>{"use strict";c.d(b,{Checkbox:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Checkbox() from the server but Checkbox is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment\\prompt\\components\\ui\\checkbox.tsx","Checkbox")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,519,522,814,121,41,17,780],()=>b(b.s=24574));module.exports=c})();