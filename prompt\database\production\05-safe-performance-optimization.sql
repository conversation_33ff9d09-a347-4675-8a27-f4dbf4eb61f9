-- =====================================================
-- 安全的性能优化脚本
-- =====================================================
-- 版本: 1.0.1
-- 创建时间: 2025-07-26
-- 描述: 安全地优化数据库性能，检查表结构后创建索引
-- =====================================================

-- 首先检查现有表结构
DO $$
BEGIN
    RAISE NOTICE '🔍 检查数据库表结构...';
    
    -- 检查 categories 表
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        RAISE NOTICE '✅ categories 表存在';
        
        -- 检查 deleted_at 字段
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'categories' AND column_name = 'deleted_at'
        ) THEN
            RAISE NOTICE '✅ categories.deleted_at 字段存在';
        ELSE
            RAISE NOTICE '⚠️ categories.deleted_at 字段不存在';
        END IF;
    END IF;
    
    -- 检查 prompts 表
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'prompts') THEN
        RAISE NOTICE '✅ prompts 表存在';
    END IF;
    
    -- 检查 tags 表
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tags') THEN
        RAISE NOTICE '✅ tags 表存在';
    END IF;
END $$;

-- 1. 安全地创建 categories 表索引
DO $$
BEGIN
    -- 基础索引（不依赖 deleted_at）
    CREATE INDEX IF NOT EXISTS idx_categories_user_id 
    ON categories(user_id);
    
    CREATE INDEX IF NOT EXISTS idx_categories_sort_order 
    ON categories(sort_order);
    
    -- 如果有 deleted_at 字段，创建复合索引
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'deleted_at'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_categories_user_sort_active 
        ON categories(user_id, sort_order) 
        WHERE deleted_at IS NULL;
        
        CREATE INDEX IF NOT EXISTS idx_categories_user_created 
        ON categories(user_id, created_at DESC) 
        WHERE deleted_at IS NULL;
    ELSE
        CREATE INDEX IF NOT EXISTS idx_categories_user_sort 
        ON categories(user_id, sort_order);
        
        CREATE INDEX IF NOT EXISTS idx_categories_user_created 
        ON categories(user_id, created_at DESC);
    END IF;
    
    RAISE NOTICE '✅ categories 表索引创建完成';
END $$;

-- 2. 安全地创建 prompts 表索引
DO $$
BEGIN
    -- 基础索引
    CREATE INDEX IF NOT EXISTS idx_prompts_user_id 
    ON prompts(user_id);
    
    CREATE INDEX IF NOT EXISTS idx_prompts_category_id 
    ON prompts(category_id);
    
    CREATE INDEX IF NOT EXISTS idx_prompts_usage_count 
    ON prompts(usage_count DESC);
    
    -- 如果有 deleted_at 字段，创建复合索引
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'prompts' AND column_name = 'deleted_at'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_prompts_user_category_active 
        ON prompts(user_id, category_id) 
        WHERE deleted_at IS NULL;
        
        CREATE INDEX IF NOT EXISTS idx_prompts_user_created_active 
        ON prompts(user_id, created_at DESC) 
        WHERE deleted_at IS NULL;
        
        CREATE INDEX IF NOT EXISTS idx_prompts_user_updated_active 
        ON prompts(user_id, updated_at DESC) 
        WHERE deleted_at IS NULL;
    ELSE
        CREATE INDEX IF NOT EXISTS idx_prompts_user_category 
        ON prompts(user_id, category_id);
        
        CREATE INDEX IF NOT EXISTS idx_prompts_user_created 
        ON prompts(user_id, created_at DESC);
        
        CREATE INDEX IF NOT EXISTS idx_prompts_user_updated 
        ON prompts(user_id, updated_at DESC);
    END IF;
    
    RAISE NOTICE '✅ prompts 表索引创建完成';
END $$;

-- 3. 创建搜索索引（如果支持全文搜索）
DO $$
BEGIN
    -- 检查是否支持 GIN 索引
    IF EXISTS (
        SELECT 1 FROM pg_extension WHERE extname = 'pg_trgm'
    ) THEN
        -- 使用 trigram 索引进行模糊搜索
        CREATE INDEX IF NOT EXISTS idx_prompts_title_trgm 
        ON prompts USING gin(title gin_trgm_ops);
        
        CREATE INDEX IF NOT EXISTS idx_prompts_content_trgm 
        ON prompts USING gin(content gin_trgm_ops);
        
        RAISE NOTICE '✅ 全文搜索索引创建完成（trigram）';
    ELSE
        -- 使用普通 B-tree 索引
        CREATE INDEX IF NOT EXISTS idx_prompts_title_btree 
        ON prompts(title);
        
        RAISE NOTICE '✅ 基础搜索索引创建完成（B-tree）';
    END IF;
END $$;

-- 4. 安全地创建其他表索引
DO $$
BEGIN
    -- tags 表索引
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tags') THEN
        CREATE INDEX IF NOT EXISTS idx_tags_user_id 
        ON tags(user_id);
        
        CREATE INDEX IF NOT EXISTS idx_tags_name 
        ON tags(name);
        
        RAISE NOTICE '✅ tags 表索引创建完成';
    END IF;
    
    -- prompt_tags 表索引
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'prompt_tags') THEN
        CREATE INDEX IF NOT EXISTS idx_prompt_tags_prompt 
        ON prompt_tags(prompt_id);
        
        CREATE INDEX IF NOT EXISTS idx_prompt_tags_tag 
        ON prompt_tags(tag_id);
        
        RAISE NOTICE '✅ prompt_tags 表索引创建完成';
    END IF;
    
    -- user_preferences 表索引
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_preferences') THEN
        CREATE INDEX IF NOT EXISTS idx_user_preferences_user 
        ON user_preferences(user_id);
        
        RAISE NOTICE '✅ user_preferences 表索引创建完成';
    END IF;
    
    -- search_history 表索引
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'search_history') THEN
        CREATE INDEX IF NOT EXISTS idx_search_history_user_created 
        ON search_history(user_id, created_at DESC);
        
        RAISE NOTICE '✅ search_history 表索引创建完成';
    END IF;
END $$;

-- 5. 创建简化的搜索函数
CREATE OR REPLACE FUNCTION search_prompts_simple(
    search_query TEXT,
    user_uuid UUID,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    category_id UUID,
    usage_count INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.title,
        p.content,
        p.category_id,
        p.usage_count,
        p.created_at,
        p.updated_at
    FROM prompts p
    WHERE 
        p.user_id = user_uuid
        AND (
            search_query IS NULL 
            OR search_query = '' 
            OR p.title ILIKE '%' || search_query || '%'
            OR p.content ILIKE '%' || search_query || '%'
        )
    ORDER BY p.updated_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql STABLE;

-- 6. 授予权限
GRANT EXECUTE ON FUNCTION search_prompts_simple TO authenticated;

-- 7. 更新表统计信息
DO $$
BEGIN
    -- 只分析存在的表
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        ANALYZE categories;
        RAISE NOTICE '✅ categories 表统计信息已更新';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'prompts') THEN
        ANALYZE prompts;
        RAISE NOTICE '✅ prompts 表统计信息已更新';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tags') THEN
        ANALYZE tags;
        RAISE NOTICE '✅ tags 表统计信息已更新';
    END IF;
END $$;

-- 8. 显示优化结果
DO $$
DECLARE
    index_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE tablename IN ('categories', 'prompts', 'tags', 'prompt_tags', 'user_preferences', 'search_history');
    
    RAISE NOTICE '🎉 性能优化完成！';
    RAISE NOTICE '📊 已创建/验证 % 个索引', index_count;
    RAISE NOTICE '🔍 已创建搜索优化函数';
    RAISE NOTICE '📈 数据库性能已优化';
END $$;
