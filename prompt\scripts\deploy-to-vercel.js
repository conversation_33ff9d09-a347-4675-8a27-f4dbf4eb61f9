#!/usr/bin/env node

/**
 * 自动化 Vercel 部署脚本
 */

const { execSync } = require('child_process');
const fs = require('fs');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  header: (msg) => console.log(`\n${colors.bold}${colors.blue}🚀 ${msg}${colors.reset}\n`)
};

function runCommand(command, description) {
  try {
    log.info(`${description}...`);
    const result = execSync(command, { encoding: 'utf8', stdio: 'inherit' });
    log.success(`${description} 完成`);
    return true;
  } catch (error) {
    log.error(`${description} 失败: ${error.message}`);
    return false;
  }
}

function checkVercelCLI() {
  try {
    execSync('vercel --version', { encoding: 'utf8', stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

async function deployToVercel() {
  log.header('开始部署到 Vercel');

  // 1. 检查 Vercel CLI
  if (!checkVercelCLI()) {
    log.warning('Vercel CLI 未安装，正在安装...');
    if (!runCommand('npm install -g vercel', '安装 Vercel CLI')) {
      log.error('请手动安装 Vercel CLI: npm install -g vercel');
      return;
    }
  } else {
    log.success('Vercel CLI 已安装');
  }

  // 2. 运行部署前检查
  log.header('运行部署前检查');
  if (!runCommand('npm run quick-check', '快速检查')) {
    log.error('部署前检查失败，请修复问题后重试');
    return;
  }

  // 3. 检查环境变量文件
  if (!fs.existsSync('.env.example')) {
    log.error('.env.example 文件不存在');
    return;
  }

  // 4. 显示环境变量提示
  log.header('环境变量配置');
  console.log('请确保您已经准备好以下信息：');
  console.log('1. Supabase 项目 URL');
  console.log('2. Supabase Anon Key');
  console.log('');
  console.log('如果还没有 Supabase 项目，请先访问 https://app.supabase.com/ 创建');
  console.log('');

  // 5. 初始化 Vercel 项目
  log.header('初始化 Vercel 项目');
  console.log('即将运行 vercel 命令，请按照提示操作：');
  console.log('- Set up and deploy? 选择 Y');
  console.log('- Which scope? 选择您的账户');
  console.log('- Link to existing project? 选择 N');
  console.log('- Project name? 输入项目名称（如：prompt-management-tool）');
  console.log('- Code directory? 直接回车（使用当前目录）');
  console.log('');

  if (!runCommand('vercel', '初始化 Vercel 项目')) {
    log.error('Vercel 项目初始化失败');
    return;
  }

  // 6. 环境变量配置提示
  log.header('配置环境变量');
  console.log('现在需要配置环境变量，您可以选择：');
  console.log('');
  console.log('方法一：通过 CLI 配置');
  console.log('  vercel env add NEXT_PUBLIC_SUPABASE_URL production');
  console.log('  vercel env add NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY production');
  console.log('');
  console.log('方法二：通过 Vercel Dashboard 配置');
  console.log('  1. 访问 https://vercel.com/dashboard');
  console.log('  2. 找到您的项目');
  console.log('  3. 进入 Settings > Environment Variables');
  console.log('  4. 添加环境变量');
  console.log('');

  // 7. 部署到生产环境
  log.header('部署到生产环境');
  console.log('配置完环境变量后，运行以下命令部署：');
  console.log('');
  console.log(`${colors.bold}vercel --prod${colors.reset}`);
  console.log('');

  // 8. 数据库配置提示
  log.header('数据库配置');
  console.log('部署完成后，请在 Supabase 中执行数据库脚本：');
  console.log('1. 访问 Supabase Dashboard > SQL Editor');
  console.log('2. 执行 database/production/01-schema.sql');
  console.log('3. 执行 database/production/02-seed.sql');
  console.log('');

  // 9. 验证部署
  log.header('验证部署');
  console.log('部署完成后，请测试以下功能：');
  console.log('✅ 用户注册/登录');
  console.log('✅ 创建提示词');
  console.log('✅ 搜索功能');
  console.log('✅ 分类管理');
  console.log('');

  log.success('部署指导完成！');
  console.log(`\n${colors.blue}详细部署文档：${colors.reset} docs/deployment/vercel-deployment.md`);
}

// 运行部署
deployToVercel().catch(console.error);
