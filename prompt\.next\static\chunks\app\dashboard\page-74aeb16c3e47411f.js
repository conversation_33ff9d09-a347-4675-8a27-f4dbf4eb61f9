(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{88298:(e,s,a)=>{"use strict";a.d(s,{K:()=>j});var t=a(95155),l=a(12115),r=a(99840),n=a(97168),c=a(89852),i=a(99474),o=a(82714),d=a(81704),m=a(53580),x=a(34938),h=a(11790),u=a(19987);let g=[{name:"folder",label:"文件夹"},{name:"code",label:"代码"},{name:"pen-to-square",label:"写作"},{name:"bullhorn",label:"营销"},{name:"rocket",label:"效率"},{name:"graduation-cap",label:"学习"},{name:"lightbulb",label:"创意"},{name:"cog",label:"工具"},{name:"heart",label:"收藏"},{name:"star",label:"重要"},{name:"fire",label:"热门"},{name:"gem",label:"精选"},{name:"bullseye",label:"目标"},{name:"flag",label:"标记"},{name:"bookmark",label:"书签"},{name:"database",label:"数据"},{name:"cloud",label:"云端"},{name:"mobile",label:"移动"},{name:"desktop",label:"桌面"},{name:"palette",label:"设计"}],p=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9","#3b82f6","#6366f1","#8b5cf6","#a855f7","#d946ef","#ec4899"];function j(e){let{category:s,isOpen:a,onClose:j,onSuccess:f}=e,[v,y]=(0,l.useState)(""),[N,w]=(0,l.useState)(""),[b,S]=(0,l.useState)("#6366f1"),[C,k]=(0,l.useState)("folder"),[E,I]=(0,l.useState)(""),[L,_]=(0,l.useState)(!1),[J,O]=(0,l.useState)(""),{toast:$}=(0,m.dj)(),z=!!s;(0,l.useEffect)(()=>{s?(y(s.name),w(s.description||""),S(s.color),k(s.icon),I("")):D()},[s]);let D=()=>{y(""),w(""),S("#6366f1"),k("folder"),I(""),O("")},F=async e=>{if(!e.trim())return O("分类名称不能为空"),!1;if(e.length>50)return O("分类名称不能超过50个字符"),!1;try{if(await (0,x.c1)(e.trim(),z?null==s?void 0:s.id:void 0))return O("分类名称已存在"),!1}catch(e){console.error("检查分类名称失败:",e)}return O(""),!0},R=async e=>{if(e.preventDefault(),!await F(v))return;let a=E&&(0,u.o1)(E)?E:b;try{_(!0);let e={name:v.trim(),description:N.trim()||void 0,color:a,icon:C};if(z&&s)if(console.log("\uD83D\uDE80 使用本地优先更新分类"),await h.J.updateCategory(s.id,e))$({title:"更新成功",description:"分类已成功更新"});else throw Error("更新失败");else{console.log("\uD83D\uDE80 使用本地优先创建分类");let s=await h.J.createCategory(e);if(s)$({title:"创建成功",description:"分类已成功创建"}),console.log("✅ 分类创建成功:",s);else throw Error("创建失败")}f(),j(),D()}catch(e){console.error("保存分类失败:",e),$({title:"保存失败",description:z?"更新分类时出现错误":"创建分类时出现错误",variant:"destructive"})}finally{_(!1)}};return(0,t.jsx)(r.lG,{open:a,onOpenChange:j,children:(0,t.jsxs)(r.Cf,{className:"max-w-md",children:[(0,t.jsxs)(r.c7,{children:[(0,t.jsx)(r.L3,{children:z?"编辑分类":"创建新分类"}),(0,t.jsx)(r.rr,{children:z?"修改分类的信息和外观":"创建一个新的提示词分类"})]}),(0,t.jsxs)("form",{onSubmit:R,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"name",children:"分类名称 *"}),(0,t.jsx)(c.p,{id:"name",value:v,onChange:e=>{y(e.target.value),O("")},onBlur:()=>F(v),placeholder:"输入分类名称",className:J?"border-red-500":"",required:!0}),J&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:J})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"description",children:"描述"}),(0,t.jsx)(i.T,{id:"description",value:N,onChange:e=>w(e.target.value),placeholder:"输入分类描述（可选）",className:"min-h-[80px]"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{children:"图标"}),(0,t.jsx)("div",{className:"grid grid-cols-5 gap-2",children:g.map(e=>(0,t.jsx)("button",{type:"button",className:"\n                    flex items-center justify-center w-10 h-10 rounded-lg border-2 transition-colors\n                    ".concat(C===e.name?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50","\n                  "),onClick:()=>k(e.name),title:e.label,children:(0,t.jsx)(d.Icon,{name:e.name,className:"h-5 w-5"})},e.name))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{children:"颜色"}),(0,t.jsx)("div",{className:"grid grid-cols-8 gap-2",children:p.map(e=>(0,t.jsx)("button",{type:"button",className:"\n                    w-8 h-8 rounded-lg border-2 transition-all\n                    ".concat(b===e?"border-gray-400 scale-110":"border-gray-200 hover:scale-105","\n                  "),style:{backgroundColor:e},onClick:()=>{S(e),I("")}},e))}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(c.p,{type:"text",value:E,onChange:e=>{var s;I(s=e.target.value),(0,u.o1)(s)&&S(s)},placeholder:"#6366f1",className:"flex-1"}),(0,t.jsx)("div",{className:"w-8 h-8 rounded border border-gray-200",style:{backgroundColor:b}})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{children:"预览"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 border rounded-lg bg-gray-50",children:[(0,t.jsx)(d.Icon,{name:C,className:"h-5 w-5",color:b}),(0,t.jsx)("span",{className:"font-medium",children:v||"分类名称"}),N&&(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:["- ",N]})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-4",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:j,children:"取消"}),(0,t.jsx)(n.$,{type:"submit",disabled:L||!!J,children:L?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),z?"更新中...":"创建中..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Icon,{name:"save",className:"h-4 w-4 mr-2"}),z?"更新":"创建"]})})]})]})]})})}},90860:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>O});var t=a(95155),l=a(12115),r=a(46414),n=a(97168),c=a(81704),i=a(88145),o=a(53999);function d(e){let{categories:s,selectedCategoryId:a,onCategorySelect:r,onCategoryCreate:d,onCategoryEdit:m,onCategoryDelete:x,isCollapsed:h=!1,onToggleCollapse:u,className:g}=e,[p,j]=(0,l.useState)(null),f=[...s].sort((e,s)=>e.sort_order-s.sort_order),v=s.reduce((e,s)=>e+(s.prompt_count||0),0);return(0,t.jsxs)("div",{className:(0,o.cn)("flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300",h?"w-16":"w-64","md:relative absolute md:translate-x-0 z-30",h?"md:w-16 w-0 -translate-x-full":"md:w-64 w-64 translate-x-0",g),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[!h&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"分类"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[v," 个提示词"]})]}),u&&(0,t.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:u,children:(0,t.jsx)(c.Icon,{name:h?"chevron-right":"chevron-left",className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"p-2",children:(0,t.jsxs)(n.$,{variant:void 0===a?"secondary":"ghost",className:(0,o.cn)("w-full justify-start gap-3 h-10",h&&"justify-center px-2"),onClick:()=>r(void 0),children:[(0,t.jsx)(c.Icon,{name:"home",className:"h-4 w-4 flex-shrink-0"}),!h&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"flex-1 text-left",children:"全部"}),(0,t.jsx)(i.E,{variant:"secondary",className:"ml-auto",children:v})]})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto p-2 space-y-1",children:f.map(e=>(0,t.jsxs)("div",{className:"relative group",onMouseEnter:()=>j(e.id),onMouseLeave:()=>j(null),children:[(0,t.jsxs)(n.$,{variant:a===e.id?"secondary":"ghost",className:(0,o.cn)("w-full justify-start gap-3 h-10 relative",h&&"justify-center px-2"),onClick:()=>r(e.id),children:[(0,t.jsx)(c.Icon,{name:e.icon||"folder",className:"h-4 w-4 flex-shrink-0",color:e.color}),!h&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"flex-1 text-left truncate",children:e.name}),(0,t.jsx)(i.E,{variant:"secondary",className:(0,o.cn)("ml-auto transition-opacity duration-200",p===e.id?"opacity-0":"opacity-100"),style:{backgroundColor:"".concat(e.color,"20"),color:e.color},children:e.prompt_count||0})]})]}),!h&&p===e.id&&(0,t.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1 bg-white rounded shadow-sm border",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-6 w-6 hover:bg-blue-100 hover:text-blue-600",onClick:s=>{s.stopPropagation(),m(e.id)},children:(0,t.jsx)(c.Icon,{name:"edit",className:"h-3 w-3"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-6 w-6 hover:bg-red-100 hover:text-red-600",onClick:s=>{s.stopPropagation(),x(e.id)},children:(0,t.jsx)(c.Icon,{name:"trash",className:"h-3 w-3"})})]}),h&&(0,t.jsxs)("div",{className:"absolute left-full top-0 ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50",children:[e.name," (",e.prompt_count||0,")"]})]},e.id))}),(0,t.jsx)("div",{className:"p-2 border-t border-gray-200",children:(0,t.jsxs)(n.$,{variant:"outline",className:(0,o.cn)("w-full gap-2",h&&"justify-center px-2"),onClick:d,children:[(0,t.jsx)(c.Icon,{name:"plus",className:"h-4 w-4"}),!h&&"新建分类"]})})]})}var m=a(319),x=a(33208),h=a(92669),u=a(88482),g=a(19987);function p(e){let{className:s}=e,[a,r]=(0,l.useState)([]),[o,d]=(0,l.useState)(!0);(0,l.useEffect)(()=>{m()},[]);let m=()=>{try{d(!0);let e=localStorage.getItem("prompt-copy-history");if(e){let s=JSON.parse(e);r(s.slice(0,20))}}catch(e){console.error("加载复制历史失败:",e)}finally{d(!1)}},x=(e,s,t,l)=>{let n=[{id:Date.now().toString(),promptId:e,promptTitle:s,content:t,copiedAt:new Date().toISOString(),category:l},...a].slice(0,20);r(n);try{localStorage.setItem("prompt-copy-history",JSON.stringify(n))}catch(e){console.error("保存复制历史失败:",e)}},h=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("复制失败:",e)}};return((0,l.useEffect)(()=>(window.addCopyRecord=x,()=>{delete window.addCopyRecord}),[a]),o)?(0,t.jsxs)(u.Zp,{className:s,children:[(0,t.jsx)(u.aR,{children:(0,t.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(c.Icon,{name:"clipboard",className:"h-5 w-5"}),"复制历史"]})}),(0,t.jsx)(u.Wu,{children:(0,t.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]},s))})})]}):(0,t.jsxs)(u.Zp,{className:s,children:[(0,t.jsx)(u.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(c.Icon,{name:"clipboard",className:"h-5 w-5"}),"复制历史"]}),(0,t.jsx)(u.BT,{children:"最近复制的提示词记录"})]}),a.length>0&&(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{r([]);try{localStorage.removeItem("prompt-copy-history")}catch(e){console.error("清除复制历史失败:",e)}},children:[(0,t.jsx)(c.Icon,{name:"trash",className:"h-4 w-4 mr-2"}),"清除"]})]})}),(0,t.jsx)(u.Wu,{children:a.length>0?(0,t.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:a.map(e=>(0,t.jsx)("div",{className:"group p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:(0,t.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("h4",{className:"font-medium text-sm truncate",children:e.promptTitle}),e.category&&(0,t.jsx)(i.E,{variant:"secondary",className:"text-xs",style:{backgroundColor:"".concat(e.category.color,"20"),color:e.category.color},children:e.category.name})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mb-2",children:(0,g.EJ)(e.content,100)}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,g.fw)(e.copiedAt)})]}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity",onClick:()=>h(e.content),children:(0,t.jsx)(c.Icon,{name:"copy",className:"h-4 w-4"})})]})},e.id))}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(c.Icon,{name:"clipboard",className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"暂无复制记录"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"复制提示词后会在这里显示历史记录"})]})})]})}var j=a(20728),f=a(26724),v=a(57462),y=a(88298),N=a(53580);function w(e){let{className:s,children:a,...l}=e;return(0,t.jsx)("div",{className:(0,o.cn)("animate-pulse rounded-md bg-gray-200 dark:bg-gray-700",s),...l,children:a})}function b(){return(0,t.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsx)(w,{className:"h-6 w-3/4"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(w,{className:"h-4 w-4 rounded-full"}),(0,t.jsx)(w,{className:"h-4 w-16"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(w,{className:"h-8 w-8 rounded"}),(0,t.jsx)(w,{className:"h-8 w-8 rounded"}),(0,t.jsx)(w,{className:"h-8 w-8 rounded"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w,{className:"h-4 w-full"}),(0,t.jsx)(w,{className:"h-4 w-5/6"}),(0,t.jsx)(w,{className:"h-4 w-4/5"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(w,{className:"h-4 w-4"}),(0,t.jsx)(w,{className:"h-4 w-12"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(w,{className:"h-4 w-4"}),(0,t.jsx)(w,{className:"h-4 w-16"})]})]}),(0,t.jsx)(w,{className:"h-8 w-16 rounded"})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(w,{className:"h-6 w-12 rounded-full"}),(0,t.jsx)(w,{className:"h-6 w-16 rounded-full"})]})]})}function S(){return(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-200",children:[(0,t.jsx)(w,{className:"h-8 w-8 rounded"}),(0,t.jsx)("div",{className:"flex-1 space-y-1",children:(0,t.jsx)(w,{className:"h-4 w-20"})}),(0,t.jsx)(w,{className:"h-5 w-8 rounded-full"})]})}function C(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w,{className:"h-6 w-16"}),(0,t.jsx)(w,{className:"h-4 w-24"})]}),(0,t.jsx)(w,{className:"h-8 w-8 rounded"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S,{}),Array.from({length:4}).map((e,s)=>(0,t.jsx)(S,{},s))]}),(0,t.jsx)(w,{className:"h-10 w-full rounded"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(w,{className:"h-4 w-4"}),(0,t.jsx)(w,{className:"h-10 flex-1 rounded"}),(0,t.jsx)(w,{className:"h-10 w-20 rounded"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w,{className:"h-6 w-32"}),(0,t.jsx)(w,{className:"h-4 w-24"})]}),(0,t.jsx)("div",{className:"grid gap-4",children:Array.from({length:4}).map((e,s)=>(0,t.jsx)(b,{},s))})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(w,{className:"h-4 w-4"}),(0,t.jsx)(w,{className:"h-5 w-20"})]}),(0,t.jsx)(w,{className:"h-4 w-40"})]}),(0,t.jsx)(w,{className:"h-8 w-16 rounded"})]}),(0,t.jsx)("div",{className:"space-y-3",children:Array.from({length:2}).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)(w,{className:"h-4 w-32"}),(0,t.jsx)(w,{className:"h-3 w-full"}),(0,t.jsx)(w,{className:"h-3 w-24"})]}),(0,t.jsx)(w,{className:"h-6 w-6 rounded"})]},s))})]})]})}function k(e){let{size:s="md"}=e;return(0,t.jsx)("div",{className:"flex items-center justify-center",children:(0,t.jsx)("div",{className:(0,o.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[s])})})}function E(){return(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)(k,{size:"lg"}),(0,t.jsx)("p",{className:"text-gray-500",children:"加载中..."})]})})}var I=a(11790);function L(){let[e,s]=(0,l.useState)({pending:0,lastSync:null}),[a,r]=(0,l.useState)(!0),[i,o]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=()=>{s(I.J.getSyncStatus())};e();let a=setInterval(e,5e3),t=()=>r(!0),l=()=>r(!1);return window.addEventListener("online",t),window.addEventListener("offline",l),()=>{clearInterval(a),window.removeEventListener("online",t),window.removeEventListener("offline",l)}},[]);let d=async()=>{o(!0);try{await I.J.manualSync(),s(I.J.getSyncStatus())}catch(e){console.error("手动同步失败:",e)}finally{o(!1)}};return(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1 ".concat(a?i?"text-blue-500":e.pending>0?"text-orange-500":"text-green-500":"text-gray-500"),children:[(0,t.jsx)(c.Icon,{name:a?i?"spinner":e.pending>0?"clock":"check-circle":"wifi-off",className:"h-4 w-4 ".concat(i?"animate-spin":"")}),(0,t.jsx)("span",{children:a?i?"同步中...":e.pending>0?"".concat(e.pending," 项待同步"):"已同步":"离线模式"})]}),e.pending>0&&a&&(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:d,disabled:i,className:"h-6 px-2 text-xs",children:"立即同步"}),(0,t.jsx)("div",{className:"hidden md:block text-xs text-gray-500",children:(()=>{if(!e.lastSync)return"从未同步";let s=Math.floor((Date.now()-e.lastSync)/6e4);if(s<1)return"刚刚同步";if(s<60)return"".concat(s," 分钟前");let a=Math.floor(s/60);return"".concat(a," 小时前")})()})]})}function _(){let[e,s]=(0,l.useState)(!0);return((0,l.useEffect)(()=>{let e=()=>s(!0),a=()=>s(!1);return s(navigator.onLine),window.addEventListener("online",e),window.addEventListener("offline",a),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",a)}},[]),e)?null:(0,t.jsx)("div",{className:"bg-orange-100 border-l-4 border-orange-500 p-4 mb-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c.Icon,{name:"wifi-off",className:"h-5 w-5 text-orange-500 mr-3"}),(0,t.jsx)("div",{children:(0,t.jsxs)("p",{className:"text-sm text-orange-700",children:[(0,t.jsx)("strong",{children:"离线模式"})," - 您的更改将保存在本地，网络恢复后自动同步"]})})]})})}var J=a(34938);function O(){var e;let[s,a]=(0,l.useState)([]),[i,o]=(0,l.useState)([]),[u,g]=(0,l.useState)([]),[w,b]=(0,l.useState)([]),[S,k]=(0,l.useState)(),[O,$]=(0,l.useState)(""),[z,D]=(0,l.useState)(!0),[F,R]=(0,l.useState)(!1),[A,M]=(0,l.useState)(!1),[T,B]=(0,l.useState)(!1),[P,Z]=(0,l.useState)(1),[q,W]=(0,l.useState)(!1),[G,H]=(0,l.useState)(0),[K,Q]=(0,l.useState)(!0),[X,U]=(0,l.useState)(null),[V,Y]=(0,l.useState)(!1),[ee,es]=(0,l.useState)(!1),[ea,et]=(0,l.useState)(null),[el,er]=(0,l.useState)(!1),[en,ec]=(0,l.useState)(null),[ei,eo]=(0,l.useState)(!1),[ed,em]=(0,l.useState)(!1),[ex,eh]=(0,l.useState)(null),[eu,eg]=(0,l.useState)(!1),[ep,ej]=(0,l.useState)(null),[ef,ev]=(0,l.useState)(!1),[ey,eN]=(0,l.useState)(400),[ew,eb]=(0,l.useState)(!1),{toast:eS}=(0,N.dj)(),{addCopyRecord:eC}={addCopyRecord:(e,s,a,t)=>{window.addCopyRecord&&window.addCopyRecord(e,s,a,t)}};(0,r.U)(),(0,l.useEffect)(()=>{eE()},[]);let ek=(0,l.useCallback)((e,s)=>{let t=i;if(s&&(t=t.filter(e=>e.category_id===s)),e.trim()){let s=e.toLowerCase();t=t.filter(e=>{var a;return e.title.toLowerCase().includes(s)||e.content.toLowerCase().includes(s)||(null==(a=e.tags)?void 0:a.some(e=>e.name.toLowerCase().includes(s)))})}t.sort((e,s)=>new Date(s.updated_at).getTime()-new Date(e.updated_at).getTime()),a(t),H(t.length),W(!1)},[i]);(0,l.useEffect)(()=>{K&&i.length>0?ek(O,S):eL()},[S,O,P,K,i.length,ek]);let eE=async()=>{try{D(!0),console.log("\uD83D\uDE80 开始初始化应用 - 本地优先模式");let{prompts:e,categories:s}=I.J.getLocalDataOnly();e.length>0&&(o(e),a(e.slice(0,12)),H(e.length),W(e.length>12),console.log("✅ 立即显示 ".concat(e.length," 个本地提示词"))),s.length>0&&(g(s),console.log("✅ 立即显示 ".concat(s.length," 个本地分类"))),D(!1),console.log("✅ 本地数据加载完成，用户可以开始使用"),setTimeout(async()=>{try{console.log("\uD83D\uDD04 开始后台初始化和同步"),await (0,J.Yv)(),await Promise.all([e_(),eJ(),eI()]),console.log("✅ 后台同步完成")}catch(e){console.error("后台同步失败:",e)}},100)}catch(e){console.error("初始化应用失败:",e),eS({title:"初始化失败",description:"应用初始化时出现错误，请刷新页面重试",variant:"destructive"})}finally{D(!1)}},eI=async()=>{try{console.log("\uD83D\uDE80 使用本地优先存储加载提示词");let e=await I.J.getPrompts();if(o(e),e.length>0){a(e.slice(0,12)),H(e.length),W(e.length>12),console.log("✅ 立即显示 ".concat(e.length," 个提示词"));return}console.log("\uD83D\uDCE1 本地无数据，使用远程加载");let s=await (0,J.oO)({sortBy:"updated_at",sortOrder:"desc",limit:1e3});o(s.data),O||S||(a(s.data.slice(0,12)),H(s.data.length),W(s.data.length>12))}catch(e){console.error("加载所有提示词失败:",e),Q(!1)}},eL=async()=>{try{R(!0);let e=await (0,J.oO)({query:O||void 0,categoryId:S,sortBy:"updated_at",sortOrder:"desc",limit:12,offset:(P-1)*12});1===P?a(e.data):a(s=>[...s,...e.data]),W(e.hasMore),H(e.total)}catch(e){console.error("加载提示词失败:",e),eS({title:"加载失败",description:"无法加载提示词列表",variant:"destructive"})}finally{R(!1)}},e_=async()=>{try{M(!0),console.log("\uD83D\uDE80 使用本地优先存储加载分类");let e=await I.J.getCategories();g(e),console.log("✅ 加载了 ".concat(e.length," 个分类"))}catch(e){console.error("加载分类失败:",e)}finally{M(!1)}},eJ=async()=>{try{let e=await (0,J.FA)(10);b(e)}catch(e){console.error("加载搜索历史失败:",e)}},eO=(0,l.useCallback)(async e=>{if($(e),Z(1),e.trim())try{await (0,J.eQ)(e.trim()),eJ()}catch(e){console.error("添加搜索历史失败:",e)}},[]),e$=(0,l.useCallback)(async e=>{try{if(Q(!1),$(e),Z(1),e.trim())try{await (0,J.eQ)(e.trim()),eJ()}catch(e){console.error("添加搜索历史失败:",e)}}catch(e){console.error("远程搜索失败:",e)}},[]),ez=async(e,a)=>{try{await (0,J.pB)(a);let t=s.find(e=>e.id===a);t&&eC(a,t.title,e,t.category?{name:t.category.name,color:t.category.color}:void 0),await eL()}catch(e){console.error("更新使用次数失败:",e)}},eD=async e=>{try{let s=await (0,J.Fw)(e);s&&(U(s),Y(!0))}catch(e){console.error("获取提示词详情失败:",e),eS({title:"加载失败",description:"无法获取提示词详情",variant:"destructive"})}},eF=async e=>{try{let s=await (0,J.Fw)(e);s&&(et(s),es(!0))}catch(e){console.error("获取提示词详情失败:",e),eS({title:"加载失败",description:"无法获取提示词详情",variant:"destructive"})}},eR=async e=>{let a=s.find(s=>s.id===e);a&&(ec(a),er(!0))},eA=()=>{et(null),es(!0)},eM=async()=>{if(en)try{if(eo(!0),console.log("\uD83D\uDE80 使用本地优先删除提示词"),await I.J.deletePrompt(en.id))o(e=>e.filter(e=>e.id!==en.id)),a(e=>e.filter(e=>e.id!==en.id)),eS({title:"删除成功",description:"提示词已成功删除"});else throw Error("删除失败");er(!1),ec(null),await eL(),await e_()}catch(e){console.error("删除提示词失败:",e),eS({title:"删除失败",description:"删除提示词时出现错误",variant:"destructive"})}finally{eo(!1)}},eT=(0,l.useCallback)(async()=>{try{await (0,J.Wf)(),b([]),eS({title:"清除成功",description:"搜索历史已清除"})}catch(e){console.error("清除搜索历史失败:",e),eS({title:"清除失败",description:"清除搜索历史时出现错误",variant:"destructive"})}},[eS]),eB=async()=>{if(ep)try{if(ev(!0),console.log("\uD83D\uDE80 使用本地优先删除分类（首页）"),await I.J.deleteCategory(ep.id)){g(e=>e.filter(e=>e.id!==ep.id)),eS({title:"删除成功",description:"分类已成功删除"}),eg(!1),ej(null),console.log("✅ 分类已从首页本地状态中移除");let e=I.J.getDetailedSyncStatus();console.log("\uD83D\uDD0D 首页删除后同步状态:",e)}else throw Error("删除失败")}catch(e){console.error("删除分类失败:",e),eS({title:"删除失败",description:"删除分类时出现错误",variant:"destructive"})}finally{ev(!1)}};return z?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(h.a,{}),(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"w-80 bg-white border-r border-gray-200 p-6",children:(0,t.jsx)(C,{})}),(0,t.jsx)("div",{className:"flex-1 p-6",children:(0,t.jsx)(E,{})})]})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,t.jsx)(h.a,{onCreatePrompt:eA,children:(0,t.jsx)(L,{})}),(0,t.jsx)(_,{}),(0,t.jsxs)("div",{className:"flex h-[calc(100vh-64px)] relative",children:[!T&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden",onClick:()=>B(!0)}),(0,t.jsx)(d,{categories:u,selectedCategoryId:S,onCategorySelect:e=>{k(e),Z(1)},onCategoryCreate:()=>{eh(null),em(!0)},onCategoryEdit:e=>{let s=u.find(s=>s.id===e);s&&(eh(s),em(!0))},onCategoryDelete:e=>{let s=u.find(s=>s.id===e);s&&(ej(s),eg(!0))},isCollapsed:T,onToggleCollapse:()=>B(!T)}),(0,t.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>B(!1),children:(0,t.jsx)(c.Icon,{name:"bars",className:"h-5 w-5"})}),(0,t.jsx)("div",{className:"flex-1 max-w-2xl",children:(0,t.jsx)(m.I,{value:O,onChange:$,onSearch:eO,onRemoteSearch:e$,searchHistory:w,onClearHistory:eT,showRemoteSearch:K&&O.trim().length>0})})]})}),(0,t.jsxs)("main",{className:"flex-1 overflow-y-auto p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:S?(null==(e=u.find(e=>e.id===S))?void 0:e.name)||"分类":"全部提示词"}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("p",{className:"text-muted-foreground",children:["共 ",G," 个提示词",O&&' \xb7 搜索 "'.concat(O,'"')]}),O&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:K?(0,t.jsx)("span",{className:"text-xs bg-green-100 text-green-700 px-2 py-1 rounded",children:"本地搜索"}):(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded",children:"在线搜索"}),(0,t.jsx)("button",{onClick:()=>Q(!0),className:"text-xs text-blue-600 hover:text-blue-800 underline",children:"切换到本地搜索"})]})})]})]}),s.length>0?(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-4 md:gap-6",children:s.map(e=>(0,t.jsx)(x.z,{id:e.id,title:e.title,description:e.description||void 0,content:e.content,category:e.category,tags:e.tags,usageCount:e.usage_count,createdAt:e.created_at,updatedAt:e.updated_at,isLocal:e._isLocal,onView:eD,onEdit:eF,onDelete:eR,onCopy:ez},e.id))}):(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(c.Icon,{name:"search",className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:O?"未找到匹配的提示词":"暂无提示词"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:O?"尝试调整搜索关键词或清除筛选条件":"开始创建您的第一个提示词吧"}),(0,t.jsxs)(n.$,{onClick:()=>{O?$(""):eA()},children:[(0,t.jsx)(c.Icon,{name:"plus",className:"h-4 w-4 mr-2"}),O?"清除搜索":"新建提示词"]})]}),q&&(0,t.jsx)("div",{className:"text-center mt-8",children:(0,t.jsx)(n.$,{variant:"outline",onClick:()=>{q&&!z&&Z(e=>e+1)},disabled:z,children:z?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.Icon,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),"加载中..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.Icon,{name:"refresh",className:"h-4 w-4 mr-2"}),"加载更多"]})})})]})]}),(0,t.jsxs)("div",{className:"hidden xl:flex relative border-l border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",style:{width:ey},children:[(0,t.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-2 cursor-col-resize hover:bg-blue-400 transition-all duration-200 group ".concat(ew?"bg-blue-500 w-3":"bg-gray-300 hover:bg-blue-400"),onMouseDown:e=>{eb(!0),e.preventDefault();let s=e.clientX,a=e=>{eN(Math.max(300,Math.min(600,ey+(s-e.clientX))))},t=()=>{eb(!1),document.removeEventListener("mousemove",a),document.removeEventListener("mouseup",t)};document.addEventListener("mousemove",a),document.addEventListener("mouseup",t)},title:"拖拽调整宽度",children:(0,t.jsx)("div",{className:"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-8 bg-white opacity-60 group-hover:opacity-100 transition-opacity"})}),(0,t.jsx)("div",{className:"flex-1 p-4 overflow-y-auto",children:(0,t.jsx)(p,{})})]})]})]}),(0,t.jsx)(j.n,{prompt:X,isOpen:V,onClose:()=>{Y(!1),U(null)},onEdit:eF,onDelete:eR,onCopy:ez}),(0,t.jsx)(f.G,{prompt:ea,isOpen:ee,onClose:()=>{es(!1),et(null)},onSuccess:e=>{es(!1),et(null),e?(o(s=>[e,...s]),a(s=>[e,...s.slice(0,11)]),H(e=>e+1),console.log("✅ 新提示词已添加到本地状态")):eI(),e_()}}),(0,t.jsx)(v.o,{isOpen:el,onClose:()=>{er(!1),ec(null)},onConfirm:eM,title:"删除提示词",description:"此操作无法撤销，确定要删除这个提示词吗？",itemName:null==en?void 0:en.title,isLoading:ei}),(0,t.jsx)(y.K,{category:ex,isOpen:ed,onClose:()=>{em(!1),eh(null)},onSuccess:()=>{e_()}}),(0,t.jsx)(v.o,{isOpen:eu,onClose:()=>{eg(!1),ej(null)},onConfirm:eB,title:"删除分类",description:"此操作无法撤销，确定要删除这个分类吗？",itemName:null==ep?void 0:ep.name,isLoading:ef})]})}},96309:(e,s,a)=>{Promise.resolve().then(a.bind(a,90860))}},e=>{e.O(0,[266,352,865,874,576,561,949,505,802,938,647,194,441,964,358],()=>e(e.s=96309)),_N_E=e.O()}]);