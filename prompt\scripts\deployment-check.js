#!/usr/bin/env node

/**
 * 自动化部署前检查脚本
 * 自动检查大部分部署前检查清单项目
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  header: (msg) => console.log(`\n${colors.bold}${colors.blue}🔍 ${msg}${colors.reset}\n`)
};

class DeploymentChecker {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      total: 0
    };
  }

  check(description, testFn) {
    this.results.total++;
    try {
      const result = testFn();
      if (result === true) {
        log.success(description);
        this.results.passed++;
      } else if (result === 'warning') {
        log.warning(description);
        this.results.warnings++;
      } else {
        log.error(description);
        this.results.failed++;
      }
    } catch (error) {
      log.error(`${description} - ${error.message}`);
      this.results.failed++;
    }
  }

  // 检查文件是否存在
  fileExists(filePath) {
    return fs.existsSync(filePath);
  }

  // 检查目录是否存在
  dirExists(dirPath) {
    return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  }

  // 运行命令并检查结果
  runCommand(command, silent = true) {
    try {
      const result = execSync(command, { 
        encoding: 'utf8', 
        stdio: silent ? 'pipe' : 'inherit' 
      });
      return { success: true, output: result };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // 检查 package.json 配置
  checkPackageJson() {
    log.header('检查 package.json 配置');
    
    this.check('package.json 文件存在', () => this.fileExists('package.json'));
    
    if (this.fileExists('package.json')) {
      const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      this.check('包含 build 脚本', () => pkg.scripts && pkg.scripts.build);
      this.check('包含 start 脚本', () => pkg.scripts && pkg.scripts.start);
      this.check('包含 dev 脚本', () => pkg.scripts && pkg.scripts.dev);
      this.check('包含 lint 脚本', () => pkg.scripts && pkg.scripts.lint);
      
      // 检查关键依赖
      const deps = { ...pkg.dependencies, ...pkg.devDependencies };
      this.check('Next.js 依赖存在', () => deps.next);
      this.check('React 依赖存在', () => deps.react);
      this.check('TypeScript 依赖存在', () => deps.typescript);
      this.check('Supabase 依赖存在', () => deps['@supabase/supabase-js']);
    }
  }

  // 检查必需文件
  checkRequiredFiles() {
    log.header('检查必需文件');
    
    const requiredFiles = [
      'next.config.ts',
      'tailwind.config.ts',
      'tsconfig.json',
      'vercel.json',
      '.env.example',
      'README.md',
      'DEPLOYMENT_CHECKLIST.md'
    ];

    requiredFiles.forEach(file => {
      this.check(`${file} 存在`, () => this.fileExists(file));
    });
  }

  // 检查目录结构
  checkDirectoryStructure() {
    log.header('检查目录结构');
    
    const requiredDirs = [
      'app',
      'components',
      'lib',
      'types',
      'docs',
      'database/production'
    ];

    requiredDirs.forEach(dir => {
      this.check(`${dir}/ 目录存在`, () => this.dirExists(dir));
    });
  }

  // 检查环境变量
  checkEnvironmentVariables() {
    log.header('检查环境变量配置');
    
    this.check('.env.example 存在', () => this.fileExists('.env.example'));
    
    if (this.fileExists('.env.example')) {
      const envExample = fs.readFileSync('.env.example', 'utf8');
      this.check('包含 SUPABASE_URL', () => envExample.includes('NEXT_PUBLIC_SUPABASE_URL'));
      this.check('包含 SUPABASE_ANON_KEY', () => envExample.includes('NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY'));
    }

    // 检查是否有 .env.local（不应该提交）
    this.check('.env.local 未提交到仓库', () => !this.fileExists('.env.local') || 'warning');
  }

  // 检查数据库脚本
  checkDatabaseScripts() {
    log.header('检查数据库脚本');
    
    this.check('database/production/ 目录存在', () => this.dirExists('database/production'));
    this.check('01-schema.sql 存在', () => this.fileExists('database/production/01-schema.sql'));
    this.check('02-seed.sql 存在', () => this.fileExists('database/production/02-seed.sql'));
    
    // 检查脚本内容
    if (this.fileExists('database/production/01-schema.sql')) {
      const schema = fs.readFileSync('database/production/01-schema.sql', 'utf8');
      this.check('包含 RLS 策略', () => schema.includes('ROW LEVEL SECURITY'));
      this.check('包含索引定义', () => schema.includes('CREATE INDEX'));
      this.check('包含触发器', () => schema.includes('CREATE TRIGGER'));
    }
  }

  // 检查文档
  checkDocumentation() {
    log.header('检查文档');
    
    const docFiles = [
      'docs/README.md',
      'docs/user/getting-started.md',
      'docs/technical/architecture.md',
      'docs/development/setup.md',
      'docs/deployment/vercel-deployment.md'
    ];

    docFiles.forEach(file => {
      this.check(`${file} 存在`, () => this.fileExists(file));
    });
  }

  // 检查代码质量
  checkCodeQuality() {
    log.header('检查代码质量');
    
    // TypeScript 检查
    this.check('TypeScript 编译检查', () => {
      const result = this.runCommand('npx tsc --noEmit');
      return result.success;
    });

    // ESLint 检查
    this.check('ESLint 检查', () => {
      const result = this.runCommand('npm run lint');
      return result.success;
    });

    // 构建检查
    this.check('项目构建检查', () => {
      log.info('正在执行构建检查，这可能需要几分钟...');
      const result = this.runCommand('npm run build', false);
      return result.success;
    });
  }

  // 检查 Git 状态
  checkGitStatus() {
    log.header('检查 Git 状态');
    
    this.check('Git 仓库已初始化', () => this.dirExists('.git'));
    
    if (this.dirExists('.git')) {
      this.check('没有未提交的更改', () => {
        const result = this.runCommand('git status --porcelain');
        return result.success && result.output.trim() === '';
      });
      
      this.check('.gitignore 存在', () => this.fileExists('.gitignore'));
      
      if (this.fileExists('.gitignore')) {
        const gitignore = fs.readFileSync('.gitignore', 'utf8');
        this.check('.gitignore 包含 node_modules', () => gitignore.includes('node_modules'));
        this.check('.gitignore 包含 .env.local', () => gitignore.includes('.env.local'));
        this.check('.gitignore 包含 .next', () => gitignore.includes('.next'));
      }
    }
  }

  // 运行所有检查
  async runAllChecks() {
    console.log(`${colors.bold}${colors.blue}🚀 开始部署前自动化检查...${colors.reset}\n`);
    
    this.checkPackageJson();
    this.checkRequiredFiles();
    this.checkDirectoryStructure();
    this.checkEnvironmentVariables();
    this.checkDatabaseScripts();
    this.checkDocumentation();
    this.checkGitStatus();
    this.checkCodeQuality();
    
    this.printSummary();
  }

  // 打印检查结果摘要
  printSummary() {
    console.log(`\n${colors.bold}📊 检查结果摘要${colors.reset}`);
    console.log('='.repeat(50));
    
    log.success(`通过: ${this.results.passed}/${this.results.total}`);
    if (this.results.warnings > 0) {
      log.warning(`警告: ${this.results.warnings}`);
    }
    if (this.results.failed > 0) {
      log.error(`失败: ${this.results.failed}`);
    }
    
    const percentage = Math.round((this.results.passed / this.results.total) * 100);
    console.log(`\n${colors.bold}总体完成度: ${percentage}%${colors.reset}`);
    
    if (this.results.failed === 0) {
      console.log(`\n${colors.green}${colors.bold}🎉 恭喜！项目已准备好部署到 Vercel！${colors.reset}`);
      console.log(`\n${colors.blue}下一步：${colors.reset}`);
      console.log('1. 确保 Supabase 项目已创建并配置');
      console.log('2. 在 Vercel 中设置环境变量');
      console.log('3. 执行数据库脚本');
      console.log('4. 部署到 Vercel');
    } else {
      console.log(`\n${colors.red}${colors.bold}⚠️  请修复上述问题后再进行部署${colors.reset}`);
    }
  }
}

// 运行检查
if (require.main === module) {
  const checker = new DeploymentChecker();
  checker.runAllChecks().catch(console.error);
}

module.exports = DeploymentChecker;
