"use client"

import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@/components/ui/icon'
import { formatDate } from '@/lib/utils/format'
import type { CategoryWithCount } from '@/types/database'

interface SortableCategoryItemProps {
  category: CategoryWithCount
  onEdit: (category: CategoryWithCount) => void
  onDelete: (category: CategoryWithCount) => void
}

export function SortableCategoryItem({
  category,
  onEdit,
  onDelete
}: SortableCategoryItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: category.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        group relative bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 
        rounded-lg p-4 transition-all duration-200
        ${isDragging 
          ? 'shadow-lg scale-105 z-10' 
          : 'hover:shadow-md hover:border-gray-300 dark:hover:border-gray-500'
        }
      `}
    >
      <div className="flex items-center gap-4">
        {/* 拖拽手柄 */}
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600"
        >
          <Icon name="bars" className="h-4 w-4 text-gray-400" />
        </div>

        {/* 分类图标和颜色 */}
        <div 
          className="flex items-center justify-center w-10 h-10 rounded-lg"
          style={{ backgroundColor: `${category.color}20` }}
        >
          <Icon
            name={(category.icon || 'folder') as any}
            className="h-5 w-5"
            color={category.color}
          />
        </div>

        {/* 分类信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-medium text-gray-900 dark:text-white truncate">
              {category.name}
            </h3>
            <Badge 
              variant="secondary"
              style={{ 
                backgroundColor: `${category.color}20`,
                color: category.color 
              }}
            >
              {category.prompt_count} 个提示词
            </Badge>
          </div>
          
          {category.description && (
            <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
              {category.description}
            </p>
          )}
          
          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
            <span>排序: {category.sort_order}</span>
            <span>创建于 {formatDate(category.created_at)}</span>
            {category.updated_at !== category.created_at && (
              <span>更新于 {formatDate(category.updated_at)}</span>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(category)}
            className="hover:bg-blue-100 hover:text-blue-600"
          >
            <Icon name="edit" className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(category)}
            className="hover:bg-red-100 hover:text-red-600"
            disabled={category.prompt_count > 0}
          >
            <Icon name="trash" className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 拖拽时的覆盖层 */}
      {isDragging && (
        <div className="absolute inset-0 bg-blue-100 dark:bg-blue-900/20 rounded-lg border-2 border-blue-300 dark:border-blue-600" />
      )}
    </div>
  )
}
