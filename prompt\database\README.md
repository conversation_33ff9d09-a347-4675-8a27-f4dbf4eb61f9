# 数据库设计文档

## 概述
提示词管理工具的数据库设计，基于PostgreSQL和Supabase，支持多用户、分类管理、标签系统、搜索功能等。

## ⚠️ 重要说明

### 中文全文搜索问题
原始的 `schema.sql` 使用了 `'chinese'` 全文搜索配置，但 Supabase 的 PostgreSQL 默认没有安装中文分词扩展，会导致以下错误：

```
ERROR: 42704: text search configuration "chinese" does not exist
```

### 解决方案
我们提供了两个版本的 schema 文件：

1. **schema.sql** - 修复版，使用 `'english'` 配置（推荐）
2. **schema_fixed.sql** - 完整修复版，包含额外的函数和优化

### 部署步骤
1. 在 Supabase SQL Editor 中执行 `schema.sql`
2. 执行 `seed.sql` 创建初始数据
3. 在应用中调用 `create_default_categories_for_user()` 函数为新用户创建默认数据

## 表结构

### 1. categories (分类表)
存储提示词的分类信息。

| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| id | UUID | 主键 | PRIMARY KEY |
| name | VARCHAR(100) | 分类名称 | NOT NULL |
| description | VARCHAR(500) | 分类描述 | - |
| color | VARCHAR(7) | 颜色代码 | 默认 '#6366f1' |
| icon | VARCHAR(50) | 图标名称 | 默认 'folder' |
| sort_order | INTEGER | 排序顺序 | 默认 0 |
| user_id | UUID | 用户ID | 外键，级联删除 |
| created_at | TIMESTAMPTZ | 创建时间 | 默认 NOW() |
| updated_at | TIMESTAMPTZ | 更新时间 | 自动更新 |
| deleted_at | TIMESTAMPTZ | 软删除时间 | - |

### 2. tags (标签表)
存储提示词的标签信息。

| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| id | UUID | 主键 | PRIMARY KEY |
| name | VARCHAR(50) | 标签名称 | NOT NULL |
| color | VARCHAR(7) | 颜色代码 | 默认 '#10b981' |
| user_id | UUID | 用户ID | 外键，级联删除 |
| created_at | TIMESTAMPTZ | 创建时间 | 默认 NOW() |

### 3. prompts (提示词表)
存储提示词的主要信息。

| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| id | UUID | 主键 | PRIMARY KEY |
| title | VARCHAR(200) | 标题 | NOT NULL |
| description | VARCHAR(500) | 简短描述 | - |
| content | TEXT | 提示词内容 | NOT NULL |
| category_id | UUID | 分类ID | 外键，可为空 |
| usage_count | INTEGER | 使用次数 | 默认 0，非负 |
| user_id | UUID | 用户ID | 外键，级联删除 |
| metadata | JSONB | 扩展字段 | 默认 '{}' |
| created_at | TIMESTAMPTZ | 创建时间 | 默认 NOW() |
| updated_at | TIMESTAMPTZ | 更新时间 | 自动更新 |
| deleted_at | TIMESTAMPTZ | 软删除时间 | - |

### 4. prompt_tags (提示词标签关联表)
多对多关系表，连接提示词和标签。

| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| prompt_id | UUID | 提示词ID | 外键，级联删除 |
| tag_id | UUID | 标签ID | 外键，级联删除 |
| created_at | TIMESTAMPTZ | 创建时间 | 默认 NOW() |

### 5. search_history (搜索历史表)
存储用户的搜索历史记录。

| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| id | UUID | 主键 | PRIMARY KEY |
| search_term | VARCHAR(200) | 搜索词 | NOT NULL |
| search_count | INTEGER | 搜索次数 | 默认 1 |
| user_id | UUID | 用户ID | 外键，级联删除 |
| last_searched_at | TIMESTAMPTZ | 最后搜索时间 | 默认 NOW() |

### 6. user_preferences (用户偏好表)
存储用户的个性化设置。

| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| user_id | UUID | 用户ID | 主键，外键 |
| theme | VARCHAR(20) | 主题偏好 | 默认 'system' |
| default_category_id | UUID | 默认分类 | 外键，可为空 |
| items_per_page | INTEGER | 每页显示数量 | 默认 12，正数 |
| show_usage_count | BOOLEAN | 显示使用次数 | 默认 true |
| auto_copy_feedback | BOOLEAN | 自动复制反馈 | 默认 true |
| created_at | TIMESTAMPTZ | 创建时间 | 默认 NOW() |
| updated_at | TIMESTAMPTZ | 更新时间 | 自动更新 |

## 索引设计

### 性能优化索引
- `idx_prompts_user_id`: 用户查询优化
- `idx_prompts_category_id`: 分类筛选优化
- `idx_prompts_created_at`: 时间排序优化
- `idx_prompts_usage_count`: 热门排序优化

### 全文搜索索引
- `idx_prompts_title_search`: 标题搜索
- `idx_prompts_content_search`: 内容搜索
- `idx_prompts_description_search`: 描述搜索

## 安全策略

### 行级安全 (RLS)
所有表都启用了RLS，确保用户只能访问自己的数据：
- 基于 `auth.uid()` 的访问控制
- 自动过滤用户数据
- 防止数据泄露

### 数据完整性
- 外键约束确保数据一致性
- 检查约束防止无效数据
- 唯一约束防止重复数据

## 部署说明

### 1. 创建表结构
```sql
-- 在Supabase SQL编辑器中执行
\i schema.sql
```

### 2. 初始化数据
```sql
-- 执行种子数据
\i seed.sql
```

### 3. 用户首次登录
应用需要在用户首次登录时调用 `create_default_categories_for_user()` 函数。

## 扩展计划
- 支持提示词收藏功能
- 支持提示词分享功能
- 支持团队协作功能
- 支持提示词版本管理
