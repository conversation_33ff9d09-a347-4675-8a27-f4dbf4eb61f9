"use client"

import { DashboardHeader } from '@/components/dashboard-header'
import { StatsDashboard } from '@/components/stats-dashboard'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'

export default function StatsPage() {
  const handleExportData = () => {
    // TODO: 实现数据导出功能
    console.log('导出数据')
  }

  const handleRefreshStats = () => {
    // 刷新页面来重新加载统计数据
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 顶部导航 */}
      <DashboardHeader />
      
      <div className="max-w-7xl mx-auto p-6">
        {/* 页面头部 */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
              数据统计
            </h1>
            <p className="text-muted-foreground mt-2">
              查看您的提示词使用情况和统计数据
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshStats}
              className="flex-1 sm:flex-none"
            >
              <Icon name="refresh" className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">刷新数据</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              className="flex-1 sm:flex-none"
            >
              <Icon name="download" className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">导出数据</span>
            </Button>
          </div>
        </div>

        {/* 统计仪表板 */}
        <StatsDashboard />

        {/* 使用说明 */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <Icon name="info-circle" className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                统计说明
              </h3>
              <div className="text-sm text-blue-700 dark:text-blue-200 space-y-1">
                <p>• <strong>总使用次数</strong>：所有提示词的复制使用次数总和</p>
                <p>• <strong>热门提示词</strong>：按使用次数排序的前5个提示词</p>
                <p>• <strong>热门标签</strong>：按关联提示词数量排序的标签</p>
                <p>• <strong>热门搜索</strong>：搜索次数最多的关键词</p>
                <p>• <strong>最近创建</strong>：按创建时间排序的最新提示词</p>
              </div>
            </div>
          </div>
        </div>

        {/* 数据洞察 */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Icon name="lightbulb" className="h-5 w-5 text-green-600" />
              </div>
              <h3 className="font-medium">提升效率</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              通过查看热门提示词，了解哪些内容最受欢迎，优化您的提示词库。
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Icon name="bullseye" className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="font-medium">精准分类</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              根据使用统计调整分类结构，让常用的提示词更容易找到。
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Icon name="chart" className="h-5 w-5 text-purple-600" />
              </div>
              <h3 className="font-medium">数据驱动</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              基于搜索和使用数据，持续优化您的提示词管理策略。
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
